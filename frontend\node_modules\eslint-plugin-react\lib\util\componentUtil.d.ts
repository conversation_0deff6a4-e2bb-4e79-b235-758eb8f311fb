/**
 * @param {ASTNode} node
 * @param {Context} context
 * @returns {boolean}
 */
export function isES5Component(node: ASTNode, context: Context): boolean;
/**
 * @param {ASTNode} node
 * @param {Context} context
 * @returns {boolean}
 */
export function isES6Component(node: ASTNode, context: Context): boolean;
/**
 * Get the parent ES5 component node from the current scope
 * @param {Context} context
 * @param {ASTNode} node
 * @returns {ASTNode|null}
 */
export function getParentES5Component(context: Context, node: ASTNode): ASTNode | null;
/**
 * Get the parent ES6 component node from the current scope
 * @param {Context} context
 * @param {ASTNode} node
 * @returns {ASTNode | null}
 */
export function getParentES6Component(context: Context, node: ASTNode): ASTNode | null;
/**
 * Check if the node is explicitly declared as a descendant of a React Component
 * @param {any} node
 * @param {Context} context
 * @returns {boolean}
 */
export function isExplicitComponent(node: any, context: Context): boolean;
/**
 * Checks if a component extends React.PureComponent
 * @param {ASTNode} node
 * @param {Context} context
 * @returns {boolean}
 */
export function isPureComponent(node: ASTNode, context: Context): boolean;
/**
 * @param {ASTNode} node
 * @returns {boolean}
 */
export function isStateMemberExpression(node: ASTNode): boolean;
//# sourceMappingURL=componentUtil.d.ts.map