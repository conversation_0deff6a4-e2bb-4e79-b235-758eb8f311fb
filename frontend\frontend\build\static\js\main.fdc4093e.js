/*! For license information please see main.fdc4093e.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),a=n(43),o=n(950);function l(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(l(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),S=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),N=Symbol.for("react.lazy");Symbol.for("react.scope");var P=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var R=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var j=Symbol.iterator;function T(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=j&&e[j]||e["@@iterator"])?e:null}var O=Symbol.for("react.client.reference");function _(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===O?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case x:return"Suspense";case E:return"SuspenseList";case P:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case S:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:_(e.type)||"Memo";case N:t=e._payload,e=e._init;try{return _(e(t))}catch(n){}}return null}var L=Array.isArray,A=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},F=[],M=-1;function U(e){return{current:e}}function I(e){0>M||(e.current=F[M],F[M]=null,M--)}function B(e,t){M++,F[M]=e.current,e.current=t}var H=U(null),q=U(null),W=U(null),$=U(null);function V(e,t){switch(B(W,t),B(q,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(H),B(H,e)}function Q(){I(H),I(q),I(W)}function K(e){null!==e.memoizedState&&B($,e);var t=H.current,n=od(t,e.type);t!==n&&(B(q,e),B(H,n))}function J(e){q.current===e&&(I(H),I(q)),$.current===e&&(I($),Kd._currentValue=D)}var Y=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,G=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,oe=r.unstable_NormalPriority,le=r.unstable_LowPriority,ie=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof se&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,o=e.suspendedLanes,l=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~o)?a=ve(r):0!==(l&=i)?a=ve(l):n||0!==(n=i&~e)&&(a=ve(n)):0!==(i=r&~o)?a=ve(i):0!==l?a=ve(l):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&o)&&((o=a&-a)>=(n=t&-t)||32===o&&0!==(4194048&n))?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Se(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function xe(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ne(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Pe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Re(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function je(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Te(){var e=z.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var Oe=Math.random().toString(36).slice(2),_e="__reactFiber$"+Oe,Le="__reactProps$"+Oe,Ae="__reactContainer$"+Oe,ze="__reactEvents$"+Oe,De="__reactListeners$"+Oe,Fe="__reactHandles$"+Oe,Me="__reactResources$"+Oe,Ue="__reactMarker$"+Oe;function Ie(e){delete e[_e],delete e[Le],delete e[ze],delete e[De],delete e[Fe]}function Be(e){var t=e[_e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[_e]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[_e])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[_e]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(l(33))}function We(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Ue]=!0}var Ve=new Set,Qe={};function Ke(e,t){Je(e,t),Je(e+"Capture",t)}function Je(e,t){for(Qe[e]=t,e=0;e<t.length;e++)Ve.add(t[e])}var Ye,Xe,Ge=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,Y.call(et,a)||!Y.call(Ze,a)&&(Ge.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ye)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ye=t&&t[1]||"",Xe=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ye+e+Xe}var ot=!1;function lt(e,t){if(!e||ot)return"";ot=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(o){r=o}e.call(n.prototype)}}else{try{throw Error()}catch(l){r=l}(n=e())&&"function"===typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=r.DetermineComponentFrameRoot(),l=o[0],i=o[1];if(l&&i){var s=l.split("\n"),u=i.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{ot=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function it(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return lt(e.type,!1);case 11:return lt(e.type.render,!1);case 1:return lt(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=it(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function mt(e){return e.replace(ht,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function gt(e,t,n,r,a,o,l,i){e.name="",null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.type=l:e.removeAttribute("type"),null!=t?"number"===l?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=t?vt(e,l,ut(t)):null!=n?vt(e,l,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=o&&(e.defaultChecked=!!o),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+ut(i):e.removeAttribute("name")}function yt(e,t,n,r,a,o,l,i){if(null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.type=o),null!=t||null!=n){if(!("submit"!==o&&"reset"!==o||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.name=l)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function St(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(l(92));if(L(r)){if(1<r.length)throw Error(l(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var xt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||xt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(l(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Et(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&Et(e,o,t[o])}function Nt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function jt(e){return Rt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Tt=null;function Ot(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var _t=null,Lt=null;function At(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Le]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Le]||null;if(!a)throw Error(l(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var zt=!1;function Dt(e,t,n){if(zt)return e(t,n);zt=!0;try{return e(t)}finally{if(zt=!1,(null!==_t||null!==Lt)&&(Bu(),_t&&(t=_t,e=Lt,Lt=_t=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Ft(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Le]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Ut=!1;if(Mt)try{var It={};Object.defineProperty(It,"passive",{get:function(){Ut=!0}}),window.addEventListener("test",It,It),window.removeEventListener("test",It,It)}catch(Af){Ut=!1}var Bt=null,Ht=null,qt=null;function Wt(){if(qt)return qt;var e,t,n=Ht,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return qt=a.slice(e,1<t?1-t:void 0)}function $t(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Vt(){return!0}function Qt(){return!1}function Kt(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Vt:Qt,this.isPropagationStopped=Qt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Jt,Yt,Xt,Gt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Kt(Gt),en=f({},Gt,{view:0,detail:0}),tn=Kt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Jt=e.screenX-Xt.screenX,Yt=e.screenY-Xt.screenY):Yt=Jt=0,Xt=e),Jt)},movementY:function(e){return"movementY"in e?e.movementY:Yt}}),rn=Kt(nn),an=Kt(f({},nn,{dataTransfer:0})),on=Kt(f({},en,{relatedTarget:0})),ln=Kt(f({},Gt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Kt(f({},Gt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Kt(f({},Gt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function hn(){return pn}var mn=Kt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=$t(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?$t(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?$t(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Kt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Kt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),vn=Kt(f({},Gt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Kt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Kt(f({},Gt,{newState:0,oldState:0})),Sn=[9,13,27,32],kn=Mt&&"CompositionEvent"in window,xn=null;Mt&&"documentMode"in document&&(xn=document.documentMode);var En=Mt&&"TextEvent"in window&&!xn,Cn=Mt&&(!kn||xn&&8<xn&&11>=xn),Nn=String.fromCharCode(32),Pn=!1;function Rn(e,t){switch(e){case"keyup":return-1!==Sn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Tn=!1;var On={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!On[e.type]:"textarea"===t}function Ln(e,t,n,r){_t?Lt?Lt.push(r):Lt=[r]:_t=r,0<(t=Wc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,zn=null;function Dn(e){Dc(e,0)}function Fn(e){if(ft(qe(e)))return e}function Mn(e,t){if("change"===e)return t}var Un=!1;if(Mt){var In;if(Mt){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}In=Bn}else In=!1;Un=In&&(!document.documentMode||9<document.documentMode)}function qn(){An&&(An.detachEvent("onpropertychange",Wn),zn=An=null)}function Wn(e){if("value"===e.propertyName&&Fn(zn)){var t=[];Ln(t,zn,e,Ot(e)),Dt(Dn,t)}}function $n(e,t,n){"focusin"===e?(qn(),zn=n,(An=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&qn()}function Vn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Fn(zn)}function Qn(e,t){if("click"===e)return Fn(t)}function Kn(e,t){if("input"===e||"change"===e)return Fn(t)}var Jn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Yn(e,t){if(Jn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Y.call(t,a)||!Jn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Gn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,or=null,lr=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},or&&Yn(or,r)||(or=r,0<(r=Wc(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,Sr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){wr.set(e,t),Ke(t,[e])}Sr.push("scrollEnd");var xr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=xr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},xr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Cr=[],Nr=0,Pr=0;function Rr(){for(var e=Nr,t=Pr=Nr=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var a=Cr[t];Cr[t++]=null;var o=Cr[t];if(Cr[t++]=null,null!==r&&null!==a){var l=r.pending;null===l?a.next=a:(a.next=l.next,l.next=a),r.pending=a}0!==o&&_r(n,a,o)}}function jr(e,t,n,r){Cr[Nr++]=e,Cr[Nr++]=t,Cr[Nr++]=n,Cr[Nr++]=r,Pr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Tr(e,t,n,r){return jr(e,t,n,r),Lr(e)}function Or(e,t){return jr(e,null,null,t),Lr(e)}function _r(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,o=e.return;null!==o;)o.childLanes|=n,null!==(r=o.alternate)&&(r.childLanes|=n),22===o.tag&&(null===(e=o.stateNode)||1&e._visibility||(a=!0)),e=o,o=o.return;return 3===e.tag?(o=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=o.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),o):null}function Lr(e){if(50<_u)throw _u=0,Lu=null,Error(l(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={};function zr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new zr(e,t,n,r)}function Fr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ur(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ir(e,t,n,r,a,o){var i=0;if(r=e,"function"===typeof e)Fr(e)&&(i=1);else if("string"===typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case P:return(e=Dr(31,n,t,a)).elementType=P,e.lanes=o,e;case g:return Br(n.children,a,o,t);case y:i=8,a|=24;break;case v:return(e=Dr(12,n,t,2|a)).elementType=v,e.lanes=o,e;case x:return(e=Dr(13,n,t,a)).elementType=x,e.lanes=o,e;case E:return(e=Dr(19,n,t,a)).elementType=E,e.lanes=o,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case S:i=10;break e;case w:i=9;break e;case k:i=11;break e;case C:i=14;break e;case N:i=16,r=null;break e}i=29,n=Error(l(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Br(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function qr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],$r=0,Vr=null,Qr=0,Kr=[],Jr=0,Yr=null,Xr=1,Gr="";function Zr(e,t){Wr[$r++]=Qr,Wr[$r++]=Vr,Vr=e,Qr=t}function ea(e,t,n){Kr[Jr++]=Xr,Kr[Jr++]=Gr,Kr[Jr++]=Yr,Yr=e;var r=Xr;e=Gr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var o=32-pe(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Xr=1<<32-pe(t)+a|n<<a|r,Gr=o+e}else Xr=1<<o|n<<a|r,Gr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Vr;)Vr=Wr[--$r],Wr[$r]=null,Qr=Wr[--$r],Wr[$r]=null;for(;e===Yr;)Yr=Kr[--Jr],Kr[Jr]=null,Gr=Kr[--Jr],Kr[Jr]=null,Xr=Kr[--Jr],Kr[Jr]=null}var ra=null,aa=null,oa=!1,la=null,ia=!1,sa=Error(l(519));function ua(e){throw ma(Er(Error(l(418,"")),e)),sa}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[_e]=e,t[Le]=r,n){case"dialog":Fc("cancel",t),Fc("close",t);break;case"iframe":case"object":case"embed":Fc("load",t);break;case"video":case"audio":for(n=0;n<Ac.length;n++)Fc(Ac[n],t);break;case"source":Fc("error",t);break;case"img":case"image":case"link":Fc("error",t),Fc("load",t);break;case"details":Fc("toggle",t);break;case"input":Fc("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Fc("invalid",t);break;case"textarea":Fc("invalid",t),St(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Yc(t.textContent,n)?(null!=r.popover&&(Fc("beforetoggle",t),Fc("toggle",t)),null!=r.onScroll&&Fc("scroll",t),null!=r.onScrollEnd&&Fc("scrollend",t),null!=r.onClick&&(t.onclick=Xc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ia=!1);case 27:case 3:return void(ia=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!oa)return da(e),oa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||ld(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,pd(e.type)?(e=vd,vd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,oa=!1}function ha(){var e=la;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),la=null),e}function ma(e){null===la?la=[e]:la.push(e)}var ga=U(null),ya=null,va=null;function ba(e,t,n){B(ga,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ga.current,I(ga)}function Sa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var i=a.child;o=o.firstContext;e:for(;null!==o;){var s=o;o=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),Sa(o.return,n,e),r||(i=null);break e}o=s.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(l(341));i.lanes|=n,null!==(o=i.alternate)&&(o.lanes|=n),Sa(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function xa(e,t,n,r){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(0!==(524288&a.flags))o=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(l(387));if(null!==(i=i.memoizedProps)){var s=a.type;Jn(a.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(a===$.current){if(null===(i=a.alternate))throw Error(l(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Kd):e=[Kd])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function Ea(e){for(e=e.firstContext;null!==e;){if(!Jn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Na(e){return Ra(ya,e)}function Pa(e,t){return null===ya&&Ca(e),Ra(e,t)}function Ra(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(l(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var ja="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Ta=r.unstable_scheduleCallback,Oa=r.unstable_NormalPriority,_a={$$typeof:S,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function La(){return{controller:new ja,data:new Map,refCount:0}}function Aa(e){e.refCount--,0===e.refCount&&Ta(Oa,(function(){e.controller.abort()}))}var za=null,Da=0,Fa=0,Ma=null;function Ua(){if(0===--Da&&null!==za){null!==Ma&&(Ma.status="fulfilled");var e=za;za=null,Fa=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ia=A.S;A.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===za){var n=za=[];Da=0,Fa=jc(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Ua,Ua)}(0,t),null!==Ia&&Ia(e,t)};var Ba=U(null);function Ha(){var e=Ba.current;return null!==e?e:ru.pooledCache}function qa(e,t){B(Ba,null===t?Ba.current:t.pool)}function Wa(){var e=Ha();return null===e?null:{parent:_a._currentValue,pool:e}}var $a=Error(l(460)),Va=Error(l(474)),Qa=Error(l(542)),Ka={then:function(){}};function Ja(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ya(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ya,Ya),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ya,Ya);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(l(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e}throw Ga=t,$a}}var Ga=null;function Za(){if(null===Ga)throw Error(l(459));var e=Ga;return Ga=null,e}function eo(e){if(e===$a||e===Qa)throw Error(l(483))}var to=!1;function no(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ao(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Lr(e),_r(e,null,n),t}return jr(e,r,t,n),Lr(e)}function lo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}function io(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var so=!1;function uo(){if(so){if(null!==Ma)throw Ma}}function co(e,t,n,r){so=!1;var a=e.updateQueue;to=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===l?o=u:l.next=u,l=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(l=0,c=u=s=null,i=o;;){var p=-536870913&i.lane,h=p!==i.lane;if(h?(ou&p)===p:(r&p)===p){0!==p&&p===Fa&&(so=!0),null!==c&&(c=c.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var m=e,g=i;p=t;var y=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(y,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:to=!0}}null!==(p=i.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=h,s=d):c=c.next=h,l|=p;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(h=i).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===o&&(a.shared.lanes=0),pu|=l,e.lanes=l,e.memoizedState=d}}function fo(e,t){if("function"!==typeof e)throw Error(l(191,e));e.call(t)}function po(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fo(n[e],t)}var ho=U(null),mo=U(0);function go(e,t){B(mo,e=du),B(ho,t),du=e|t.baseLanes}function yo(){B(mo,du),B(ho,ho.current)}function vo(){du=mo.current,I(ho),I(mo)}var bo=0,wo=null,So=null,ko=null,xo=!1,Eo=!1,Co=!1,No=0,Po=0,Ro=null,jo=0;function To(){throw Error(l(321))}function Oo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Jn(e[n],t[n]))return!1;return!0}function _o(e,t,n,r,a,o){return bo=o,wo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?Vl:Ql,Co=!1,o=n(r,a),Co=!1,Eo&&(o=Ao(t,n,r,a)),Lo(e),o}function Lo(e){A.H=$l;var t=null!==So&&null!==So.next;if(bo=0,ko=So=wo=null,xo=!1,Po=0,Ro=null,t)throw Error(l(300));null===e||Pi||null!==(e=e.dependencies)&&Ea(e)&&(Pi=!0)}function Ao(e,t,n,r){wo=e;var a=0;do{if(Eo&&(Ro=null),Po=0,Eo=!1,25<=a)throw Error(l(301));if(a+=1,ko=So=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}A.H=Kl,o=t(n,r)}while(Eo);return o}function zo(){var e=A.H,t=e.useState()[0];return t="function"===typeof t.then?Bo(t):t,e=e.useState()[0],(null!==So?So.memoizedState:null)!==e&&(wo.flags|=1024),t}function Do(){var e=0!==No;return No=0,e}function Fo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Mo(e){if(xo){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}xo=!1}bo=0,ko=So=wo=null,Eo=!1,Po=No=0,Ro=null}function Uo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ko?wo.memoizedState=ko=e:ko=ko.next=e,ko}function Io(){if(null===So){var e=wo.alternate;e=null!==e?e.memoizedState:null}else e=So.next;var t=null===ko?wo.memoizedState:ko.next;if(null!==t)ko=t,So=e;else{if(null===e){if(null===wo.alternate)throw Error(l(467));throw Error(l(310))}e={memoizedState:(So=e).memoizedState,baseState:So.baseState,baseQueue:So.baseQueue,queue:So.queue,next:null},null===ko?wo.memoizedState=ko=e:ko=ko.next=e}return ko}function Bo(e){var t=Po;return Po+=1,null===Ro&&(Ro=[]),e=Xa(Ro,e,t),t=wo,null===(null===ko?t.memoizedState:ko.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?Vl:Ql),e}function Ho(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Bo(e);if(e.$$typeof===S)return Na(e)}throw Error(l(438,String(e)))}function qo(e){var t=null,n=wo.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wo.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=R;return t.index++,n}function Wo(e,t){return"function"===typeof t?t(e):t}function $o(e){return Vo(Io(),So,e)}function Vo(e,t,n){var r=e.queue;if(null===r)throw Error(l(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var s=i=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(ou&f)===f:(bo&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Fa&&(d=!0);else{if((bo&p)===p){c=c.next,p===Fa&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,i=o):u=u.next=f,wo.lanes|=p,pu|=p}f=c.action,Co&&n(o,f),o=c.hasEagerState?c.eagerState:n(o,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,i=o):u=u.next=p,wo.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=o:u.next=s,!Jn(o,e.memoizedState)&&(Pi=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=o,e.baseState=i,e.baseQueue=u,r.lastRenderedState=o}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Qo(e){var t=Io(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);Jn(o,t.memoizedState)||(Pi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ko(e,t,n){var r=wo,a=Io(),o=oa;if(o){if(void 0===n)throw Error(l(407));n=n()}else n=t();var i=!Jn((So||a).memoizedState,n);if(i&&(a.memoizedState=n,Pi=!0),a=a.queue,yl(2048,8,Xo.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==ko&&1&ko.memoizedState.tag){if(r.flags|=2048,hl(9,{destroy:void 0,resource:void 0},Yo.bind(null,r,a,n,t),null),null===ru)throw Error(l(349));o||0!==(124&bo)||Jo(r,t,n)}return n}function Jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wo.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Yo(e,t,n,r){t.value=n,t.getSnapshot=r,Go(t)&&Zo(e)}function Xo(e,t,n){return n((function(){Go(t)&&Zo(e)}))}function Go(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Jn(e,n)}catch(r){return!0}}function Zo(e){var t=Or(e,2);null!==t&&Du(t,e,2)}function el(e){var t=Uo();if("function"===typeof e){var n=e;if(e=n(),Co){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:e},t}function tl(e,t,n,r){return e.baseState=n,Vo(e,So,"function"===typeof r?r:Wo)}function nl(e,t,n,r,a){if(Hl(e))throw Error(l(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==A.T?n(!0):o.isTransition=!1,r(o),null===(n=t.pending)?(o.next=t.pending=o,rl(t,o)):(o.next=n.next,t.pending=n.next=o)}}function rl(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var o=A.T,l={};A.T=l;try{var i=n(a,r),s=A.S;null!==s&&s(l,i),al(e,t,i)}catch(u){ll(e,t,u)}finally{A.T=o}}else try{al(e,t,o=n(a,r))}catch(c){ll(e,t,c)}}function al(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then((function(n){ol(e,t,n)}),(function(n){return ll(e,t,n)})):ol(e,t,n)}function ol(e,t,n){t.status="fulfilled",t.value=n,il(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,rl(e,n)))}function ll(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,il(t),t=t.next}while(t!==r)}e.action=null}function il(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function sl(e,t){return t}function ul(e,t){if(oa){var n=ru.formState;if(null!==n){e:{var r=wo;if(oa){if(aa){t:{for(var a=aa,o=ia;8!==a.nodeType;){if(!o){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(o=a.data)||"F"===o?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Uo()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:t},n.queue=r,n=Ul.bind(null,wo,r),r.dispatch=n,r=el(!1),o=Bl.bind(null,wo,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Uo()).queue=a,n=nl.bind(null,wo,a,o,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function cl(e){return dl(Io(),So,e)}function dl(e,t,n){if(t=Vo(e,t,sl)[0],e=$o(Wo)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Bo(t)}catch(l){if(l===$a)throw Qa;throw l}else r=t;var a=(t=Io()).queue,o=a.dispatch;return n!==t.memoizedState&&(wo.flags|=2048,hl(9,{destroy:void 0,resource:void 0},fl.bind(null,a,n),null)),[r,o,e]}function fl(e,t){e.action=t}function pl(e){var t=Io(),n=So;if(null!==n)return dl(t,n,e);Io(),t=t.memoizedState;var r=(n=Io()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function hl(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wo.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ml(){return Io().memoizedState}function gl(e,t,n,r){var a=Uo();r=void 0===r?null:r,wo.flags|=e,a.memoizedState=hl(1|t,{destroy:void 0,resource:void 0},n,r)}function yl(e,t,n,r){var a=Io();r=void 0===r?null:r;var o=a.memoizedState.inst;null!==So&&null!==r&&Oo(r,So.memoizedState.deps)?a.memoizedState=hl(t,o,n,r):(wo.flags|=e,a.memoizedState=hl(1|t,o,n,r))}function vl(e,t){gl(8390656,8,e,t)}function bl(e,t){yl(2048,8,e,t)}function wl(e,t){return yl(4,2,e,t)}function Sl(e,t){return yl(4,4,e,t)}function kl(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function xl(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,yl(4,4,kl.bind(null,t,e),n)}function El(){}function Cl(e,t){var n=Io();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Oo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Nl(e,t){var n=Io();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Oo(t,r[1]))return r[0];if(r=e(),Co){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Pl(e,t,n){return void 0===n||0!==(1073741824&bo)?e.memoizedState=t:(e.memoizedState=n,e=zu(),wo.lanes|=e,pu|=e,n)}function Rl(e,t,n,r){return Jn(n,t)?n:null!==ho.current?(e=Pl(e,n,r),Jn(e,t)||(Pi=!0),e):0===(42&bo)?(Pi=!0,e.memoizedState=n):(e=zu(),wo.lanes|=e,pu|=e,t)}function jl(e,t,n,r,a){var o=z.p;z.p=0!==o&&8>o?o:8;var l=A.T,i={};A.T=i,Bl(e,!1,t,n);try{var s=a(),u=A.S;if(null!==u&&u(i,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Il(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then((function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)}),(function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)})),r}(s,r),Au());else Il(e,t,r,Au())}catch(c){Il(e,t,{then:function(){},status:"rejected",reason:c},Au())}finally{z.p=o,A.T=l}}function Tl(){}function Ol(e,t,n,r){if(5!==e.tag)throw Error(l(476));var a=_l(e).queue;jl(e,a,t,D,null===n?Tl:function(){return Ll(e),n(r)})}function _l(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wo,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ll(e){Il(e,_l(e).next.queue,{},Au())}function Al(){return Na(Kd)}function zl(){return Io().memoizedState}function Dl(){return Io().memoizedState}function Fl(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Au(),r=oo(t,e=ao(n),n);return null!==r&&(Du(r,t,n),lo(r,t,n)),t={cache:La()},void(e.payload=t)}t=t.return}}function Ml(e,t,n){var r=Au();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hl(e)?ql(t,n):null!==(n=Tr(e,t,n,r))&&(Du(n,e,r),Wl(n,t,r))}function Ul(e,t,n){Il(e,t,n,Au())}function Il(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hl(e))ql(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,Jn(i,l))return jr(e,t,a,0),null===ru&&Rr(),!1}catch(s){}if(null!==(n=Tr(e,t,a,r)))return Du(n,e,r),Wl(n,t,r),!0}return!1}function Bl(e,t,n,r){if(r={lane:2,revertLane:jc(),action:r,hasEagerState:!1,eagerState:null,next:null},Hl(e)){if(t)throw Error(l(479))}else null!==(t=Tr(e,n,r,2))&&Du(t,e,2)}function Hl(e){var t=e.alternate;return e===wo||null!==t&&t===wo}function ql(e,t){Eo=xo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wl(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}var $l={readContext:Na,use:Ho,useCallback:To,useContext:To,useEffect:To,useImperativeHandle:To,useLayoutEffect:To,useInsertionEffect:To,useMemo:To,useReducer:To,useRef:To,useState:To,useDebugValue:To,useDeferredValue:To,useTransition:To,useSyncExternalStore:To,useId:To,useHostTransitionStatus:To,useFormState:To,useActionState:To,useOptimistic:To,useMemoCache:To,useCacheRefresh:To},Vl={readContext:Na,use:Ho,useCallback:function(e,t){return Uo().memoizedState=[e,void 0===t?null:t],e},useContext:Na,useEffect:vl,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gl(4194308,4,kl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return gl(4194308,4,e,t)},useInsertionEffect:function(e,t){gl(4,2,e,t)},useMemo:function(e,t){var n=Uo();t=void 0===t?null:t;var r=e();if(Co){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Uo();if(void 0!==n){var a=n(t);if(Co){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Ml.bind(null,wo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Uo().memoizedState=e},useState:function(e){var t=(e=el(e)).queue,n=Ul.bind(null,wo,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:El,useDeferredValue:function(e,t){return Pl(Uo(),e,t)},useTransition:function(){var e=el(!1);return e=jl.bind(null,wo,e.queue,!0,!1),Uo().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wo,a=Uo();if(oa){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===ru)throw Error(l(349));0!==(124&ou)||Jo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,vl(Xo.bind(null,r,o,e),[e]),r.flags|=2048,hl(9,{destroy:void 0,resource:void 0},Yo.bind(null,r,o,n,t),null),n},useId:function(){var e=Uo(),t=ru.identifierPrefix;if(oa){var n=Gr;t="\xab"+t+"R"+(n=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+n),0<(n=No++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=jo++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Al,useFormState:ul,useActionState:ul,useOptimistic:function(e){var t=Uo();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bl.bind(null,wo,!0,n),n.dispatch=t,[e,t]},useMemoCache:qo,useCacheRefresh:function(){return Uo().memoizedState=Fl.bind(null,wo)}},Ql={readContext:Na,use:Ho,useCallback:Cl,useContext:Na,useEffect:bl,useImperativeHandle:xl,useInsertionEffect:wl,useLayoutEffect:Sl,useMemo:Nl,useReducer:$o,useRef:ml,useState:function(){return $o(Wo)},useDebugValue:El,useDeferredValue:function(e,t){return Rl(Io(),So.memoizedState,e,t)},useTransition:function(){var e=$o(Wo)[0],t=Io().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Ko,useId:zl,useHostTransitionStatus:Al,useFormState:cl,useActionState:cl,useOptimistic:function(e,t){return tl(Io(),0,e,t)},useMemoCache:qo,useCacheRefresh:Dl},Kl={readContext:Na,use:Ho,useCallback:Cl,useContext:Na,useEffect:bl,useImperativeHandle:xl,useInsertionEffect:wl,useLayoutEffect:Sl,useMemo:Nl,useReducer:Qo,useRef:ml,useState:function(){return Qo(Wo)},useDebugValue:El,useDeferredValue:function(e,t){var n=Io();return null===So?Pl(n,e,t):Rl(n,So.memoizedState,e,t)},useTransition:function(){var e=Qo(Wo)[0],t=Io().memoizedState;return["boolean"===typeof e?e:Bo(e),t]},useSyncExternalStore:Ko,useId:zl,useHostTransitionStatus:Al,useFormState:pl,useActionState:pl,useOptimistic:function(e,t){var n=Io();return null!==So?tl(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:qo,useCacheRefresh:Dl},Jl=null,Yl=0;function Xl(e){var t=Yl;return Yl+=1,null===Jl&&(Jl=[]),Xa(Jl,e,t)}function Gl(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zl(e,t){if(t.$$typeof===p)throw Error(l(525));throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ei(e){return(0,e._init)(e._payload)}function ti(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===N&&ei(o)===t.type)?(Gl(t=a(t,n.props),n),t.return=e,t):(Gl(t=Ir(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Br(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return Gl(n=Ir(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=qr(t,e.mode,n)).return=e,t;case N:return f(e,t=(0,t._init)(t._payload),n)}if(L(t)||T(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Xl(t),n);if(t.$$typeof===S)return f(e,Pa(e,t),n);Zl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===a?u(e,t,n,r):null;case m:return n.key===a?c(e,t,n,r):null;case N:return p(e,t,n=(a=n._init)(n._payload),r)}if(L(n)||T(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Xl(n),r);if(n.$$typeof===S)return p(e,t,Pa(e,n),r);Zl(e,n)}return null}function y(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case N:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(L(r)||T(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return y(e,t,n,Xl(r),a);if(r.$$typeof===S)return y(e,t,n,Pa(t,r),a);Zl(t,r)}return null}function v(s,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case h:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===g){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===N&&ei(b)===u.type){n(s,u.sibling),Gl(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===g?((d=Br(c.props.children,s.mode,d,c.key)).return=s,s=d):(Gl(d=Ir(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return i(s);case m:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=qr(c,s.mode,d)).return=s,s=d}return i(s);case N:return v(s,u,c=(b=c._init)(c._payload),d)}if(L(c))return function(a,l,i,s){for(var u=null,c=null,d=l,h=l=0,m=null;null!==d&&h<i.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(a,d,i[h],s);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(a,d),l=o(g,l,h),null===c?u=g:c.sibling=g,c=g,d=m}if(h===i.length)return n(a,d),oa&&Zr(a,h),u;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],s))&&(l=o(d,l,h),null===c?u=d:c.sibling=d,c=d);return oa&&Zr(a,h),u}for(d=r(d);h<i.length;h++)null!==(m=y(d,a,h,i[h],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),l=o(m,l,h),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(a,e)})),oa&&Zr(a,h),u}(s,u,c,d);if(T(c)){if("function"!==typeof(b=T(c)))throw Error(l(150));return function(a,i,s,u){if(null==s)throw Error(l(151));for(var c=null,d=null,h=i,m=i=0,g=null,v=s.next();null!==h&&!v.done;m++,v=s.next()){h.index>m?(g=h,h=null):g=h.sibling;var b=p(a,h,v.value,u);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&t(a,h),i=o(b,i,m),null===d?c=b:d.sibling=b,d=b,h=g}if(v.done)return n(a,h),oa&&Zr(a,m),c;if(null===h){for(;!v.done;m++,v=s.next())null!==(v=f(a,v.value,u))&&(i=o(v,i,m),null===d?c=v:d.sibling=v,d=v);return oa&&Zr(a,m),c}for(h=r(h);!v.done;m++,v=s.next())null!==(v=y(h,a,m,v.value,u))&&(e&&null!==v.alternate&&h.delete(null===v.key?m:v.key),i=o(v,i,m),null===d?c=v:d.sibling=v,d=v);return e&&h.forEach((function(e){return t(a,e)})),oa&&Zr(a,m),c}(s,u,c=b.call(c),d)}if("function"===typeof c.then)return v(s,u,Xl(c),d);if(c.$$typeof===S)return v(s,u,Pa(s,c),d);Zl(s,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Hr(c,s.mode,d)).return=s,s=d),i(s)):n(s,u)}return function(e,t,n,r){try{Yl=0;var a=v(e,t,n,r);return Jl=null,a}catch(l){if(l===$a||l===Qa)throw l;var o=Dr(29,l,null,e.mode);return o.lanes=r,o.return=e,o}}}var ni=ti(!0),ri=ti(!1),ai=U(null),oi=null;function li(e){var t=e.alternate;B(ci,1&ci.current),B(ai,e),null===oi&&(null===t||null!==ho.current||null!==t.memoizedState)&&(oi=e)}function ii(e){if(22===e.tag){if(B(ci,ci.current),B(ai,e),null===oi){var t=e.alternate;null!==t&&null!==t.memoizedState&&(oi=e)}}else si()}function si(){B(ci,ci.current),B(ai,ai.current)}function ui(e){I(ai),oi===e&&(oi=null),I(ci)}var ci=U(0);function di(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Au(),a=ao(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Du(t,e,r),lo(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Au(),a=ao(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Du(t,e,r),lo(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Au(),r=ao(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=oo(e,r,n))&&(Du(t,e,n),lo(t,e,n))}};function hi(e,t,n,r,a,o,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!t.prototype||!t.prototype.isPureReactComponent||(!Yn(n,r)||!Yn(a,o))}function mi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pi.enqueueReplaceState(t,t.state,null)}function gi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yi="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vi(e){yi(e)}function bi(e){console.error(e)}function wi(e){yi(e)}function Si(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function ki(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function xi(e,t,n){return(n=ao(n)).tag=3,n.payload={element:null},n.callback=function(){Si(e,t)},n}function Ei(e){return(e=ao(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var o=r.value;e.payload=function(){return a(o)},e.callback=function(){ki(t,n,r)}}var l=n.stateNode;null!==l&&"function"===typeof l.componentDidCatch&&(e.callback=function(){ki(t,n,r),"function"!==typeof a&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ni=Error(l(461)),Pi=!1;function Ri(e,t,n,r){t.child=null===e?ri(t,null,n,r):ni(t,e.child,n,r)}function ji(e,t,n,r,a){n=n.render;var o=t.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return Ca(t),r=_o(e,t,n,l,o,a),i=Do(),null===e||Pi?(oa&&i&&ta(t),t.flags|=1,Ri(e,t,r,a),t.child):(Fo(e,t,a),Ji(e,t,a))}function Ti(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Fr(o)||void 0!==o.defaultProps||null!==n.compare?((e=Ir(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Oi(e,t,o,r,a))}if(o=e.child,!Yi(e,a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:Yn)(l,r)&&e.ref===t.ref)return Ji(e,t,a)}return t.flags|=1,(e=Mr(o,r)).ref=t.ref,e.return=t,t.child=e}function Oi(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(Yn(o,r)&&e.ref===t.ref){if(Pi=!1,t.pendingProps=r=o,!Yi(e,a))return t.lanes=e.lanes,Ji(e,t,a);0!==(131072&e.flags)&&(Pi=!0)}}return zi(e,t,n,r,a)}function _i(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==o?o.baseLanes|n:n,null!==e){for(a=t.child=e.child,o=0;null!==a;)o=o|a.lanes|a.childLanes,a=a.sibling;t.childLanes=o&~r}else t.childLanes=0,t.child=null;return Li(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Li(e,t,null!==o?o.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qa(0,null!==o?o.cachePool:null),null!==o?go(t,o):yo(),ii(t)}else null!==o?(qa(0,o.cachePool),go(t,o),si(),t.memoizedState=null):(null!==e&&qa(0,null),yo(),si());return Ri(e,t,a,n),t.child}function Li(e,t,n,r){var a=Ha();return a=null===a?null:{parent:_a._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&qa(0,null),yo(),ii(t),null!==e&&xa(e,t,r,!0),null}function Ai(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(l(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function zi(e,t,n,r,a){return Ca(t),n=_o(e,t,n,r,void 0,a),r=Do(),null===e||Pi?(oa&&r&&ta(t),t.flags|=1,Ri(e,t,n,a),t.child):(Fo(e,t,a),Ji(e,t,a))}function Di(e,t,n,r,a,o){return Ca(t),t.updateQueue=null,n=Ao(t,r,n,a),Lo(e),r=Do(),null===e||Pi?(oa&&r&&ta(t),t.flags|=1,Ri(e,t,n,o),t.child):(Fo(e,t,o),Ji(e,t,o))}function Fi(e,t,n,r,a){if(Ca(t),null===t.stateNode){var o=Ar,l=n.contextType;"object"===typeof l&&null!==l&&(o=Na(l)),o=new n(r,o),t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,o.updater=pi,t.stateNode=o,o._reactInternals=t,(o=t.stateNode).props=r,o.state=t.memoizedState,o.refs={},no(t),l=n.contextType,o.context="object"===typeof l&&null!==l?Na(l):Ar,o.state=t.memoizedState,"function"===typeof(l=n.getDerivedStateFromProps)&&(fi(t,n,l,r),o.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(l=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),l!==o.state&&pi.enqueueReplaceState(o,o.state,null),co(t,r,o,a),uo(),o.state=t.memoizedState),"function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){o=t.stateNode;var i=t.memoizedProps,s=gi(n,i);o.props=s;var u=o.context,c=n.contextType;l=Ar,"object"===typeof c&&null!==c&&(l=Na(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof o.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i||u!==l)&&mi(t,o,r,l),to=!1;var f=t.memoizedState;o.state=f,co(t,r,o,a),uo(),u=t.memoizedState,i||f!==u||to?("function"===typeof d&&(fi(t,n,d,r),u=t.memoizedState),(s=to||hi(t,n,s,r,f,u,l))?(c||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=l,r=s):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ro(e,t),c=gi(n,l=t.memoizedProps),o.props=c,d=t.pendingProps,f=o.context,u=n.contextType,s=Ar,"object"===typeof u&&null!==u&&(s=Na(u)),(u="function"===typeof(i=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(l!==d||f!==s)&&mi(t,o,r,s),to=!1,f=t.memoizedState,o.state=f,co(t,r,o,a),uo();var p=t.memoizedState;l!==d||f!==p||to||null!==e&&null!==e.dependencies&&Ea(e.dependencies)?("function"===typeof i&&(fi(t,n,i,r),p=t.memoizedState),(c=to||hi(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&Ea(e.dependencies))?(u||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=s,r=c):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return o=r,Ai(e,t),r=0!==(128&t.flags),o||r?(o=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:o.render(),t.flags|=1,null!==e&&r?(t.child=ni(t,e.child,null,a),t.child=ni(t,null,n,a)):Ri(e,t,n,a),t.memoizedState=o.state,e=t.child):e=Ji(e,t,a),e}function Mi(e,t,n,r){return pa(),t.flags|=256,Ri(e,t,n,r),t.child}var Ui={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ii(e){return{baseLanes:e,cachePool:Wa()}}function Bi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gu),e}function Hi(e,t,n){var r,a=t.pendingProps,o=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ci.current)),r&&(o=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(oa){if(o?li(t):si(),oa){var s,u=aa;if(s=u){e:{for(s=u,u=ia;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Yr?{id:Xr,overflow:Gr}:null,retryLane:536870912,hydrationErrors:null},(s=Dr(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?t.lanes=32:t.lanes=536870912,null;ui(t)}return u=a.children,a=a.fallback,o?(si(),u=Wi({mode:"hidden",children:u},o=t.mode),a=Br(a,o,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(o=t.child).memoizedState=Ii(n),o.childLanes=Bi(e,r,n),t.memoizedState=Ui,a):(li(t),qi(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(i)256&t.flags?(li(t),t.flags&=-257,t=$i(e,t,n)):null!==t.memoizedState?(si(),t.child=e.child,t.flags|=128,t=null):(si(),o=a.fallback,u=t.mode,a=Wi({mode:"visible",children:a.children},u),(o=Br(o,u,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,ni(t,e.child,null,n),(a=t.child).memoizedState=Ii(n),a.childLanes=Bi(e,r,n),t.memoizedState=Ui,t=o);else if(li(t),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(l(419))).stack="",a.digest=r,ma({value:a,source:null,stack:null}),t=$i(e,t,n)}else if(Pi||xa(e,t,n,!1),r=0!==(n&e.childLanes),Pi||r){if(null!==(r=ru)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Re(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,Or(e,a),Du(r,e,a),Ni;"$?"===u.data||Qu(),t=$i(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=yd(u.nextSibling),ra=t,oa=!0,la=null,ia=!1,null!==e&&(Kr[Jr++]=Xr,Kr[Jr++]=Gr,Kr[Jr++]=Yr,Xr=e.id,Gr=e.overflow,Yr=t),(t=qi(t,a.children)).flags|=4096);return t}return o?(si(),o=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Mr(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?o=Mr(c,o):(o=Br(o,u,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(u=e.child.memoizedState)?u=Ii(n):(null!==(s=u.cachePool)?(c=_a._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Wa(),u={baseLanes:u.baseLanes|n,cachePool:s}),o.memoizedState=u,o.childLanes=Bi(e,r,n),t.memoizedState=Ui,a):(li(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function qi(e,t){return(t=Wi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Wi(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $i(e,t,n){return ni(t,e.child,null,n),(e=qi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Sa(e.return,t,n)}function Qi(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Ki(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Ri(e,t,r.children,n),0!==(2&(r=ci.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vi(e,n,t);else if(19===e.tag)Vi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(ci,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===di(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qi(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===di(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qi(t,!0,n,null,o);break;case"together":Qi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ji(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(xa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Yi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ea(e))}function Xi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Pi=!0;else{if(!Yi(e,n)&&0===(128&t.flags))return Pi=!1,function(e,t,n){switch(t.tag){case 3:V(t,t.stateNode.containerInfo),ba(0,_a,e.memoizedState.cache),pa();break;case 27:case 5:K(t);break;case 4:V(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(li(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Hi(e,t,n):(li(t),null!==(e=Ji(e,t,n))?e.sibling:null);li(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(xa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Ki(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(ci,ci.current),r)break;return null;case 22:case 23:return t.lanes=0,_i(e,t,n);case 24:ba(0,_a,e.memoizedState.cache)}return Ji(e,t,n)}(e,t,n);Pi=0!==(131072&e.flags)}else Pi=!1,oa&&0!==(1048576&t.flags)&&ea(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===k){t.tag=11,t=ji(null,t,r,e,n);break e}if(a===C){t.tag=14,t=Ti(null,t,r,e,n);break e}}throw t=_(r)||r,Error(l(306,t,""))}Fr(r)?(e=gi(r,e),t.tag=1,t=Fi(null,t,r,e,n)):(t.tag=0,t=zi(null,t,r,e,n))}return t;case 0:return zi(e,t,t.type,t.pendingProps,n);case 1:return Fi(e,t,r=t.type,a=gi(r,t.pendingProps),n);case 3:e:{if(V(t,t.stateNode.containerInfo),null===e)throw Error(l(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,ro(e,t),co(t,r,null,n);var i=t.memoizedState;if(r=i.cache,ba(0,_a,r),r!==o.cache&&ka(t,[_a],n,!0),uo(),r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Mi(e,t,r,n);break e}if(r!==a){ma(a=Er(Error(l(424)),t)),t=Mi(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,oa=!0,la=null,ia=!0,n=ri(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Ji(e,t,n);break e}Ri(e,t,r,n)}t=t.child}return t;case 26:return Ai(e,t),null===e?(n=Rd(t.type,null,t.pendingProps,null))?t.memoizedState=n:oa||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[_e]=t,r[Le]=e,ed(r,n,e),$e(r),t.stateNode=r):t.memoizedState=Rd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&oa&&(r=t.stateNode=wd(t.type,t.pendingProps,W.current),ra=t,ia=!0,a=aa,pd(t.type)?(vd=a,aa=yd(r.firstChild)):aa=a),Ri(e,t,t.pendingProps.children,n),Ai(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&oa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ue])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(o=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(o!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((o=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var o=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===o)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,ia))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),ia=!1,a=!0):a=!1),a||ua(t)),K(t),a=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,r=o.children,ld(a,o)?r=null:null!==i&&ld(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=_o(e,t,zo,null,null,n),Kd._currentValue=a),Ai(e,t),Ri(e,t,r,n),t.child;case 6:return null===e&&oa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ia))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return Hi(e,t,n);case 4:return V(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ni(t,null,r,n):Ri(e,t,r,n),t.child;case 11:return ji(e,t,t.type,t.pendingProps,n);case 7:return Ri(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ri(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Ri(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ca(t),r=r(a=Na(a)),t.flags|=1,Ri(e,t,r,n),t.child;case 14:return Ti(e,t,t.type,t.pendingProps,n);case 15:return Oi(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Wi(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return _i(e,t,n);case 24:return Ca(t),r=Na(_a),null===e?(null===(a=Ha())&&(a=ru,o=La(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},no(t),ba(0,_a,a)):(0!==(e.lanes&n)&&(ro(e,t),co(t,null,null,n),uo()),a=e.memoizedState,o=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,_a,r)):(r=o.cache,ba(0,_a,r),r!==a.cache&&ka(t,[_a],n,!0))),Ri(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(l(156,t.tag))}function Gi(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=ai.current)&&((4194048&ou)===ou?null!==oi:(62914560&ou)!==ou&&0===(536870912&ou)||t!==oi))throw Ga=Ka,Va;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?xe():536870912,e.lanes|=t,yu|=t)}function ts(e,t){if(!oa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(_a),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Gi(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,ha())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Gi(t),null!==n?(ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Gi(t),ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Gi(t),ns(t),t.flags&=-16777217),null;case 27:J(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return ns(t),null}e=H.current,fa(t)?ca(t):(e=wd(a,r,n),t.stateNode=e,Gi(t))}return ns(t),null;case 5:if(J(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return ns(t),null}if(e=H.current,fa(t))ca(t);else{switch(a=rd(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[_e]=t,e[Le]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Gi(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(e=W.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[_e]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Yc(e.nodeValue,n)))||ua(t)}else(e=rd(e).createTextNode(r))[_e]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(l(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(l(317));a[_e]=t}else pa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),a=!1}else a=ha(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return Q(),null===e&&Ic(t.stateNode.containerInfo),ns(t),null;case 10:return wa(t.type),ns(t),null;case 19:if(I(ci),null===(a=t.memoizedState))return ns(t),null;if(r=0!==(128&t.flags),null===(o=a.rendering))if(r)ts(a,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=di(e))){for(t.flags|=128,ts(a,!1),e=o.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ur(n,e),n=n.sibling;return B(ci,1&ci.current|2),t.child}e=e.sibling}null!==a.tail&&te()>ku&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=di(o))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!oa)return ns(t),null}else 2*te()-a.renderingStartTime>ku&&536870912!==n&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=ci.current,B(ci,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return ui(t),vo(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&I(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(_a),ns(t),null;case 25:case 30:return null}throw Error(l(156,t.tag))}function as(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(_a),Q(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return J(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return I(ci),null;case 4:return Q(),null;case 10:return wa(t.type),null;case 22:case 23:return ui(t),vo(),null!==e&&I(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(_a),null;default:return null}}function os(e,t){switch(na(t),t.tag){case 3:wa(_a),Q();break;case 26:case 27:case 5:J(t);break;case 4:Q();break;case 13:ui(t);break;case 19:I(ci);break;case 10:wa(t.type);break;case 22:case 23:ui(t),vo(),null!==e&&I(Ba);break;case 24:wa(_a)}}function ls(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var o=n.create,l=n.inst;r=o(),l.destroy=r}n=n.next}while(n!==a)}}catch(i){cc(t,t.return,i)}}function is(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var o=a.next;r=o;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,a=t;var s=n,u=i;try{u()}catch(c){cc(a,s,c)}}}r=r.next}while(r!==o)}}catch(c){cc(t,t.return,c)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{po(t,n)}catch(r){cc(e,e.return,r)}}}function us(e,t,n){n.props=gi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){cc(e,t,a)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){cc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(o){cc(e,t,o)}else n.current=null}function fs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){cc(e,e.return,a)}}function ps(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,i=null,s=null,u=null,c=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(h)||Gc(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":o=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":i=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(l(137,t));break;default:h!==f&&Gc(e,t,p,h,r,f)}}return void gt(e,i,s,u,c,d,o,a);case"select":for(o in h=i=s=p=null,n)if(u=n[o],n.hasOwnProperty(o)&&null!=u)switch(o){case"value":break;case"multiple":h=u;default:r.hasOwnProperty(o)||Gc(e,t,o,null,r,u)}for(a in r)if(o=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=o||null!=u))switch(a){case"value":p=o;break;case"defaultValue":s=o;break;case"multiple":i=o;default:o!==u&&Gc(e,t,a,o,r,u)}return t=s,n=i,r=h,void(null!=p?bt(e,!!n,p,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Gc(e,t,s,null,r,a)}for(i in r)if(a=r[i],o=n[i],r.hasOwnProperty(i)&&(null!=a||null!=o))switch(i){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(l(91));break;default:a!==o&&Gc(e,t,i,a,r,o)}return void wt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Gc(e,t,m,null,r,p);for(u in r)if(p=r[u],h=n[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Gc(e,t,u,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Gc(e,t,g,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(l(137,t));break;default:Gc(e,t,c,p,r,h)}return;default:if(Nt(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Zc(e,t,d,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Gc(e,t,v,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Gc(e,t,f,p,r,h)}(r,e.type,n,t),r[Le]=t}catch(a){cc(e,e.return,a)}}function hs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ms(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xc));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function vs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[_e]=e,t[Le]=n}catch(o){cc(e,e.return,o)}}var bs=!1,ws=!1,Ss=!1,ks="function"===typeof WeakSet?WeakSet:Set,xs=null;function Es(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Fs(e,n),4&r&&ls(5,n);break;case 1:if(Fs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(l){cc(n,n.return,l)}else{var a=gi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){cc(n,n.return,i)}}64&r&&ss(n),512&r&&cs(n,n.return);break;case 3:if(Fs(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{po(e,t)}catch(l){cc(n,n.return,l)}}break;case 27:null===t&&4&r&&vs(n);case 26:case 5:Fs(e,n),null===t&&4&r&&fs(n),512&r&&cs(n,n.return);break;case 12:Fs(e,n);break;case 13:Fs(e,n),4&r&&Ts(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bs)){t=null!==t&&null!==t.memoizedState||ws,a=bs;var o=ws;bs=r,(ws=t)&&!o?Us(e,n,0!==(8772&n.subtreeFlags)):Fs(e,n),bs=a,ws=o}break;case 30:break;default:Fs(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ie(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ns=null,Ps=!1;function Rs(e,t,n){for(n=n.child;null!==n;)js(e,t,n),n=n.sibling}function js(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(o){}switch(n.tag){case 26:ws||ds(n,t),Rs(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ws||ds(n,t);var r=Ns,a=Ps;pd(n.type)&&(Ns=n.stateNode,Ps=!1),Rs(e,t,n),Sd(n.stateNode),Ns=r,Ps=a;break;case 5:ws||ds(n,t);case 6:if(r=Ns,a=Ps,Ns=null,Rs(e,t,n),Ps=a,null!==(Ns=r))if(Ps)try{(9===Ns.nodeType?Ns.body:"HTML"===Ns.nodeName?Ns.ownerDocument.body:Ns).removeChild(n.stateNode)}catch(l){cc(n,t,l)}else try{Ns.removeChild(n.stateNode)}catch(l){cc(n,t,l)}break;case 18:null!==Ns&&(Ps?(hd(9===(e=Ns).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Rf(e)):hd(Ns,n.stateNode));break;case 4:r=Ns,a=Ps,Ns=n.stateNode.containerInfo,Ps=!0,Rs(e,t,n),Ns=r,Ps=a;break;case 0:case 11:case 14:case 15:ws||is(2,n,t),ws||is(4,n,t),Rs(e,t,n);break;case 1:ws||(ds(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&us(n,t,r)),Rs(e,t,n);break;case 21:Rs(e,t,n);break;case 22:ws=(r=ws)||null!==n.memoizedState,Rs(e,t,n),ws=r;break;default:Rs(e,t,n)}}function Ts(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Rf(e)}catch(n){cc(t,t.return,n)}}function Os(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ks),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ks),t;default:throw Error(l(435,e.tag))}}(e);t.forEach((function(t){var r=mc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function _s(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){Ns=s.stateNode,Ps=!1;break e}break;case 5:Ns=s.stateNode,Ps=!1;break e;case 3:case 4:Ns=s.stateNode.containerInfo,Ps=!0;break e}s=s.return}if(null===Ns)throw Error(l(160));js(o,i,a),Ns=null,Ps=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)As(t,e),t=t.sibling}var Ls=null;function As(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:_s(t,e),zs(e),4&r&&(is(3,e,e.return),ls(3,e),is(5,e,e.return));break;case 1:_s(t,e),zs(e),512&r&&(ws||null===n||ds(n,n.return)),64&r&&bs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Ls;if(_s(t,e),zs(e),512&r&&(ws||null===n||ds(n,n.return)),4&r){var o=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Ue]||o[_e]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector("head > title"))),ed(o,r,n),o[_e]=e,$e(o),r=o;break e;case"link":var i=Ud("link","href",a).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((o=i[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;case"meta":if(i=Ud("meta","content",a).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((o=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;default:throw Error(l(468,r))}o[_e]=e,$e(o),r=o}e.stateNode=r}else Id(a,e.type,e.stateNode);else e.stateNode=Ad(a,r,e.memoizedProps);else o!==r?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===r?Id(a,e.type,e.stateNode):Ad(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,n.memoizedProps)}break;case 27:_s(t,e),zs(e),512&r&&(ws||null===n||ds(n,n.return)),null!==n&&4&r&&ps(e,e.memoizedProps,n.memoizedProps);break;case 5:if(_s(t,e),zs(e),512&r&&(ws||null===n||ds(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(h){cc(e,e.return,h)}}4&r&&null!=e.stateNode&&ps(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(Ss=!0);break;case 6:if(_s(t,e),zs(e),4&r){if(null===e.stateNode)throw Error(l(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){cc(e,e.return,h)}}break;case 3:if(Md=null,a=Ls,Ls=Ed(t.containerInfo),_s(t,e),Ls=a,zs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Rf(t.containerInfo)}catch(h){cc(e,e.return,h)}Ss&&(Ss=!1,Ds(e));break;case 4:r=Ls,Ls=Ed(e.stateNode.containerInfo),_s(t,e),zs(e),Ls=r;break;case 12:default:_s(t,e),zs(e);break;case 13:_s(t,e),zs(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(Su=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Os(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bs,d=ws;if(bs=c||a,ws=d||u,_s(t,e),ws=d,bs=c,zs(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||bs||ws||Ms(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,a)"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){cc(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(h){cc(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Os(e,n))));break;case 19:_s(t,e),zs(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Os(e,r)));case 30:case 21:}}function zs(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hs(r)){n=r;break}r=r.return}if(null==n)throw Error(l(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,ms(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(kt(o,""),n.flags&=-33),ys(e,ms(e),o);break;case 3:case 4:var i=n.stateNode.containerInfo;gs(e,ms(e),i);break;default:throw Error(l(161))}}catch(s){cc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ds(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ds(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Fs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Es(e,t.alternate,t),t=t.sibling}function Ms(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:is(4,t,t.return),Ms(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&us(t,t.return,n),Ms(t);break;case 27:Sd(t.stateNode);case 26:case 5:ds(t,t.return),Ms(t);break;case 22:null===t.memoizedState&&Ms(t);break;default:Ms(t)}e=e.sibling}}function Us(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,o=t,l=o.flags;switch(o.tag){case 0:case 11:case 15:Us(a,o,n),ls(4,o);break;case 1:if(Us(a,o,n),"function"===typeof(a=(r=o).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(a=(r=o).updateQueue)){var i=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)fo(s[a],i)}catch(u){cc(r,r.return,u)}}n&&64&l&&ss(o),cs(o,o.return);break;case 27:vs(o);case 26:case 5:Us(a,o,n),n&&null===r&&4&l&&fs(o),cs(o,o.return);break;case 12:Us(a,o,n);break;case 13:Us(a,o,n),n&&4&l&&Ts(a,o);break;case 22:null===o.memoizedState&&Us(a,o,n),cs(o,o.return);break;case 30:break;default:Us(a,o,n)}t=t.sibling}}function Is(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Aa(n))}function Bs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e))}function Hs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)qs(e,t,n,r),t=t.sibling}function qs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Hs(e,t,n,r),2048&a&&ls(9,t);break;case 1:case 13:default:Hs(e,t,n,r);break;case 3:Hs(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e)));break;case 12:if(2048&a){Hs(e,t,n,r),e=t.stateNode;try{var o=t.memoizedProps,l=o.id,i=o.onPostCommit;"function"===typeof i&&i(l,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){cc(t,t.return,s)}}else Hs(e,t,n,r);break;case 23:break;case 22:o=t.stateNode,l=t.alternate,null!==t.memoizedState?2&o._visibility?Hs(e,t,n,r):$s(e,t):2&o._visibility?Hs(e,t,n,r):(o._visibility|=2,Ws(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Is(l,t);break;case 24:Hs(e,t,n,r),2048&a&&Bs(t.alternate,t)}}function Ws(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var o=e,l=t,i=n,s=r,u=l.flags;switch(l.tag){case 0:case 11:case 15:Ws(o,l,i,s,a),ls(8,l);break;case 23:break;case 22:var c=l.stateNode;null!==l.memoizedState?2&c._visibility?Ws(o,l,i,s,a):$s(o,l):(c._visibility|=2,Ws(o,l,i,s,a)),a&&2048&u&&Is(l.alternate,l);break;case 24:Ws(o,l,i,s,a),a&&2048&u&&Bs(l.alternate,l);break;default:Ws(o,l,i,s,a)}t=t.sibling}}function $s(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:$s(n,r),2048&a&&Is(r.alternate,r);break;case 24:$s(n,r),2048&a&&Bs(r.alternate,r);break;default:$s(n,r)}t=t.sibling}}var Vs=8192;function Qs(e){if(e.subtreeFlags&Vs)for(e=e.child;null!==e;)Ks(e),e=e.sibling}function Ks(e){switch(e.tag){case 26:Qs(e),e.flags&Vs&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(l(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=jd(n.href),o=e.querySelector(Td(a));if(o)return null!==(e=o._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,void $e(o);o=e.ownerDocument||e,n=Od(n),(a=kd.get(a))&&Dd(n,a),$e(o=o.createElement("link"));var i=o;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ed(o,"link",n),t.instance=o}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ls,e.memoizedState,e.memoizedProps);break;case 5:default:Qs(e);break;case 3:case 4:var t=Ls;Ls=Ed(e.stateNode.containerInfo),Qs(e),Ls=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Vs,Vs=16777216,Qs(e),Vs=t):Qs(e))}}function Js(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ys(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];xs=r,Zs(r,e)}Js(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xs(e),e=e.sibling}function Xs(e){switch(e.tag){case 0:case 11:case 15:Ys(e),2048&e.flags&&is(9,e,e.return);break;case 3:case 12:default:Ys(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Gs(e)):Ys(e)}}function Gs(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];xs=r,Zs(r,e)}Js(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:is(8,t,t.return),Gs(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Gs(t));break;default:Gs(t)}e=e.sibling}}function Zs(e,t){for(;null!==xs;){var n=xs;switch(n.tag){case 0:case 11:case 15:is(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,xs=r;else e:for(n=e;null!==xs;){var a=(r=xs).sibling,o=r.return;if(Cs(r),r===n){xs=null;break e}if(null!==a){a.return=o,xs=a;break e}xs=o}}}var eu={getCacheForType:function(e){var t=Na(_a),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,au=null,ou=0,lu=0,iu=null,su=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,hu=0,mu=0,gu=0,yu=0,vu=null,bu=null,wu=!1,Su=0,ku=1/0,xu=null,Eu=null,Cu=0,Nu=null,Pu=null,Ru=0,ju=0,Tu=null,Ou=null,_u=0,Lu=null;function Au(){if(0!==(2&nu)&&0!==ou)return ou&-ou;if(null!==A.T){return 0!==Fa?Fa:jc()}return Te()}function zu(){0===gu&&(gu=0===(536870912&ou)||oa?ke():536870912);var e=ai.current;return null!==e&&(e.flags|=32),gu}function Du(e,t,n){(e!==ru||2!==lu&&9!==lu)&&null===e.cancelPendingCommit||(qu(e,0),Iu(e,ou,gu,!1)),Ce(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(hu|=n),4===fu&&Iu(e,ou,gu,!1)),kc(e))}function Fu(e,t,n){if(0!==(6&nu))throw Error(l(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nu;nu|=2;var r=$u(),a=Vu();ru!==e||ou!==t?(xu=null,ku=te()+500,qu(e,t)):uu=we(e,t);e:for(;;)try{if(0!==lu&&null!==au){t=au;var o=iu;t:switch(lu){case 1:lu=0,iu=null,Zu(e,t,o,1);break;case 2:case 9:if(Ja(o)){lu=0,iu=null,Gu(t);break}t=function(){2!==lu&&9!==lu||ru!==e||(lu=7),kc(e)},o.then(t,t);break e;case 3:lu=7;break e;case 4:lu=5;break e;case 7:Ja(o)?(lu=0,iu=null,Gu(t)):(lu=0,iu=null,Zu(e,t,o,7));break;case 5:var i=null;switch(au.tag){case 26:i=au.memoizedState;case 5:case 27:var s=au;if(!i||Bd(i)){lu=0,iu=null;var u=s.sibling;if(null!==u)au=u;else{var c=s.return;null!==c?(au=c,ec(c)):au=null}break t}}lu=0,iu=null,Zu(e,t,o,5);break;case 6:lu=0,iu=null,Zu(e,t,o,6);break;case 8:Hu(),fu=6;break e;default:throw Error(l(462))}}Yu();break}catch(d){Wu(e,d)}return va=ya=null,A.H=r,A.A=a,nu=n,null!==au?0:(ru=null,ou=0,Rr(),fu)}(e,t):Ku(e,t,!0),o=r;;){if(0===a){uu&&!r&&Iu(e,t,0,!1);break}if(n=e.current.alternate,!o||Uu(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;a=vu;var u=s.current.memoizedState.isDehydrated;if(u&&(qu(s,i).flags|=256),2!==(i=Ku(s,i,!1))){if(cu&&!u){s.errorRecoveryDisabledLanes|=o,hu|=o,a=4;break e}o=bu,bu=a,null!==o&&(null===bu?bu=o:bu.push.apply(bu,o))}a=i}if(o=!1,2!==a)continue}}if(1===a){qu(e,0),Iu(e,t,0,!0);break}e:{switch(r=e,o=a){case 0:case 1:throw Error(l(345));case 4:if((4194048&t)!==t)break;case 6:Iu(r,t,gu,!su);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(l(329))}if((62914560&t)===t&&10<(a=Su+300-te())){if(Iu(r,t,gu,!su),0!==be(r,0,!0))break e;r.timeoutHandle=sd(Mu.bind(null,r,n,bu,xu,wu,t,gu,hu,yu,su,o,2,-0,0),a)}else Mu(r,n,bu,xu,wu,t,gu,hu,yu,su,o,0,-0,0)}break}a=Ku(e,t,!1),o=!1}kc(e)}function Mu(e,t,n,r,a,o,i,s,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Hd={stylesheets:null,count:0,unsuspend:qd},Ks(t),null!==(f=function(){if(null===Hd)throw Error(l(475));var e=Hd;return e.stylesheets&&0===e.count&&Vd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Vd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,o,n,r,a,i,s,u,d,1,p,h)),void Iu(e,o,i,!c);nc(e,t,o,n,r,a,i,s,u)}function Uu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!Jn(o(),a))return!1}catch(l){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Iu(e,t,n,r){t&=~mu,t&=~hu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var o=31-pe(a),l=1<<o;r[o]=-1,a&=~l}0!==n&&Ne(e,n,t)}function Bu(){return 0!==(6&nu)||(xc(0,!1),!1)}function Hu(){if(null!==au){if(0===lu)var e=au.return;else va=ya=null,Mo(e=au),Jl=null,Yl=0,e=au;for(;null!==e;)os(e.alternate,e),e=e.return;au=null}}function qu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hu(),ru=e,au=n=Mr(e.current,null),ou=t,lu=0,iu=null,su=!1,uu=we(e,t),cu=!1,yu=gu=mu=hu=pu=fu=0,bu=vu=null,wu=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),o=1<<a;t|=e[a],r&=~o}return du=t,Rr(),n}function Wu(e,t){wo=null,A.H=$l,t===$a||t===Qa?(t=Za(),lu=3):t===Va?(t=Za(),lu=4):lu=t===Ni?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,iu=t,null===au&&(fu=1,Si(e,Er(t,e.current)))}function $u(){var e=A.H;return A.H=$l,null===e?$l:e}function Vu(){var e=A.A;return A.A=eu,e}function Qu(){fu=4,su||(4194048&ou)!==ou&&null!==ai.current||(uu=!0),0===(134217727&pu)&&0===(134217727&hu)||null===ru||Iu(ru,ou,gu,!1)}function Ku(e,t,n){var r=nu;nu|=2;var a=$u(),o=Vu();ru===e&&ou===t||(xu=null,qu(e,t)),t=!1;var l=fu;e:for(;;)try{if(0!==lu&&null!==au){var i=au,s=iu;switch(lu){case 8:Hu(),l=6;break e;case 3:case 2:case 9:case 6:null===ai.current&&(t=!0);var u=lu;if(lu=0,iu=null,Zu(e,i,s,u),n&&uu){l=0;break e}break;default:u=lu,lu=0,iu=null,Zu(e,i,s,u)}}Ju(),l=fu;break}catch(c){Wu(e,c)}return t&&e.shellSuspendCounter++,va=ya=null,nu=r,A.H=a,A.A=o,null===au&&(ru=null,ou=0,Rr()),l}function Ju(){for(;null!==au;)Xu(au)}function Yu(){for(;null!==au&&!Z();)Xu(au)}function Xu(e){var t=Xi(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Gu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Di(n,t,t.pendingProps,t.type,void 0,ou);break;case 11:t=Di(n,t,t.pendingProps,t.type.render,t.ref,ou);break;case 5:Mo(t);default:os(n,t),t=Xi(n,t=au=Ur(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Zu(e,t,n,r){va=ya=null,Mo(t),Jl=null,Yl=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&xa(t,n,a,!0),null!==(n=ai.current)){switch(n.tag){case 13:return null===oi?Qu():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,a)),!1;case 22:return n.flags|=65536,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,a)),!1}throw Error(l(435,n.tag))}return dc(e,r,a),Qu(),!1}if(oa)return null!==(t=ai.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ma(Er(e=Error(l(422),{cause:r}),n))):(r!==sa&&ma(Er(t=Error(l(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Er(r,n),io(e,a=xi(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var o=Error(l(520),{cause:r});if(o=Er(o,n),null===vu?vu=[o]:vu.push(o),4!==fu&&(fu=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,io(n,e=xi(n.stateNode,r,e)),!1;case 1:if(t=n.type,o=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==o&&"function"===typeof o.componentDidCatch&&(null===Eu||!Eu.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,Ci(a=Ei(a),e,n,r),io(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,ou))return fu=1,Si(e,Er(n,e.current)),void(au=null)}catch(o){if(null!==a)throw au=a,o;return fu=1,Si(e,Er(n,e.current)),void(au=null)}32768&t.flags?(oa||1===r?e=!0:uu||0!==(536870912&ou)?e=!1:(su=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ai.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,su);e=t.return;var n=rs(t.alternate,t,du);if(null!==n)return void(au=n);if(null!==(t=t.sibling))return void(au=t);au=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=as(e.alternate,e);if(null!==n)return n.flags&=32767,void(au=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(au=e);au=e=n}while(null!==e);fu=6,au=null}function nc(e,t,n,r,a,o,i,s,u){e.cancelPendingCommit=null;do{ic()}while(0!==Cu);if(0!==(6&nu))throw Error(l(327));if(null!==t){if(t===e.current)throw Error(l(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,a,o){var l=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=l&~n;0<n;){var c=31-pe(n),d=1<<c;i[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ne(e,r,0),0!==o&&0===a&&0!==e.tag&&(e.suspendedLanes|=o&~(l&~t))}(e,n,o|=Pr,i,s,u),e===ru&&(au=ru=null,ou=0),Pu=t,Nu=e,Ru=n,ju=o,Tu=a,Ou=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,X(oe,(function(){return sc(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=A.T,A.T=null,a=z.p,z.p=2,i=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(g){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,xs=t;null!==xs;)if(e=(t=xs).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,xs=e;else for(;null!==xs;){switch(o=(t=xs).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var m=gi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){cc(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(l(163))}if(null!==(e=t.sibling)){e.return=t.return,xs=e;break}xs=t.return}}(e,t)}finally{nu=i,z.p=a,A.T=r}}Cu=1,rc(),ac(),oc()}}function rc(){if(1===Cu){Cu=0;var e=Nu,t=Pu,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=A.T,A.T=null;var r=z.p;z.p=2;var a=nu;nu|=4;try{As(t,e);var o=nd,l=er(e.containerInfo),i=o.focusedElem,s=o.selectionRange;if(l!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==s&&tr(i)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=i.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(l=g,g=m,m=l);var y=Gn(i,m),v=Gn(i,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=i;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof i.focus&&i.focus(),i=0;i<d.length;i++){var w=d[i];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nu=a,z.p=r,A.T=n}}e.current=t,Cu=2}}function ac(){if(2===Cu){Cu=0;var e=Nu,t=Pu,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=A.T,A.T=null;var r=z.p;z.p=2;var a=nu;nu|=4;try{Es(e,t.alternate,t)}finally{nu=a,z.p=r,A.T=n}}Cu=3}}function oc(){if(4===Cu||3===Cu){Cu=0,ee();var e=Nu,t=Pu,n=Ru,r=Ou;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cu=5:(Cu=0,Pu=Nu=null,lc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Eu=null),je(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=A.T,a=z.p,z.p=2,A.T=null;try{for(var o=e.onRecoverableError,l=0;l<r.length;l++){var i=r[l];o(i.value,{componentStack:i.stack})}}finally{A.T=t,z.p=a}}0!==(3&Ru)&&ic(),kc(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Lu?_u++:(_u=0,Lu=e):_u=0,xc(0,!1)}}function lc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Aa(t)))}function ic(e){return rc(),ac(),oc(),sc()}function sc(){if(5!==Cu)return!1;var e=Nu,t=ju;ju=0;var n=je(Ru),r=A.T,a=z.p;try{z.p=32>n?32:n,A.T=null,n=Tu,Tu=null;var o=Nu,i=Ru;if(Cu=0,Pu=Nu=null,Ru=0,0!==(6&nu))throw Error(l(331));var s=nu;if(nu|=4,Xs(o.current),qs(o,o.current,i,n),nu=s,xc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,o)}catch(u){}return!0}finally{z.p=a,A.T=r,lc(e,t)}}function uc(e,t,n){t=Er(n,t),null!==(e=oo(e,t=xi(e.stateNode,t,2),2))&&(Ce(e,2),kc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=Er(n,e),null!==(r=oo(t,n=Ei(2),2))&&(Ci(n,r,t,e),Ce(r,2),kc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cu=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(ou&n)===n&&(4===fu||3===fu&&(62914560&ou)===ou&&300>te()-Su?0===(2&nu)&&qu(e,0):mu|=n,yu===ou&&(yu=0)),kc(e)}function pc(e,t){0===t&&(t=xe()),null!==(e=Or(e,t))&&(Ce(e,t),kc(e))}function hc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function mc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(l(314))}null!==r&&r.delete(t),pc(e,n)}var gc=null,yc=null,vc=!1,bc=!1,wc=!1,Sc=0;function kc(e){e!==yc&&null===e.next&&(null===yc?gc=yc=e:yc=yc.next=e),bc=!0,vc||(vc=!0,dd((function(){0!==(6&nu)?X(re,Ec):Cc()})))}function xc(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=gc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var o=0;else{var l=r.suspendedLanes,i=r.pingedLanes;o=(1<<31-pe(42|e)+1)-1,o=201326741&(o&=a&~(l&~i))?201326741&o|1:o?2|o:0}0!==o&&(n=!0,Rc(r,o))}else o=ou,0===(3&(o=be(r,r===ru?o:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,o)||(n=!0,Rc(r,o));r=r.next}}while(n);wc=!1}}function Ec(){Cc()}function Cc(){bc=vc=!1;var e=0;0!==Sc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==id&&(id=e,!0);return id=null,!1}()&&(e=Sc),Sc=0);for(var t=te(),n=null,r=gc;null!==r;){var a=r.next,o=Nc(r,t);0===o?(r.next=null,null===n?gc=a:n.next=a,null===a&&(yc=n)):(n=r,(0!==e||0!==(3&o))&&(bc=!0)),r=a}xc(e,!1)}function Nc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=-62914561&e.pendingLanes;0<o;){var l=31-pe(o),i=1<<l,s=a[l];-1===s?0!==(i&n)&&0===(i&r)||(a[l]=Se(i,t)):s<=t&&(e.expiredLanes|=i),o&=~i}if(n=ou,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===lu||9===lu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&G(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&G(r),je(n)){case 2:case 8:n=ae;break;case 32:default:n=oe;break;case 268435456:n=ie}return r=Pc.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&G(r),e.callbackPriority=2,e.callbackNode=null,2}function Pc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ic()&&e.callbackNode!==n)return null;var r=ou;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Fu(e,r,t),Nc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Pc.bind(null,e):null)}function Rc(e,t){if(ic())return null;Fu(e,t,!0)}function jc(){return 0===Sc&&(Sc=ke()),Sc}function Tc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:jt(""+e)}function Oc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var _c=0;_c<Sr.length;_c++){var Lc=Sr[_c];kr(Lc.toLowerCase(),"on"+(Lc[0].toUpperCase()+Lc.slice(1)))}kr(pr,"onAnimationEnd"),kr(hr,"onAnimationIteration"),kr(mr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(gr,"onTransitionRun"),kr(yr,"onTransitionStart"),kr(vr,"onTransitionCancel"),kr(br,"onTransitionEnd"),Je("onMouseEnter",["mouseout","mouseover"]),Je("onMouseLeave",["mouseout","mouseover"]),Je("onPointerEnter",["pointerout","pointerover"]),Je("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ac="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ac));function Dc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==o&&a.isPropagationStopped())break e;o=i,a.currentTarget=u;try{o(a)}catch(c){yi(c)}a.currentTarget=null,o=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,u=i.currentTarget,i=i.listener,s!==o&&a.isPropagationStopped())break e;o=i,a.currentTarget=u;try{o(a)}catch(c){yi(c)}a.currentTarget=null,o=s}}}}function Fc(e,t){var n=t[ze];void 0===n&&(n=t[ze]=new Set);var r=e+"__bubble";n.has(r)||(Bc(t,e,2,!1),n.add(r))}function Mc(e,t,n){var r=0;t&&(r|=4),Bc(n,e,r,t)}var Uc="_reactListening"+Math.random().toString(36).slice(2);function Ic(e){if(!e[Uc]){e[Uc]=!0,Ve.forEach((function(t){"selectionchange"!==t&&(zc.has(t)||Mc(t,!1,e),Mc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Uc]||(t[Uc]=!0,Mc("selectionchange",!1,t))}}function Bc(e,t,n,r){switch(cf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=of}n=a.bind(null,t,n,e),a=void 0,!Ut||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hc(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a)break;if(4===l)for(l=r.return;null!==l;){var u=l.tag;if((3===u||4===u)&&l.stateNode.containerInfo===a)return;l=l.return}for(;null!==i;){if(null===(l=Be(i)))return;if(5===(u=l.tag)||6===u||26===u||27===u){r=o=l;continue e}i=i.parentNode}}r=r.return}Dt((function(){var r=o,a=Ot(n),l=[];e:{var i=wr.get(e);if(void 0!==i){var u=Zt,c=e;switch(e){case"keypress":if(0===$t(n))break e;case"keydown":case"keyup":u=mn;break;case"focusin":c="focus",u=on;break;case"focusout":c="blur",u=on;break;case"beforeblur":case"afterblur":u=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case pr:case hr:case mr:u=ln;break;case br:u=vn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gn;break;case"toggle":case"beforetoggle":u=wn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==i?i+"Capture":null:i;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Ft(m,p))&&d.push(qc(m,g,h)),f)break;m=m.return}0<d.length&&(i=new u(i,c,null,n,a),l.push({event:i,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===Tt||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[Ae])&&(u||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?i:qe(u),h=null==c?i:qe(c),(i=new d(g,m+"leave",u,n,a)).target=f,i.relatedTarget=h,g=null,Be(a)===r&&((d=new d(p,m+"enter",c,n,a)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=$c(h))m++;for(h=0,g=p;g;g=$c(g))h++;for(;0<m-h;)d=$c(d),m--;for(;0<h-m;)p=$c(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=$c(d),p=$c(p)}d=null}else d=null;null!==u&&Vc(l,i,u,d,!1),null!==c&&null!==f&&Vc(l,f,c,d,!0)}if("select"===(u=(i=r?qe(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===u&&"file"===i.type)var y=Mn;else if(_n(i))if(Un)y=Kn;else{y=Vn;var v=$n}else!(u=i.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&Nt(r.elementType)&&(y=Mn):y=Qn;switch(y&&(y=y(e,r))?Ln(l,y,n,a):(v&&v(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&vt(i,"number",i.value)),v=r?qe(r):window,e){case"focusin":(_n(v)||"true"===v.contentEditable)&&(rr=v,ar=r,or=null);break;case"focusout":or=ar=rr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,ir(l,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(l,n,a)}var b;if(kn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Tn?Rn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Cn&&"ko"!==n.locale&&(Tn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Tn&&(b=Wt()):(Ht="value"in(Bt=a)?Bt.value:Bt.textContent,Tn=!0)),0<(v=Wc(r,w)).length&&(w=new un(w,e,null,n,a),l.push({event:w,listeners:v}),b?w.data=b:null!==(b=jn(n))&&(w.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return jn(t);case"keypress":return 32!==t.which?null:(Pn=!0,Nn);case"textInput":return(e=t.data)===Nn&&Pn?null:e;default:return null}}(e,n):function(e,t){if(Tn)return"compositionend"===e||!kn&&Rn(e,t)?(e=Wt(),qt=Ht=Bt=null,Tn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Wc(r,"onBeforeInput")).length&&(v=new un("onBeforeInput","beforeinput",null,n,a),l.push({event:v,listeners:w}),v.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var o=Tc((a[Le]||null).action),l=r.submitter;l&&null!==(t=(t=l[Le]||null)?Tc(t.formAction):l.getAttribute("formAction"))&&(o=t,l=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Sc){var e=l?Oc(a,l):new FormData(a);Ol(n,{pending:!0,data:e,method:a.method,action:o},null,e)}}else"function"===typeof o&&(i.preventDefault(),e=l?Oc(a,l):new FormData(a),Ol(n,{pending:!0,data:e,method:a.method,action:o},o,e))},currentTarget:a}]})}}(l,e,r,n,a)}Dc(l,t)}))}function qc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===o||(null!=(a=Ft(e,n))&&r.unshift(qc(e,a,o)),null!=(a=Ft(e,t))&&r.push(qc(e,a,o))),3===e.tag)return r;e=e.return}return[]}function $c(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Vc(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===u||(s=u,a?null!=(u=Ft(n,o))&&l.unshift(qc(n,u,s)):a||null!=(u=Ft(n,o))&&l.push(qc(n,u,s))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Qc=/\r\n?/g,Kc=/\u0000|\uFFFD/g;function Jc(e){return("string"===typeof e?e:""+e).replace(Qc,"\n").replace(Kc,"")}function Yc(e,t){return t=Jc(t),Jc(e)===t}function Xc(){}function Gc(e,t,n,r,a,o){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,o);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=jt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof o&&("formAction"===n?("input"!==t&&Gc(e,t,"name",a.name,a,null),Gc(e,t,"formEncType",a.formEncType,a,null),Gc(e,t,"formMethod",a.formMethod,a,null),Gc(e,t,"formTarget",a.formTarget,a,null)):(Gc(e,t,"encType",a.encType,a,null),Gc(e,t,"method",a.method,a,null),Gc(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=jt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"onScroll":null!=r&&Fc("scroll",e);break;case"onScrollEnd":null!=r&&Fc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=jt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Fc("beforetoggle",e),Fc("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Pt.get(n)||n,r)}}function Zc(e,t,n,r,a,o){switch(n){case"style":Ct(e,r,o);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=n}}break;case"children":"string"===typeof r?kt(e,r):("number"===typeof r||"bigint"===typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&Fc("scroll",e);break;case"onScrollEnd":null!=r&&Fc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(o=null!=(o=e[Le]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fc("error",e),Fc("load",e);var r,a=!1,o=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Gc(e,t,r,i,n,null)}}return o&&Gc(e,t,"srcSet",n.srcSet,n,null),void(a&&Gc(e,t,"src",n.src,n,null));case"input":Fc("invalid",e);var s=r=i=o=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(l(137,t));break;default:Gc(e,t,a,d,n,null)}}return yt(e,r,s,u,c,i,o,!1),void dt(e);case"select":for(o in Fc("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":a=s;default:Gc(e,t,o,s,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(i in Fc("invalid",e),r=o=a=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":a=s;break;case"defaultValue":o=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(l(91));break;default:Gc(e,t,i,s,n,null)}return St(e,a,o,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Gc(e,t,u,a,n,null);return;case"dialog":Fc("beforetoggle",e),Fc("toggle",e),Fc("cancel",e),Fc("close",e);break;case"iframe":case"object":Fc("load",e);break;case"video":case"audio":for(a=0;a<Ac.length;a++)Fc(Ac[a],e);break;case"image":Fc("error",e),Fc("load",e);break;case"details":Fc("toggle",e);break;case"embed":case"source":case"link":Fc("error",e),Fc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Gc(e,t,c,a,n,null)}return;default:if(Nt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zc(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&Gc(e,t,s,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function ld(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var id=null;var sd="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout((function(){throw e}))}function pd(e){return"head"===e}function hd(e,t){var n=t,r=0,a=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0<r&&8>r){n=r;var l=e.ownerDocument;if(1&n&&Sd(l.documentElement),2&n&&Sd(l.body),4&n)for(Sd(n=l.head),l=n.firstChild;l;){var i=l.nextSibling,s=l.nodeName;l[Ue]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===l.rel.toLowerCase()||n.removeChild(l),l=i}}if(0===a)return e.removeChild(o),void Rf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=o}while(n);Rf(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Ie(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(l(452));return e;case"head":if(!(e=t.head))throw Error(l(453));return e;case"body":if(!(e=t.body))throw Error(l(454));return e;default:throw Error(l(451))}}function Sd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ie(e)}var kd=new Map,xd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=z.d;z.d={f:function(){var e=Cd.f(),t=Bu();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Ll(t):Cd.r(e)},D:function(e){Cd.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Nd;if(r&&e&&t){var a='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+mt(n.imageSizes)+'"]')):a+='[href="'+mt(e)+'"]';var o=a;switch(t){case"style":o=jd(e);break;case"script":o=_d(e)}kd.has(o)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),kd.set(o,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Td(o))||"script"===t&&r.querySelector(Ld(o))||(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Nd;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',o=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=_d(e)}if(!kd.has(o)&&(e=f({rel:"modulepreload",href:e},t),kd.set(o,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ld(o)))return}ed(r=n.createElement("link"),"link",e),$e(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Nd;if(n&&e){var r=We(n).hoistableScripts,a=_d(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=f({src:e,async:!0},t),(t=kd.get(a))&&Fd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}},S:function(e,t,n){Cd.S(e,t,n);var r=Nd;if(r&&e){var a=We(r).hoistableStyles,o=jd(e);t=t||"default";var l=a.get(o);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(Td(o)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=kd.get(o))&&Dd(e,n);var s=l=r.createElement("link");$e(s),ed(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){i.loading|=1})),s.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,zd(l,t,r)}l={type:"stylesheet",instance:l,count:1,state:i},a.set(o,l)}}},M:function(e,t){Cd.M(e,t);var n=Nd;if(n&&e){var r=We(n).hoistableScripts,a=_d(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=kd.get(a))&&Fd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}}};var Nd="undefined"===typeof document?null:document;function Pd(e,t,n){var r=Nd;if(r&&"string"===typeof t&&t){var a=mt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),xd.has(a)||(xd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}}function Rd(e,t,n,r){var a,o,i,s,u=(u=W.current)?Ed(u):null;if(!u)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=jd(n.href),(r=(n=We(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=jd(n.href);var c=We(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Td(e)))&&!c._p&&(d.instance=c,d.state.loading=5),kd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},kd.set(e,n),c||(a=u,o=e,i=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?s.loading=1:(o=a.createElement("link"),s.preload=o,o.addEventListener("load",(function(){return s.loading|=1})),o.addEventListener("error",(function(){return s.loading|=2})),ed(o,"link",i),$e(o),a.head.appendChild(o))))),t&&null===r)throw Error(l(528,""));return d}if(t&&null!==r)throw Error(l(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=_d(n),(r=(n=We(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function jd(e){return'href="'+mt(e)+'"'}function Td(e){return'link[rel="stylesheet"]['+e+"]"}function Od(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function _d(e){return'[src="'+mt(e)+'"]'}function Ld(e){return"script[async]"+e}function Ad(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,$e(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return $e(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),zd(r,n.precedence,e),t.instance=r;case"stylesheet":a=jd(n.href);var o=e.querySelector(Td(a));if(o)return t.state.loading|=4,t.instance=o,$e(o),o;r=Od(n),(a=kd.get(a))&&Dd(r,a),$e(o=(e.ownerDocument||e).createElement("link"));var i=o;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ed(o,"link",r),t.state.loading|=4,zd(o,n.precedence,e),t.instance=o;case"script":return o=_d(n.src),(a=e.querySelector(Ld(o)))?(t.instance=a,$e(a),a):(r=n,(a=kd.get(o))&&Fd(r=f({},n),a),$e(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(l(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,zd(r,n.precedence,e));return t.instance}function zd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,o=a,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===t)o=i;else if(o!==a)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Md=null;function Ud(e,t,n){if(null===Md){var r=new Map,a=Md=new Map;a.set(n,r)}else(r=(a=Md).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var o=n[a];if(!(o[Ue]||o[_e]||"link"===e&&"stylesheet"===o.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==o.namespaceURI){var l=o.getAttribute(t)||"";l=e+l;var i=r.get(l);i?i.push(o):r.set(l,[o])}}return r}function Id(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function qd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)Vd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var $d=null;function Vd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,$d=new Map,t.forEach(Qd,e),$d=null,Wd.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=$d.get(e);if(n)var r=n.get(null);else{n=new Map,$d.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<a.length;o++){var l=a[o];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(n.set(l.dataset.precedence,l),r=l)}r&&n.set(null,r)}l=(a=t.instance).getAttribute("data-precedence"),(o=n.get(l)||r)===r&&n.set(null,a),n.set(l,a),this.count++,r=Wd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),o?o.parentNode.insertBefore(a,o.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Kd={$$typeof:S,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Jd(e,t,n,r,a,o,l,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=o,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Yd(e,t,n,r,a,o,l,i,s,u,c,d){return e=new Jd(e,t,n,l,i,s,u,d),t=1,!0===o&&(t|=24),o=Dr(3,null,null,t),e.current=o,o.stateNode=e,(t=La()).refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:r,isDehydrated:n,cache:t},no(o),e}function Xd(e){return e?e=Ar:Ar}function Gd(e,t,n,r,a,o){a=Xd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ao(t)).payload={element:n},null!==(o=void 0===o?null:o)&&(r.callback=o),null!==(n=oo(e,r,t))&&(Du(n,0,t),lo(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=Or(e,67108864);null!==t&&Du(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=A.T;A.T=null;var o=z.p;try{z.p=2,of(e,t,n,r)}finally{z.p=o,A.T=a}}function af(e,t,n,r){var a=A.T;A.T=null;var o=z.p;try{z.p=8,of(e,t,n,r)}finally{z.p=o,A.T=a}}function of(e,t,n,r){if(nf){var a=lf(r);if(null===a)Hc(e,t,r,sf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,a),!0;case"dragenter":return pf=wf(pf,e,t,n,r,a),!0;case"mouseover":return hf=wf(hf,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return mf.set(o,wf(mf.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,gf.set(o,wf(gf.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var o=He(a);if(null!==o)switch(o.tag){case 3:if((o=o.stateNode).current.memoizedState.isDehydrated){var l=ve(o.pendingLanes);if(0!==l){var i=o;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var s=1<<31-pe(l);i.entanglements[1]|=s,l&=~s}kc(o),0===(6&nu)&&(ku=te()+500,xc(0,!1))}}break;case 13:null!==(i=Or(o,2))&&Du(i,0,2),Bu(),ef(o,2)}if(null===(o=lf(r))&&Hc(e,t,r,sf,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Hc(e,t,r,null,n)}}function lf(e){return uf(e=Ot(e))}var sf=null;function uf(e){if(sf=null,null!==(e=Be(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case oe:case le:return 32;case ie:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function wf(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Sf(e){var t=Be(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=z.p;try{return z.p=e,t()}finally{z.p=n}}(e.priority,(function(){if(13===n.tag){var e=Au();e=Re(e);var t=Or(n,e);null!==t&&Du(t,0,e),ef(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=lf(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Tt=r,n.target.dispatchEvent(r),Tt=null,t.shift()}return!0}function xf(e,t,n){kf(e)&&n.delete(t)}function Ef(){df=!1,null!==ff&&kf(ff)&&(ff=null),null!==pf&&kf(pf)&&(pf=null),null!==hf&&kf(hf)&&(hf=null),mf.forEach(xf),gf.forEach(xf)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var Nf=null;function Pf(e){Nf!==e&&(Nf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){Nf===e&&(Nf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var o=He(n);null!==o&&(e.splice(t,3),t-=3,Ol(o,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Rf(e){function t(t){return Cf(t,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==hf&&Cf(hf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)Sf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],o=n[r+1],l=a[Le]||null;if("function"===typeof o)l||Pf(n);else if(l){var i=null;if(o&&o.hasAttribute("formAction")){if(a=o,l=o[Le]||null)i=l.formAction;else if(null!==uf(a))continue}else i=l.action;"function"===typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Pf(n)}}}function jf(e){this._internalRoot=e}function Tf(e){this._internalRoot=e}Tf.prototype.render=jf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Gd(t.current,Au(),e,t,null,null)},Tf.prototype.unmount=jf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Gd(e.current,2,null,e,null,null),Bu(),t[Ae]=null}},Tf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Te();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&Sf(e)}};var Of=a.version;if("19.1.0"!==Of)throw Error(l(527,Of,"19.1.0"));z.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return c(a),e;if(o===r)return c(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,u=a.child;u;){if(u===n){i=!0,n=a,r=o;break}if(u===r){i=!0,r=a,n=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===n){i=!0,n=o,r=a;break}if(u===r){i=!0,r=o,n=a;break}u=u.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var _f={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Lf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Lf.isDisabled&&Lf.supportsFiber)try{ce=Lf.inject(_f),de=Lf}catch(zf){}}t.createRoot=function(e,t){if(!i(e))throw Error(l(299));var n=!1,r="",a=vi,o=bi,s=wi;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Yd(e,1,!1,null,0,n,r,a,o,s,0,null),e[Ae]=t.current,Ic(e),new jf(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(l(299));var r=!1,a="",o=vi,s=bi,u=wi,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(o=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Yd(e,1,!0,t,0,r,a,o,s,u,0,c)).context=Xd(null),n=t.current,(a=ao(r=Re(r=Au()))).callback=null,oo(n,a,r),n=r,t.current.lanes=n,Ce(t,n),kc(t),e[Ae]=t.current,Ic(e),new Tf(t)},t.version="19.1.0"},43:(e,t,n)=>{e.exports=n(288)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var S=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function E(e,t,r,a,o,l){return r=l.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:l}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function R(){}function j(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return j((c=e._init)(e._payload),t,a,o,l)}}if(c)return l=l(e),c=""===o?"."+P(e,0):o,S(l)?(a="",null!=c&&(a=c.replace(N,"$&/")+"/"),j(l,t,a,"",(function(e){return e}))):null!=l&&(C(l)&&(s=l,u=a+(null==l.key||e&&e.key===l.key?"":(""+l.key).replace(N,"$&/")+"/")+c,l=E(s.type,u,void 0,0,0,s.props)),t.push(l)),1;c=0;var d,h=""===o?".":o+":";if(S(e))for(var m=0;m<e.length;m++)c+=j(o=e[m],t,a,i=h+P(o,m),l);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(o=e.next()).done;)c+=j(o=o.value,t,a,i=h+P(o,m++),l);else if("object"===i){if("function"===typeof e.then)return j(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(R,R):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,o,l);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function T(e,t,n){if(null==e)return e;var r=[],a=0;return j(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var _="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function L(){}t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(o in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!x.call(t,o)||"key"===o||"__self"===o||"__source"===o||"ref"===o&&void 0===t.ref||(r[o]=t[o]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var l=Array(o),i=0;i<o;i++)l[i]=arguments[i+2];r.children=l}return E(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},o=null;if(null!=t)for(r in void 0!==t.key&&(o=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var i=Array(l),s=0;s<l;s++)i[s]=arguments[s+2];a.children=i}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===a[r]&&(a[r]=l[r]);return E(e,o,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),a=k.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(L,_)}catch(o){_(o)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=k.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.1.0"},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,l=Object.prototype.toString,i=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function u(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var l={d:{f:o,r:function(){throw Error(a(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},i=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=l.p;try{if(s.T=null,l.p=2,e)return e()}finally{s.T=t,l.p=n,l.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,l.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&l.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,o="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?l.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):"script"===n&&l.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);l.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&l.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);l.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);l.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else l.d.m(e)},t.requestFormReset=function(e){l.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{var n=Symbol.for("react.transitional.element");function r(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var o in r={},t)"key"!==o&&(r[o]=t[o]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}Symbol.for("react.fragment"),t.jsx=r,t.jsxs=r},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function S(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function k(e){if(g=!1,S(e),!m)if(null!==r(u))m=!0,E||(E=!0,x());else{var t=r(c);null!==t&&_(k,t.startTime-e)}}var x,E=!1,C=-1,N=5,P=-1;function R(){return!!y||!(t.unstable_now()-P<N)}function j(){if(y=!1,E){var e=t.unstable_now();P=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(C),C=-1),h=!0;var o=p;try{t:{for(S(e),f=r(u);null!==f&&!(f.expirationTime>e&&R());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof i){f.callback=i,S(e),n=!0;break t}f===r(u)&&a(u),S(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&_(k,s.startTime-e),n=!1}}break e}finally{f=null,p=o,h=!1}n=void 0}}finally{n?x():E=!1}}}if("function"===typeof w)x=function(){w(j)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,O=T.port2;T.port1.onmessage=j,x=function(){O.postMessage(null)}}else x=function(){v(j,0)};function _(e,n){C=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?l+o:l:o=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(b(C),C=-1):g=!0,_(k,o-l))):(e.sortIndex=i,n(u,e),m||h||(m=!0,E||(E=!0,x()))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>jn,hasStandardBrowserEnv:()=>On,hasStandardBrowserWebWorkerEnv:()=>_n,navigator:()=>Tn,origin:()=>Ln});var a=n(43),o=n(391);function l(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function u(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(358);const f=["sri"],p=["page"],h=["page","matches"],m=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],g=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],y=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var v="popstate";function b(){return N((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return x("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:E(t)}),null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function w(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function S(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function k(e,t){return{usr:e.state,key:e.key,idx:t}}function x(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return d(d({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?C(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function E(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function C(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function N(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,i="POP",s=null,u=c();function c(){return(l.state||{idx:null}).idx}function f(){i="POP";let e=c(),t=null==e?null:e-u;u=e,s&&s({action:i,location:h.location,delta:t})}function p(e){return P(e)}null==u&&(u=0,l.replaceState(d(d({},l.state),{},{idx:u}),""));let h={get action(){return i},get location(){return e(a,l)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(v,f),s=e,()=>{a.removeEventListener(v,f),s=null}},createHref:e=>t(a,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i="PUSH";let r=x(h.location,e,t);n&&n(r,e),u=c()+1;let d=k(r,u),f=h.createHref(r);try{l.pushState(d,"",f)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;a.location.assign(f)}o&&s&&s({action:i,location:h.location,delta:1})},replace:function(e,t){i="REPLACE";let r=x(h.location,e,t);n&&n(r,e),u=c();let a=k(r,u),d=h.createHref(r);l.replaceState(a,"",d),o&&s&&s({action:i,location:h.location,delta:0})},go:e=>l.go(e)};return h}function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),w(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:E(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function R(e,t){return j(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function j(e,t,n,r){let a=W(("string"===typeof t?C(t):t).pathname||"/",n);if(null==a)return null;let o=T(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=q(a);l=I(o[i],e,r)}return l}function T(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(w(l.relativePath.startsWith(r),'Absolute route path "'.concat(l.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),l.relativePath=l.relativePath.slice(r.length));let i=J([r,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(w(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(i,'".')),T(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:U(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of O(e.path))a(e,t,r);else a(e,t)})),t}function O(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=O(r.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}var _=/^:[\w-]+$/,L=3,A=2,z=1,D=10,F=-2,M=e=>"*"===e;function U(e,t){let n=e.split("/"),r=n.length;return n.some(M)&&(r+=F),t&&(r+=A),n.filter((e=>!M(e))).reduce(((e,t)=>e+(_.test(t)?L:""===t?z:D)),r)}function I(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},o="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=B({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=B({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:J([o,c.pathname]),pathnameBase:Y(J([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=J([o,c.pathnameBase]))}return l}function B(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=H(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function H(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];S("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function q(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return S(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function W(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function $(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function V(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function Q(e){let t=V(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function K(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=C(e):(r=d({},e),w(!r.pathname||!r.pathname.includes("?"),$("?","pathname","search",r)),w(!r.pathname||!r.pathname.includes("#"),$("#","pathname","hash",r)),w(!r.search||!r.search.includes("#"),$("#","search","hash",r)));let o,l=""===e||""===r.pathname,i=l?"/":r.pathname;if(null==i)o=n;else{let e=t.length-1;if(!a&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?C(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:X(r),hash:G(a)}}(r,o),u=i&&"/"!==i&&i.endsWith("/"),c=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}var J=e=>e.join("/").replace(/\/\/+/g,"/"),Y=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),X=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",G=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function Z(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var ee=["POST","PUT","PATCH","DELETE"],te=(new Set(ee),["GET",...ee]);new Set(te),Symbol("ResetLoaderData");var ne=a.createContext(null);ne.displayName="DataRouter";var re=a.createContext(null);re.displayName="DataRouterState";var ae=a.createContext({isTransitioning:!1});ae.displayName="ViewTransition";var oe=a.createContext(new Map);oe.displayName="Fetchers";var le=a.createContext(null);le.displayName="Await";var ie=a.createContext(null);ie.displayName="Navigation";var se=a.createContext(null);se.displayName="Location";var ue=a.createContext({outlet:null,matches:[],isDataRoute:!1});ue.displayName="Route";var ce=a.createContext(null);ce.displayName="RouteError";function de(){return null!=a.useContext(se)}function fe(){return w(de(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(se).location}var pe="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function he(e){a.useContext(ie).static||a.useLayoutEffect(e)}function me(){let{isDataRoute:e}=a.useContext(ue);return e?function(){let{router:e}=Ee("useNavigate"),t=Ne("useNavigate"),n=a.useRef(!1);he((()=>{n.current=!0}));let r=a.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};S(n.current,pe),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,d({fromRouteId:t},a)))}),[e,t]);return r}():function(){w(de(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(ne),{basename:t,navigator:n}=a.useContext(ie),{matches:r}=a.useContext(ue),{pathname:o}=fe(),l=JSON.stringify(Q(r)),i=a.useRef(!1);he((()=>{i.current=!0}));let s=a.useCallback((function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(S(i.current,pe),!i.current)return;if("number"===typeof r)return void n.go(r);let s=K(r,JSON.parse(l),o,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:J([t,s.pathname])),(a.replace?n.replace:n.push)(s,a.state,a)}),[t,n,l,o,e]);return s}()}a.createContext(null);function ge(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=a.useContext(ue),{pathname:r}=fe(),o=JSON.stringify(Q(n));return a.useMemo((()=>K(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function ye(e,t,n,r){w(de(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=a.useContext(ie),{matches:l}=a.useContext(ue),i=l[l.length-1],s=i?i.params:{},u=i?i.pathname:"/",c=i?i.pathnameBase:"/",f=i&&i.route;{let e=f&&f.path||"";je(u,!f||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(u,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let p,h=fe();if(t){var m;let e="string"===typeof t?C(t):t;w("/"===c||(null===(m=e.pathname)||void 0===m?void 0:m.startsWith(c)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(c,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),p=e}else p=h;let g=p.pathname||"/",y=g;if("/"!==c){let e=c.replace(/^\//,"").split("/");y="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=R(e,{pathname:y});S(f||null!=v,'No routes matched location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" ')),S(null==v||void 0!==v[v.length-1].route.element||void 0!==v[v.length-1].route.Component||void 0!==v[v.length-1].route.lazy,'Matched leaf route at location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let b=ke(v&&v.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:J([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:J([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&b?a.createElement(se.Provider,{value:{location:d({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:"POP"}},b):b}function ve(){let e=Pe(),t=Z(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},l={padding:"2px 4px",backgroundColor:r},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=a.createElement(a.Fragment,null,a.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:l},"ErrorBoundary")," or"," ",a.createElement("code",{style:l},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,i)}var be=a.createElement(ve,null),we=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(ue.Provider,{value:this.props.routeContext},a.createElement(ce.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Se(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(ne);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(ue.Provider,{value:t},r)}function ke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=null===n||void 0===n?void 0:n.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==(null===o||void 0===o?void 0:o[e.route.id])));w(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(o).join(","))),r=r.slice(0,Math.min(r.length,e+1))}let l=!1,i=-1;if(n)for(let a=0;a<r.length;a++){let e=r[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=a),e.route.id){let{loaderData:t,errors:a}=n,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!a||void 0===a[e.route.id]);if(e.route.lazy||o){l=!0,r=i>=0?r.slice(0,i+1):[r[0]];break}}}return r.reduceRight(((e,s,u)=>{let c,d=!1,f=null,p=null;n&&(c=o&&s.route.id?o[s.route.id]:void 0,f=s.route.errorElement||be,l&&(i<0&&0===u?(je("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):i===u&&(d=!0,p=s.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,u+1)),m=()=>{let t;return t=c?f:d?p:s.route.Component?a.createElement(s.route.Component,null):s.route.element?s.route.element:e,a.createElement(Se,{match:s,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===u)?a.createElement(we,{location:n.location,revalidation:n.revalidation,component:f,error:c,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}function xe(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Ee(e){let t=a.useContext(ne);return w(t,xe(e)),t}function Ce(e){let t=a.useContext(re);return w(t,xe(e)),t}function Ne(e){let t=function(e){let t=a.useContext(ue);return w(t,xe(e)),t}(e),n=t.matches[t.matches.length-1];return w(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Pe(){var e;let t=a.useContext(ce),n=Ce("useRouteError"),r=Ne("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[r]}var Re={};function je(e,t,n){t||Re[e]||(Re[e]=!0,S(!1,n))}a.memo((function(e){let{routes:t,future:n,state:r}=e;return ye(t,void 0,r,n)}));function Te(e){w(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Oe(e){let{basename:t="/",children:n=null,location:r,navigationType:o="POP",navigator:l,static:i=!1}=e;w(!de(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=t.replace(/^\/*/,"/"),u=a.useMemo((()=>({basename:s,navigator:l,static:i,future:{}})),[s,l,i]);"string"===typeof r&&(r=C(r));let{pathname:c="/",search:d="",hash:f="",state:p=null,key:h="default"}=r,m=a.useMemo((()=>{let e=W(c,s);return null==e?null:{location:{pathname:e,search:d,hash:f,state:p,key:h},navigationType:o}}),[s,c,d,f,p,h,o]);return S(null!=m,'<Router basename="'.concat(s,'"> is not able to match the URL "').concat(c).concat(d).concat(f,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==m?null:a.createElement(ie.Provider,{value:u},a.createElement(se.Provider,{children:n,value:m}))}function _e(e){let{children:t,location:n}=e;return ye(Le(t),n)}a.Component;function Le(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,Le(e.props.children,o));w(e.type===Te,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),w(!e.props.index||!e.props.children,"An index route cannot have child routes.");let l={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=Le(e.props.children,o)),n.push(l)})),n}var Ae="get",ze="application/x-www-form-urlencoded";function De(e){return null!=e&&"string"===typeof e.tagName}var Fe=null;var Me=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ue(e){return null==e||Me.has(e)?e:(S(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(ze,'"')),null)}function Ie(e,t){let n,r,a,o,l;if(De(i=e)&&"form"===i.tagName.toLowerCase()){let l=e.getAttribute("action");r=l?W(l,t):null,n=e.getAttribute("method")||Ae,a=Ue(e.getAttribute("enctype"))||ze,o=new FormData(e)}else if(function(e){return De(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return De(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let l=e.form;if(null==l)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||l.getAttribute("action");if(r=i?W(i,t):null,n=e.getAttribute("formmethod")||l.getAttribute("method")||Ae,a=Ue(e.getAttribute("formenctype"))||Ue(l.getAttribute("enctype"))||ze,o=new FormData(l,e),!function(){if(null===Fe)try{new FormData(document.createElement("form"),0),Fe=!1}catch(e){Fe=!0}return Fe}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";o.append("".concat(e,"x"),"0"),o.append("".concat(e,"y"),"0")}else t&&o.append(t,r)}}else{if(De(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Ae,r=null,a=ze,l=e}var i;return o&&"text/plain"===a&&(l=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:l}}function Be(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function He(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function qe(e){return null!=e&&"string"===typeof e.page}function We(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function $e(e,t,n,r,a,o){let l=(e,t)=>!n[t]||e.route.id!==n[t].route.id,i=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===o?t.filter(((e,t)=>l(e,t)||i(e,t))):"data"===o?t.filter(((t,o)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(l(t,o)||i(t,o))return!0;if(t.route.shouldRevalidate){var u;let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null===(u=n[0])||void 0===u?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function Ve(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a})).flat(1),[...new Set(r)];var r}function Qe(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,a)=>{if(t&&!qe(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let o=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(o)||(n.add(o),e.push({key:o,link:a})),e}),[])}function Ke(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Je=new Set([100,101,204,205]);function Ye(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===W(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}a.Component;function Xe(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,o=a.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(Z(t))return a.createElement(Ge,{title:"Unhandled Thrown Response!"},a.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),o);if(t instanceof Error)r=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);r=new Error(e)}return a.createElement(Ge,{title:"Application Error!",isOutsideRemixApp:n},a.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),a.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),o)}function Ge(e){var t;let{title:n,renderScripts:r,isOutsideRemixApp:o,children:l}=e,{routeModules:i}=rt();return null!==(t=i.root)&&void 0!==t&&t.Layout&&!o?l:a.createElement("html",{lang:"en"},a.createElement("head",null,a.createElement("meta",{charSet:"utf-8"}),a.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),a.createElement("title",null,n)),a.createElement("body",null,a.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},l,r?a.createElement(ct,null):null)))}function Ze(e,t){return"lazy"===e.mode&&!0===t}function et(){let e=a.useContext(ne);return Be(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function tt(){let e=a.useContext(re);return Be(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var nt=a.createContext(void 0);function rt(){let e=a.useContext(nt);return Be(e,"You must render this element inside a <HydratedRouter> element"),e}function at(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function ot(e,t,n){if(n&&!ut)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function lt(e){let{page:t}=e,n=l(e,p),{router:r}=et(),o=a.useMemo((()=>R(r.routes,t,r.basename)),[r.routes,t,r.basename]);return o?a.createElement(st,d({page:t,matches:o},n)):null}function it(e){let{manifest:t,routeModules:n}=rt(),[r,o]=a.useState([]);return a.useEffect((()=>{let r=!1;return async function(e,t,n){return Qe((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await He(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(We).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?d(d({},e),{},{rel:"prefetch",as:"style"}):d(d({},e),{},{rel:"prefetch"}))))}(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}function st(e){let{page:t,matches:n}=e,r=l(e,h),o=fe(),{manifest:i,routeModules:s}=rt(),{basename:u}=et(),{loaderData:c,matches:f}=tt(),p=a.useMemo((()=>$e(t,n,f,i,o,"data")),[t,n,f,i,o]),m=a.useMemo((()=>$e(t,n,f,i,o,"assets")),[t,n,f,i,o]),g=a.useMemo((()=>{if(t===o.pathname+o.search+o.hash)return[];let e=new Set,r=!1;if(n.forEach((t=>{var n;let a=i.routes[t.route.id];a&&a.hasLoader&&(!p.some((e=>e.route.id===t.route.id))&&t.route.id in c&&null!==(n=s[t.route.id])&&void 0!==n&&n.shouldRevalidate||a.hasClientLoader?r=!0:e.add(t.route.id))})),0===e.size)return[];let a=Ye(t,u);return r&&e.size>0&&a.searchParams.set("_routes",n.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[a.pathname+a.search]}),[u,c,o,i,p,n,t,s]),y=a.useMemo((()=>Ve(m,i)),[m,i]),v=it(m);return a.createElement(a.Fragment,null,g.map((e=>a.createElement("link",d({key:e,rel:"prefetch",as:"fetch",href:e},r)))),y.map((e=>a.createElement("link",d({key:e,rel:"modulepreload",href:e},r)))),v.map((e=>{let{key:t,link:n}=e;return a.createElement("link",d({key:t},n))})))}nt.displayName="FrameworkContext";var ut=!1;function ct(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,renderMeta:o,routeDiscovery:i,ssr:s}=rt(),{router:u,static:c,staticContext:p}=et(),{matches:h}=tt(),m=Ze(i,s);o&&(o.didRenderScripts=!0);let g=ot(h,null,r);a.useEffect((()=>{ut=!0}),[]);let y=a.useMemo((()=>{var r;let o=p?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",i=c?"".concat(null!==(r=t.hmr)&&void 0!==r&&r.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(m?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(g.map(((e,n)=>{let r="route".concat(n),a=t.routes[e.route.id];Be(a,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:o,clientLoaderModule:l,clientMiddlewareModule:i,hydrateFallbackModule:s,module:u}=a,c=[...o?[{module:o,varName:"".concat(r,"_clientAction")}]:[],...l?[{module:l,varName:"".concat(r,"_clientLoader")}]:[],...i?[{module:i,varName:"".concat(r,"_clientMiddleware")}]:[],...s?[{module:s,varName:"".concat(r,"_HydrateFallback")}]:[],{module:u,varName:"".concat(r,"_main")}];return 1===c.length?"import * as ".concat(r," from ").concat(JSON.stringify(u),";"):[c.map((e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";'))).join("\n"),"const ".concat(r," = {").concat(c.map((e=>"...".concat(e.varName))).join(","),"};")].join("\n")})).join("\n"),"\n  ").concat(m?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=l(e,f),a=new Set(t.state.matches.map((e=>e.route.id))),o=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(o.pop();o.length>0;)i.push("/".concat(o.join("/"))),o.pop();i.forEach((e=>{let n=R(t.routes,e,t.basename);n&&n.forEach((e=>a.add(e.route.id)))}));let s=[...a].reduce(((e,t)=>Object.assign(e,{[t]:r.routes[t]})),{});return d(d({},r),{},{routes:s,sri:!!n||void 0})}(t,u),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map(((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t))).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return a.createElement(a.Fragment,null,a.createElement("script",d(d({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(o),type:void 0})),a.createElement("script",d(d({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(i),type:"module",async:!0})))}),[]),v=ut?[]:(b=t.entry.imports.concat(Ve(g,t,{includeHydrateFallback:!0})),[...new Set(b)]);var b;let w="object"===typeof t.sri?t.sri:{};return ut?null:a.createElement(a.Fragment,null,"object"===typeof t.sri?a.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:w})}}):null,m?null:a.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:w[t.url],suppressHydrationWarning:!0}),a.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:w[t.entry.module],suppressHydrationWarning:!0}),v.map((t=>a.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:w[t],suppressHydrationWarning:!0}))),y)}function dt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var ft="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{ft&&(window.__reactRouterVersion="7.6.2")}catch(ra){}function pt(e){let{basename:t,children:n,window:r}=e,o=a.useRef();null==o.current&&(o.current=b({window:r,v5Compat:!0}));let l=o.current,[i,s]=a.useState({action:l.action,location:l.location}),u=a.useCallback((e=>{a.startTransition((()=>s(e)))}),[s]);return a.useLayoutEffect((()=>l.listen(u)),[l,u]),a.createElement(Oe,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:l})}var ht=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mt=a.forwardRef((function(e,t){let n,{onClick:r,discover:o="render",prefetch:i="none",relative:s,reloadDocument:u,replace:c,state:f,target:p,to:h,preventScrollReset:g,viewTransition:y}=e,v=l(e,m),{basename:b}=a.useContext(ie),k="string"===typeof h&&ht.test(h),x=!1;if("string"===typeof h&&k&&(n=h,ft))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),n=W(t.pathname,b);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:x=!0}catch(ra){S(!1,'<Link to="'.concat(h,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let C=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w(de(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=a.useContext(ie),{hash:o,pathname:l,search:i}=ge(e,{relative:t}),s=l;return"/"!==n&&(s="/"===l?n:J([n,l])),r.createHref({pathname:s,search:i,hash:o})}(h,{relative:s}),[N,P,R]=function(e,t){let n=a.useContext(nt),[r,o]=a.useState(!1),[l,i]=a.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,p=a.useRef(null);a.useEffect((()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{i(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),a.useEffect((()=>{if(r){let e=setTimeout((()=>{i(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let h=()=>{o(!0)},m=()=>{o(!1),i(!1)};return n?"intent"!==e?[l,p,{}]:[l,p,{onFocus:at(s,h),onBlur:at(u,m),onMouseEnter:at(c,h),onMouseLeave:at(d,m),onTouchStart:at(f,h)}]:[!1,p,{}]}(i,v),j=function(e){let{target:t,replace:n,state:r,preventScrollReset:o,relative:l,viewTransition:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=me(),u=fe(),c=ge(e,{relative:l});return a.useCallback((a=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(a,t)){a.preventDefault();let t=void 0!==n?n:E(u)===E(c);s(e,{replace:t,state:r,preventScrollReset:o,relative:l,viewTransition:i})}}),[u,s,c,n,r,t,e,o,l,i])}(h,{replace:c,state:f,target:p,preventScrollReset:g,relative:s,viewTransition:y});let T=a.createElement("a",d(d(d({},v),R),{},{href:n||C,onClick:x||u?r:function(e){r&&r(e),e.defaultPrevented||j(e)},ref:dt(t,P),target:p,"data-discover":k||"render"!==o?void 0:"true"}));return N&&!k?a.createElement(a.Fragment,null,T,a.createElement(lt,{page:C})):T}));mt.displayName="Link",a.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:o="",end:i=!1,style:s,to:u,viewTransition:c,children:f}=e,p=l(e,g),h=ge(u,{relative:p.relative}),m=fe(),y=a.useContext(re),{navigator:v,basename:b}=a.useContext(ie),S=null!=y&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=a.useContext(ae);w(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=vt("useViewTransitionState"),o=ge(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=W(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=W(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=B(o.pathname,i)||null!=B(o.pathname,l)}(h)&&!0===c,k=v.encodeLocation?v.encodeLocation(h).pathname:h.pathname,x=m.pathname,E=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;r||(x=x.toLowerCase(),E=E?E.toLowerCase():null,k=k.toLowerCase()),E&&b&&(E=W(E,b)||E);const C="/"!==k&&k.endsWith("/")?k.length-1:k.length;let N,P=x===k||!i&&x.startsWith(k)&&"/"===x.charAt(C),R=null!=E&&(E===k||!i&&E.startsWith(k)&&"/"===E.charAt(k.length)),j={isActive:P,isPending:R,isTransitioning:S},T=P?n:void 0;N="function"===typeof o?o(j):[o,P?"active":null,R?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let O="function"===typeof s?s(j):s;return a.createElement(mt,d(d({},p),{},{"aria-current":T,className:N,ref:t,style:O,to:u,viewTransition:c}),"function"===typeof f?f(j):f)})).displayName="NavLink";var gt=a.forwardRef(((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:o,reloadDocument:i,replace:s,state:u,method:c=Ae,action:f,onSubmit:p,relative:h,preventScrollReset:m,viewTransition:g}=e,v=l(e,y),b=St(),S=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=a.useContext(ie),r=a.useContext(ue);w(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),l=d({},ge(e||".",{relative:t})),i=fe();if(null==e){l.search=i.search;let e=new URLSearchParams(l.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();l.search=n?"?".concat(n):""}}e&&"."!==e||!o.route.index||(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(l.pathname="/"===l.pathname?n:J([n,l.pathname]));return E(l)}(f,{relative:h}),k="get"===c.toLowerCase()?"get":"post",x="string"===typeof f&&ht.test(f);return a.createElement("form",d(d({ref:t,method:k,action:S,onSubmit:i?p:e=>{if(p&&p(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||c;b(t||e.currentTarget,{fetcherKey:r,method:n,navigate:o,replace:s,state:u,relative:h,preventScrollReset:m,viewTransition:g})}},v),{},{"data-discover":x||"render"!==n?void 0:"true"}))}));function yt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function vt(e){let t=a.useContext(ne);return w(t,yt(e)),t}gt.displayName="Form";var bt=0,wt=()=>"__".concat(String(++bt),"__");function St(){let{router:e}=vt("useSubmit"),{basename:t}=a.useContext(ie),n=Ne("useRouteId");return a.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:o,method:l,encType:i,formData:s,body:u}=Ie(r,t);if(!1===a.navigate){let t=a.fetcherKey||wt();await e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||l,formEncType:a.encType||i,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||l,formEncType:a.encType||i,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[e,t,n])}var kt=n(579);const xt=()=>(0,kt.jsxs)("div",{className:"home-container",children:[(0,kt.jsx)("nav",{className:"floating-nav",children:(0,kt.jsxs)("div",{className:"nav-content",children:[(0,kt.jsx)(mt,{to:"/",className:"nav-logo",children:"College Events"}),(0,kt.jsxs)("div",{className:"nav-links",children:[(0,kt.jsx)(mt,{to:"/events",children:"Events"}),(0,kt.jsx)(mt,{to:"/login",children:"Login"}),(0,kt.jsx)(mt,{to:"/register",className:"nav-cta",children:"Register"})]})]})}),(0,kt.jsx)("section",{className:"hero-section",children:(0,kt.jsx)("div",{className:"hero-overlay",children:(0,kt.jsxs)("div",{className:"hero-content",children:[(0,kt.jsxs)("h1",{className:"hero-title",children:["Discover Amazing",(0,kt.jsx)("span",{className:"highlight",children:" College Events"})]}),(0,kt.jsx)("p",{className:"hero-subtitle",children:"Connect with events happening at colleges near you. Join communities, learn new skills, and make lasting memories."}),(0,kt.jsxs)("div",{className:"hero-buttons",children:[(0,kt.jsxs)(mt,{to:"/events",className:"btn-hero primary",children:[(0,kt.jsx)("span",{children:"\ud83c\udfaf"}),"Browse Events"]}),(0,kt.jsxs)(mt,{to:"/register",className:"btn-hero secondary",children:[(0,kt.jsx)("span",{children:"\ud83d\ude80"}),"Get Started"]})]}),(0,kt.jsxs)("div",{className:"hero-stats",children:[(0,kt.jsxs)("div",{className:"stat",children:[(0,kt.jsx)("h3",{children:"500+"}),(0,kt.jsx)("p",{children:"Active Events"})]}),(0,kt.jsxs)("div",{className:"stat",children:[(0,kt.jsx)("h3",{children:"50+"}),(0,kt.jsx)("p",{children:"Partner Colleges"})]}),(0,kt.jsxs)("div",{className:"stat",children:[(0,kt.jsx)("h3",{children:"10K+"}),(0,kt.jsx)("p",{children:"Students Connected"})]})]})]})})}),(0,kt.jsx)("section",{className:"features-section",children:(0,kt.jsxs)("div",{className:"container",children:[(0,kt.jsxs)("div",{className:"section-header",children:[(0,kt.jsx)("h2",{children:"Why Choose Our Platform?"}),(0,kt.jsx)("p",{children:"Everything you need to discover and participate in college events"})]}),(0,kt.jsxs)("div",{className:"features-grid",children:[(0,kt.jsxs)("div",{className:"feature-card",children:[(0,kt.jsx)("div",{className:"feature-icon",children:"\ud83c\udfaf"}),(0,kt.jsx)("h3",{children:"Discover Events"}),(0,kt.jsx)("p",{children:"Find events based on your interests, location, and academic field. Never miss out on opportunities that matter to you."}),(0,kt.jsx)("div",{className:"feature-link",children:(0,kt.jsx)(mt,{to:"/events",children:"Explore Events \u2192"})})]}),(0,kt.jsxs)("div",{className:"feature-card",children:[(0,kt.jsx)("div",{className:"feature-icon",children:"\ud83e\udd1d"}),(0,kt.jsx)("h3",{children:"Connect with Peers"}),(0,kt.jsx)("p",{children:"Meet like-minded students, build your network, and create meaningful connections that last beyond college."}),(0,kt.jsx)("div",{className:"feature-link",children:(0,kt.jsx)(mt,{to:"/register",children:"Join Community \u2192"})})]}),(0,kt.jsxs)("div",{className:"feature-card",children:[(0,kt.jsx)("div",{className:"feature-icon",children:"\ud83d\udcc5"}),(0,kt.jsx)("h3",{children:"Manage Events"}),(0,kt.jsx)("p",{children:"Create, organize, and promote your own events. Reach thousands of students across multiple colleges."}),(0,kt.jsx)("div",{className:"feature-link",children:(0,kt.jsx)(mt,{to:"/register/college",children:"Start Organizing \u2192"})})]})]})]})}),(0,kt.jsx)("section",{className:"cta-section",children:(0,kt.jsx)("div",{className:"container",children:(0,kt.jsxs)("div",{className:"cta-content",children:[(0,kt.jsx)("h2",{children:"Ready to Get Started?"}),(0,kt.jsx)("p",{children:"Join thousands of students already discovering amazing events"}),(0,kt.jsxs)("div",{className:"cta-buttons",children:[(0,kt.jsx)(mt,{to:"/register/student",className:"btn-cta primary",children:"Register as Student"}),(0,kt.jsx)(mt,{to:"/register/college",className:"btn-cta secondary",children:"Register as College"})]})]})})})]});function Et(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ct}=Object.prototype,{getPrototypeOf:Nt}=Object,{iterator:Pt,toStringTag:Rt}=Symbol,jt=(Tt=Object.create(null),e=>{const t=Ct.call(e);return Tt[t]||(Tt[t]=t.slice(8,-1).toLowerCase())});var Tt;const Ot=e=>(e=e.toLowerCase(),t=>jt(t)===e),_t=e=>t=>typeof t===e,{isArray:Lt}=Array,At=_t("undefined");const zt=Ot("ArrayBuffer");const Dt=_t("string"),Ft=_t("function"),Mt=_t("number"),Ut=e=>null!==e&&"object"===typeof e,It=e=>{if("object"!==jt(e))return!1;const t=Nt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Rt in e)&&!(Pt in e)},Bt=Ot("Date"),Ht=Ot("File"),qt=Ot("Blob"),Wt=Ot("FileList"),$t=Ot("URLSearchParams"),[Vt,Qt,Kt,Jt]=["ReadableStream","Request","Response","Headers"].map(Ot);function Yt(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Lt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let l;for(n=0;n<o;n++)l=r[n],t.call(null,e[l],l,e)}}function Xt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const Gt="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Zt=e=>!At(e)&&e!==Gt;const en=(tn="undefined"!==typeof Uint8Array&&Nt(Uint8Array),e=>tn&&e instanceof tn);var tn;const nn=Ot("HTMLFormElement"),rn=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),an=Ot("RegExp"),on=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Yt(n,((n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)})),Object.defineProperties(e,r)};const ln=Ot("AsyncFunction"),sn=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],Gt.addEventListener("message",(e=>{let{source:t,data:a}=e;t===Gt&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),Gt.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Ft(Gt.postMessage)),un="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Gt):"undefined"!==typeof process&&process.nextTick||sn,cn={isArray:Lt,isArrayBuffer:zt,isBuffer:function(e){return null!==e&&!At(e)&&null!==e.constructor&&!At(e.constructor)&&Ft(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Ft(e.append)&&("formdata"===(t=jt(e))||"object"===t&&Ft(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&zt(e.buffer),t},isString:Dt,isNumber:Mt,isBoolean:e=>!0===e||!1===e,isObject:Ut,isPlainObject:It,isReadableStream:Vt,isRequest:Qt,isResponse:Kt,isHeaders:Jt,isUndefined:At,isDate:Bt,isFile:Ht,isBlob:qt,isRegExp:an,isFunction:Ft,isStream:e=>Ut(e)&&Ft(e.pipe),isURLSearchParams:$t,isTypedArray:en,isFileList:Wt,forEach:Yt,merge:function e(){const{caseless:t}=Zt(this)&&this||{},n={},r=(r,a)=>{const o=t&&Xt(n,a)||a;It(n[o])&&It(r)?n[o]=e(n[o],r):It(r)?n[o]=e({},r):Lt(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&Yt(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Yt(t,((t,r)=>{n&&Ft(t)?e[r]=Et(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)l=a[o],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&Nt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:jt,kindOfTest:Ot,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Lt(e))return e;let t=e.length;if(!Mt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Pt]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:nn,hasOwnProperty:rn,hasOwnProp:rn,reduceDescriptors:on,freezeMethods:e=>{on(e,((t,n)=>{if(Ft(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Ft(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Lt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Xt,global:Gt,isContextDefined:Zt,isSpecCompliantForm:function(e){return!!(e&&Ft(e.append)&&"FormData"===e[Rt]&&e[Pt])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Ut(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Lt(e)?[]:{};return Yt(e,((e,t)=>{const o=n(e,r+1);!At(o)&&(a[t]=o)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:ln,isThenable:e=>e&&(Ut(e)||Ft(e))&&Ft(e.then)&&Ft(e.catch),setImmediate:sn,asap:un,isIterable:e=>null!=e&&Ft(e[Pt])};function dn(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}cn.inherits(dn,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:cn.toJSONObject(this.config),code:this.code,status:this.status}}});const fn=dn.prototype,pn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{pn[e]={value:e}})),Object.defineProperties(dn,pn),Object.defineProperty(fn,"isAxiosError",{value:!0}),dn.from=(e,t,n,r,a,o)=>{const l=Object.create(fn);return cn.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),dn.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const hn=dn;function mn(e){return cn.isPlainObject(e)||cn.isArray(e)}function gn(e){return cn.endsWith(e,"[]")?e.slice(0,-2):e}function yn(e,t,n){return e?e.concat(t).map((function(e,t){return e=gn(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const vn=cn.toFlatObject(cn,{},null,(function(e){return/^is[A-Z]/.test(e)}));const bn=function(e,t,n){if(!cn.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=cn.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!cn.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,o=n.dots,l=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&cn.isSpecCompliantForm(t);if(!cn.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(cn.isDate(e))return e.toISOString();if(!i&&cn.isBlob(e))throw new hn("Blob is not supported. Use a Buffer instead.");return cn.isArrayBuffer(e)||cn.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(cn.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(cn.isArray(e)&&function(e){return cn.isArray(e)&&!e.some(mn)}(e)||(cn.isFileList(e)||cn.endsWith(n,"[]"))&&(i=cn.toArray(e)))return n=gn(n),i.forEach((function(e,r){!cn.isUndefined(e)&&null!==e&&t.append(!0===l?yn([n],r,o):null===l?n:n+"[]",s(e))})),!1;return!!mn(e)||(t.append(yn(a,n,o),s(e)),!1)}const c=[],d=Object.assign(vn,{defaultVisitor:u,convertValue:s,isVisitable:mn});if(!cn.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!cn.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),cn.forEach(n,(function(n,o){!0===(!(cn.isUndefined(n)||null===n)&&a.call(t,n,cn.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function wn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Sn(e,t){this._pairs=[],e&&bn(e,this,t)}const kn=Sn.prototype;kn.append=function(e,t){this._pairs.push([e,t])},kn.toString=function(e){const t=e?function(t){return e.call(this,t,wn)}:wn;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const xn=Sn;function En(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Cn(e,t,n){if(!t)return e;const r=n&&n.encode||En;cn.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):cn.isURLSearchParams(t)?t.toString():new xn(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Nn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){cn.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Pn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Rn={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:xn,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},jn="undefined"!==typeof window&&"undefined"!==typeof document,Tn="object"===typeof navigator&&navigator||void 0,On=jn&&(!Tn||["ReactNative","NativeScript","NS"].indexOf(Tn.product)<0),_n="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Ln=jn&&window.location.href||"http://localhost",An=d(d({},r),Rn);const zn=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const l=Number.isFinite(+o),i=a>=e.length;if(o=!o&&cn.isArray(r)?r.length:o,i)return cn.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!l;r[o]&&cn.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&cn.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!l}if(cn.isFormData(e)&&cn.isFunction(e.entries)){const n={};return cn.forEachEntry(e,((e,r)=>{t(function(e){return cn.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const Dn={transitional:Pn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=cn.isObject(e);a&&cn.isHTMLForm(e)&&(e=new FormData(e));if(cn.isFormData(e))return r?JSON.stringify(zn(e)):e;if(cn.isArrayBuffer(e)||cn.isBuffer(e)||cn.isStream(e)||cn.isFile(e)||cn.isBlob(e)||cn.isReadableStream(e))return e;if(cn.isArrayBufferView(e))return e.buffer;if(cn.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return bn(e,new An.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return An.isNode&&cn.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=cn.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return bn(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(cn.isString(e))try{return(t||JSON.parse)(e),cn.trim(e)}catch(ra){if("SyntaxError"!==ra.name)throw ra}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Dn.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(cn.isResponse(e)||cn.isReadableStream(e))return e;if(e&&cn.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(ra){if(n){if("SyntaxError"===ra.name)throw hn.from(ra,hn.ERR_BAD_RESPONSE,this,null,this.response);throw ra}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:An.classes.FormData,Blob:An.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};cn.forEach(["delete","get","head","post","put","patch"],(e=>{Dn.headers[e]={}}));const Fn=Dn,Mn=cn.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Un=Symbol("internals");function In(e){return e&&String(e).trim().toLowerCase()}function Bn(e){return!1===e||null==e?e:cn.isArray(e)?e.map(Bn):String(e)}function Hn(e,t,n,r,a){return cn.isFunction(r)?r.call(this,t,n):(a&&(t=n),cn.isString(t)?cn.isString(r)?-1!==t.indexOf(r):cn.isRegExp(r)?r.test(t):void 0:void 0)}class qn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=In(t);if(!a)throw new Error("header name must be a non-empty string");const o=cn.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=Bn(e))}const o=(e,t)=>cn.forEach(e,((e,n)=>a(e,n,t)));if(cn.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(cn.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Mn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(cn.isObject(e)&&cn.isIterable(e)){let n,r,a={};for(const t of e){if(!cn.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?cn.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=In(e)){const n=cn.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(cn.isFunction(t))return t.call(this,e,n);if(cn.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=In(e)){const n=cn.findKey(this,e);return!(!n||void 0===this[n]||t&&!Hn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=In(e)){const a=cn.findKey(n,e);!a||t&&!Hn(0,n[a],a,t)||(delete n[a],r=!0)}}return cn.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Hn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return cn.forEach(this,((r,a)=>{const o=cn.findKey(n,a);if(o)return t[o]=Bn(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();l!==a&&delete t[a],t[l]=Bn(r),n[l]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return cn.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&cn.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[Un]=this[Un]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=In(e);t[r]||(!function(e,t){const n=cn.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return cn.isArray(e)?e.forEach(r):r(e),this}}qn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),cn.reduceDescriptors(qn.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),cn.freezeMethods(qn);const Wn=qn;function $n(e,t){const n=this||Fn,r=t||n,a=Wn.from(r.headers);let o=r.data;return cn.forEach(e,(function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)})),a.normalize(),o}function Vn(e){return!(!e||!e.__CANCEL__)}function Qn(e,t,n){hn.call(this,null==e?"canceled":e,hn.ERR_CANCELED,t,n),this.name="CanceledError"}cn.inherits(Qn,hn,{__CANCEL__:!0});const Kn=Qn;function Jn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new hn("Request failed with status code "+n.status,[hn.ERR_BAD_REQUEST,hn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Yn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,l=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[l];a||(a=s),n[o]=i,r[o]=s;let c=l,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===l&&(l=(l+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const Xn=function(e,t){let n,r,a=0,o=1e3/t;const l=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];t>=o?l(s,e):(n=s,r||(r=setTimeout((()=>{r=null,l(n)}),o-t)))},()=>n&&l(n)]},Gn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=Yn(50,250);return Xn((n=>{const o=n.loaded,l=n.lengthComputable?n.total:void 0,i=o-r,s=a(i);r=o;e({loaded:o,total:l,progress:l?o/l:void 0,bytes:i,rate:s||void 0,estimated:s&&l&&o<=l?(l-o)/s:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})}),n)},Zn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},er=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return cn.asap((()=>e(...n)))},tr=An.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,An.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(An.origin),An.navigator&&/(msie|trident)/i.test(An.navigator.userAgent)):()=>!0,nr=An.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const l=[e+"="+encodeURIComponent(t)];cn.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),cn.isString(r)&&l.push("path="+r),cn.isString(a)&&l.push("domain="+a),!0===o&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function rr(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const ar=e=>e instanceof Wn?d({},e):e;function or(e,t){t=t||{};const n={};function r(e,t,n,r){return cn.isPlainObject(e)&&cn.isPlainObject(t)?cn.merge.call({caseless:r},e,t):cn.isPlainObject(t)?cn.merge({},t):cn.isArray(t)?t.slice():t}function a(e,t,n,a){return cn.isUndefined(t)?cn.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!cn.isUndefined(t))return r(void 0,t)}function l(e,t){return cn.isUndefined(t)?cn.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const s={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(ar(e),ar(t),0,!0)};return cn.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=s[r]||a,l=o(e[r],t[r],r);cn.isUndefined(l)&&o!==i||(n[r]=l)})),n}const lr=e=>{const t=or({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:l,headers:i,auth:s}=t;if(t.headers=i=Wn.from(i),t.url=Cn(rr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),cn.isFormData(r))if(An.hasStandardBrowserEnv||An.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(An.hasStandardBrowserEnv&&(a&&cn.isFunction(a)&&(a=a(t)),a||!1!==a&&tr(t.url))){const e=o&&l&&nr.read(l);e&&i.set(o,e)}return t},ir="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=lr(e);let a=r.data;const o=Wn.from(r.headers).normalize();let l,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Wn.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Jn((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new hn("Request aborted",hn.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new hn("Network Error",hn.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Pn;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new hn(t,a.clarifyTimeoutError?hn.ETIMEDOUT:hn.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&cn.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),cn.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,c]=Gn(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([i,u]=Gn(f),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(l=t=>{m&&(n(!t||t.type?new Kn(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===An.protocols.indexOf(y)?n(new hn("Unsupported protocol "+y+":",hn.ERR_BAD_REQUEST,e)):m.send(a||null)}))},sr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof hn?t:new Kn(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,a(new hn("timeout ".concat(t," of ms exceeded"),hn.ETIMEDOUT))}),t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>cn.asap(l),i}};function ur(e,t){this.v=e,this.k=t}function cr(e){return function(){return new dr(e.apply(this,arguments))}}function dr(e){var t,n;function r(t,n){try{var o=e[t](n),l=o.value,i=l instanceof ur;Promise.resolve(i?l.v:l).then((function(n){if(i){var s="return"===t?"return":"next";if(!l.k||n.done)return r(s,n);n=e[s](n).value}a(o.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(o,l){var i={key:e,arg:a,resolve:o,reject:l,next:null};n?n=n.next=i:(t=n=i,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function fr(e){return new ur(e,0)}function pr(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new ur(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function hr(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new mr(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function mr(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return mr=function(e){this.s=e,this.n=e.next},mr.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new mr(e)}dr.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},dr.prototype.next=function(e){return this._invoke("next",e)},dr.prototype.throw=function(e){return this._invoke("throw",e)},dr.prototype.return=function(e){return this._invoke("return",e)};const gr=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},yr=function(){var e=cr((function*(e,t){var n,r=!1,a=!1;try{for(var o,l=hr(vr(e));r=!(o=yield fr(l.next())).done;r=!1){const e=o.value;yield*pr(hr(gr(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=l.return&&(yield fr(l.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),vr=function(){var e=cr((function*(e){if(e[Symbol.asyncIterator])return void(yield*pr(hr(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield fr(t.read());if(e)break;yield n}}finally{yield fr(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),br=(e,t,n,r)=>{const a=yr(e,t);let o,l=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let o=r.byteLength;if(n){let e=l+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},wr="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Sr=wr&&"function"===typeof ReadableStream,kr=wr&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),xr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(ra){return!1}},Er=Sr&&xr((()=>{let e=!1;const t=new Request(An.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Cr=Sr&&xr((()=>cn.isReadableStream(new Response("").body))),Nr={stream:Cr&&(e=>e.body)};var Pr;wr&&(Pr=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Nr[e]&&(Nr[e]=cn.isFunction(Pr[e])?t=>t[e]():(t,n)=>{throw new hn("Response type '".concat(e,"' is not supported"),hn.ERR_NOT_SUPPORT,n)})})));const Rr=async(e,t)=>{const n=cn.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(cn.isBlob(e))return e.size;if(cn.isSpecCompliantForm(e)){const t=new Request(An.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return cn.isArrayBufferView(e)||cn.isArrayBuffer(e)?e.byteLength:(cn.isURLSearchParams(e)&&(e+=""),cn.isString(e)?(await kr(e)).byteLength:void 0)})(t):n},jr=wr&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:l,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:p}=lr(e);u=u?(u+"").toLowerCase():"text";let h,m=sr([a,o&&o.toAbortSignal()],l);const g=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let y;try{if(s&&Er&&"get"!==n&&"head"!==n&&0!==(y=await Rr(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(cn.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=Zn(y,Gn(er(s)));r=br(n.body,65536,e,t)}}cn.isString(f)||(f=f?"include":"omit");const a="credentials"in Request.prototype;h=new Request(t,d(d({},p),{},{signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?f:void 0}));let o=await fetch(h);const l=Cr&&("stream"===u||"response"===u);if(Cr&&(i||l&&g)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=cn.toFiniteNumber(o.headers.get("content-length")),[n,r]=i&&Zn(t,Gn(er(i),!0))||[];o=new Response(br(o.body,65536,n,(()=>{r&&r(),g&&g()})),e)}u=u||"text";let v=await Nr[cn.findKey(Nr,u)||"text"](o,e);return!l&&g&&g(),await new Promise(((t,n)=>{Jn(t,n,{data:v,headers:Wn.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:h})}))}catch(v){if(g&&g(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new hn("Network Error",hn.ERR_NETWORK,e,h),{cause:v.cause||v});throw hn.from(v,v&&v.code,e,h)}}),Tr={http:null,xhr:ir,fetch:jr};cn.forEach(Tr,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(ra){}Object.defineProperty(e,"adapterName",{value:t})}}));const Or=e=>"- ".concat(e),_r=e=>cn.isFunction(e)||null===e||!1===e,Lr=e=>{e=cn.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!_r(n)&&(r=Tr[(t=String(n)).toLowerCase()],void 0===r))throw new hn("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(Or).join("\n"):" "+Or(e[0]):"as no adapter specified";throw new hn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Ar(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Kn(null,e)}function zr(e){Ar(e),e.headers=Wn.from(e.headers),e.data=$n.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Lr(e.adapter||Fn.adapter)(e).then((function(t){return Ar(e),t.data=$n.call(e,e.transformResponse,t),t.headers=Wn.from(t.headers),t}),(function(t){return Vn(t)||(Ar(e),t&&t.response&&(t.response.data=$n.call(e,e.transformResponse,t.response),t.response.headers=Wn.from(t.response.headers))),Promise.reject(t)}))}const Dr="1.9.0",Fr={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Fr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Mr={};Fr.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new hn(r(a," has been removed"+(t?" in "+t:"")),hn.ERR_DEPRECATED);return t&&!Mr[a]&&(Mr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},Fr.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Ur={assertOptions:function(e,t,n){if("object"!==typeof e)throw new hn("options must be an object",hn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],l=t[o];if(l){const t=e[o],n=void 0===t||l(t,o,e);if(!0!==n)throw new hn("option "+o+" must be "+n,hn.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new hn("Unknown option "+o,hn.ERR_BAD_OPTION)}},validators:Fr},Ir=Ur.validators;class Br{constructor(e){this.defaults=e||{},this.interceptors={request:new Nn,response:new Nn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(ra){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=or(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&Ur.assertOptions(n,{silentJSONParsing:Ir.transitional(Ir.boolean),forcedJSONParsing:Ir.transitional(Ir.boolean),clarifyTimeoutError:Ir.transitional(Ir.boolean)},!1),null!=r&&(cn.isFunction(r)?t.paramsSerializer={serialize:r}:Ur.assertOptions(r,{encode:Ir.function,serialize:Ir.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Ur.assertOptions(t,{baseUrl:Ir.spelling("baseURL"),withXsrfToken:Ir.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&cn.merge(a.common,a[t.method]);a&&cn.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=Wn.concat(o,a);const l=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,d=0;if(!i){const e=[zr.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=l.length;let f=t;for(d=0;d<c;){const e=l[d++],t=l[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=zr.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Cn(rr((e=or(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}cn.forEach(["delete","get","head","options"],(function(e){Br.prototype[e]=function(t,n){return this.request(or(n||{},{method:e,url:t,data:(n||{}).data}))}})),cn.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(or(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Br.prototype[e]=t(),Br.prototype[e+"Form"]=t(!0)}));const Hr=Br;class qr{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new Kn(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new qr((function(t){e=t})),cancel:e}}}const Wr=qr;const $r={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries($r).forEach((e=>{let[t,n]=e;$r[n]=t}));const Vr=$r;const Qr=function e(t){const n=new Hr(t),r=Et(Hr.prototype.request,n);return cn.extend(r,Hr.prototype,n,{allOwnKeys:!0}),cn.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(or(t,n))},r}(Fn);Qr.Axios=Hr,Qr.CanceledError=Kn,Qr.CancelToken=Wr,Qr.isCancel=Vn,Qr.VERSION=Dr,Qr.toFormData=bn,Qr.AxiosError=hn,Qr.Cancel=Qr.CanceledError,Qr.all=function(e){return Promise.all(e)},Qr.spread=function(e){return function(t){return e.apply(null,t)}},Qr.isAxiosError=function(e){return cn.isObject(e)&&!0===e.isAxiosError},Qr.mergeConfig=or,Qr.AxiosHeaders=Wn,Qr.formToJSON=e=>zn(cn.isHTMLForm(e)?new FormData(e):e),Qr.getAdapter=Lr,Qr.HttpStatusCode=Vr,Qr.default=Qr;const Kr=Qr,Jr=Kr.create({baseURL:"http://localhost:5000/api/auth"});Jr.interceptors.request.use((e=>{var t;return console.log("API Request:",null===(t=e.method)||void 0===t?void 0:t.toUpperCase(),e.url,e.data),e}),(e=>(console.error("Request Error:",e),Promise.reject(e)))),Jr.interceptors.response.use((e=>(console.log("API Response:",e.status,e.data),e)),(e=>{var t,n;return console.error("Response Error:",null===(t=e.response)||void 0===t?void 0:t.status,(null===(n=e.response)||void 0===n?void 0:n.data)||e.message),Promise.reject(e)}));const Yr=()=>{const e=me(),[t,n]=(0,a.useState)({email:"",password:"",userType:"student"}),[r,o]=(0,a.useState)(""),l=e=>{n(d(d({},t),{},{[e.target.name]:e.target.value}))};return(0,kt.jsx)("div",{className:"auth-container",children:(0,kt.jsxs)("div",{className:"auth-box",children:[(0,kt.jsxs)("div",{className:"auth-header",children:[(0,kt.jsx)("h1",{children:"Welcome Back"}),(0,kt.jsx)("p",{children:"Sign in to your account"})]}),(0,kt.jsxs)("div",{className:"form-container",children:[r&&(0,kt.jsx)("div",{className:"error-message",children:r}),(0,kt.jsxs)("form",{onSubmit:async n=>{n.preventDefault(),o("");try{await(async e=>{try{const t=await Jr.post("/login",e);return t.data.token&&(localStorage.setItem("token",t.data.token),localStorage.setItem("user",JSON.stringify(t.data.user))),t.data}catch(r){throw console.error("Login error:",r),r}})(t),e("/dashboard")}catch(i){var a,l;o((null===(a=i.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.message)||"Login failed. Please try again.")}},className:"auth-form",children:[(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{htmlFor:"email",children:"Email Address"}),(0,kt.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:l,placeholder:"Enter your email",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{htmlFor:"password",children:"Password"}),(0,kt.jsx)("input",{type:"password",id:"password",name:"password",value:t.password,onChange:l,placeholder:"Enter your password",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{htmlFor:"userType",children:"Account Type"}),(0,kt.jsxs)("select",{id:"userType",name:"userType",value:t.userType,onChange:l,required:!0,children:[(0,kt.jsx)("option",{value:"student",children:"Student"}),(0,kt.jsx)("option",{value:"college",children:"College"})]})]}),(0,kt.jsx)("button",{type:"submit",className:"auth-btn",children:"Sign In"})]}),(0,kt.jsxs)("div",{className:"auth-footer",children:["Don't have an account? ",(0,kt.jsx)("a",{href:"/register",className:"auth-link",children:"Register here"})]})]})]})})},Xr=()=>(0,kt.jsx)("div",{className:"register-container",children:(0,kt.jsxs)("div",{className:"register-box",children:[(0,kt.jsxs)("div",{className:"register-header",children:[(0,kt.jsx)("h2",{children:"Choose Registration Type"}),(0,kt.jsx)("p",{children:"Select your account type to get started"})]}),(0,kt.jsxs)("div",{className:"register-options",children:[(0,kt.jsxs)(mt,{to:"/register/student",className:"register-option student-option",children:[(0,kt.jsx)("div",{className:"option-icon",children:"\ud83c\udf93"}),(0,kt.jsx)("h3",{children:"Register as Student"}),(0,kt.jsx)("p",{children:"Join events at your college and discover new opportunities"})]}),(0,kt.jsxs)(mt,{to:"/register/college",className:"register-option college-option",children:[(0,kt.jsx)("div",{className:"option-icon",children:"\ud83c\udfeb"}),(0,kt.jsx)("h3",{children:"Register as College"}),(0,kt.jsx)("p",{children:"Create and manage events for your institution"})]})]}),(0,kt.jsxs)("div",{className:"register-footer",children:["Already have an account? ",(0,kt.jsx)(mt,{to:"/login",className:"login-link",children:"Login here"})]})]})}),Gr=()=>{const e=me(),[t,n]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:"",age:"",collegeName:"",class:"",adharId:"",studentCardId:""}),[r,o]=(0,a.useState)(""),[l,i]=(0,a.useState)(!1),s=e=>{const{name:r,value:a}=e.target;n(d(d({},t),{},{[r]:a}))};return(0,kt.jsx)("div",{className:"auth-container",children:(0,kt.jsxs)("div",{className:"auth-box",children:[(0,kt.jsxs)("div",{className:"auth-header",children:[(0,kt.jsx)("h2",{children:"Student Registration"}),(0,kt.jsx)("p",{children:"Create your student account"})]}),r&&(0,kt.jsx)("div",{className:"error-message",children:r}),(0,kt.jsxs)("form",{onSubmit:async n=>{if(n.preventDefault(),t.password===t.confirmPassword)try{i(!0),o(""),await(async e=>{try{return(await Jr.post("/register/student",e)).data}catch(r){throw console.error("Student registration error:",r),r}})({name:t.name,email:t.email,password:t.password,age:parseInt(t.age),collegeName:t.collegeName,class:t.class,adharId:t.adharId,studentCardId:t.studentCardId}),e("/login")}catch(s){var a,l;console.error("Registration error:",s),o((null===(a=s.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.message)||"Registration failed. Please try again.")}finally{i(!1)}else o("Passwords do not match")},className:"auth-form",children:[(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Full Name"}),(0,kt.jsx)("input",{type:"text",name:"name",value:t.name,onChange:s,placeholder:"Enter your full name",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Email Address"}),(0,kt.jsx)("input",{type:"email",name:"email",value:t.email,onChange:s,placeholder:"Enter your email",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Password"}),(0,kt.jsx)("input",{type:"password",name:"password",value:t.password,onChange:s,placeholder:"Create a password",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Confirm Password"}),(0,kt.jsx)("input",{type:"password",name:"confirmPassword",value:t.confirmPassword,onChange:s,placeholder:"Confirm your password",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Age"}),(0,kt.jsx)("input",{type:"number",name:"age",value:t.age,onChange:s,placeholder:"Enter your age",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"College Name"}),(0,kt.jsx)("input",{type:"text",name:"collegeName",value:t.collegeName,onChange:s,placeholder:"Enter your college name",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Class/Year"}),(0,kt.jsx)("input",{type:"text",name:"class",value:t.class,onChange:s,placeholder:"e.g., 2nd Year, Final Year",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Aadhar ID"}),(0,kt.jsx)("input",{type:"text",name:"adharId",value:t.adharId,onChange:s,placeholder:"Enter your Aadhar number",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Student Card ID"}),(0,kt.jsx)("input",{type:"text",name:"studentCardId",value:t.studentCardId,onChange:s,placeholder:"Enter your student ID",required:!0})]}),(0,kt.jsx)("button",{type:"submit",disabled:l,className:"auth-btn",children:l?"Registering...":"Register as Student"})]}),(0,kt.jsxs)("div",{className:"auth-footer",children:["Already have an account? ",(0,kt.jsx)("a",{href:"/login",className:"auth-link",children:"Login here"})]})]})})},Zr=()=>{const e=me(),[t,n]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:"",address:"",documents:[]}),[r,o]=(0,a.useState)(""),[l,i]=(0,a.useState)(!1),s=e=>{const{name:r,value:a}=e.target;n(d(d({},t),{},{[r]:a}))};return(0,kt.jsx)("div",{className:"auth-container",children:(0,kt.jsxs)("div",{className:"auth-box",children:[(0,kt.jsxs)("div",{className:"auth-header",children:[(0,kt.jsx)("h2",{children:"College Registration"}),(0,kt.jsx)("p",{children:"Register your institution"})]}),r&&(0,kt.jsx)("div",{className:"error-message",children:r}),(0,kt.jsxs)("form",{onSubmit:async n=>{if(n.preventDefault(),t.password===t.confirmPassword)try{i(!0),o(""),console.log("Submitting college registration form with data:",{name:t.name,email:t.email,address:t.address,documentsCount:t.documents.length});const n=await(async e=>{try{const t=new FormData;if(t.append("name",e.name),t.append("email",e.email),t.append("password",e.password),t.append("address",e.address),e.documents&&e.documents.length>0)for(let n=0;n<e.documents.length;n++)t.append("documents",e.documents[n]);console.log("Form data entries:");for(const e of t.entries())console.log(e[0],e[1]);return(await Jr.post("/register/college",t,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(r){throw console.error("College registration error:",r),r}})({name:t.name,email:t.email,password:t.password,address:t.address,documents:t.documents});console.log("Registration successful:",n),e("/verification-pending")}catch(s){var a,l;console.error("Registration error:",s),o((null===(a=s.response)||void 0===a||null===(l=a.data)||void 0===l?void 0:l.message)||"Registration failed. Please try again.")}finally{i(!1)}else o("Passwords do not match")},encType:"multipart/form-data",className:"auth-form",children:[(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"College Name"}),(0,kt.jsx)("input",{type:"text",name:"name",value:t.name,onChange:s,placeholder:"Enter college name",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Email Address"}),(0,kt.jsx)("input",{type:"email",name:"email",value:t.email,onChange:s,placeholder:"Enter official email",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Password"}),(0,kt.jsx)("input",{type:"password",name:"password",value:t.password,onChange:s,placeholder:"Create a password",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Confirm Password"}),(0,kt.jsx)("input",{type:"password",name:"confirmPassword",value:t.confirmPassword,onChange:s,placeholder:"Confirm your password",required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Address"}),(0,kt.jsx)("textarea",{name:"address",value:t.address,onChange:s,placeholder:"Enter complete college address",rows:3,required:!0})]}),(0,kt.jsxs)("div",{className:"form-group",children:[(0,kt.jsx)("label",{children:"Verification Documents"}),(0,kt.jsx)("input",{type:"file",name:"documents",multiple:!0,onChange:e=>{e.target.files&&n(d(d({},t),{},{documents:Array.from(e.target.files)}))},accept:".pdf,.jpg,.jpeg,.png,.doc,.docx",required:!0}),(0,kt.jsx)("p",{style:{fontSize:"0.85rem",color:"#7f8c8d",marginTop:"0.5rem"},children:"Please upload documents that verify your college's authenticity (PDF, Images, or Documents)"})]}),(0,kt.jsx)("button",{type:"submit",disabled:l,className:"auth-btn",children:l?"Registering...":"Register College"})]}),(0,kt.jsxs)("div",{className:"auth-footer",children:["Already have an account? ",(0,kt.jsx)("a",{href:"/login",className:"auth-link",children:"Login here"})]})]})})},ea=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)(!1),[o,l]=(0,a.useState)(""),[i,s]=(0,a.useState)(""),u=async()=>{try{r(!0);const e=await Kr.get("http://localhost:5000/api/auth/unverified-colleges");t(e.data.colleges)}catch(e){console.error("Error fetching colleges:",e),l("Error fetching unverified colleges")}finally{r(!1)}};return(0,a.useEffect)((()=>{u()}),[]),(0,kt.jsx)("div",{className:"auth-container",children:(0,kt.jsxs)("div",{className:"auth-box",style:{maxWidth:"800px"},children:[(0,kt.jsxs)("div",{className:"auth-header",children:[(0,kt.jsx)("h1",{children:"Admin Panel"}),(0,kt.jsx)("p",{children:"College Verification Management"})]}),o&&(0,kt.jsx)("div",{className:o.includes("Error")?"error-message":"success-message",children:o}),(0,kt.jsxs)("div",{className:"admin-section",children:[(0,kt.jsx)("h3",{children:"Quick Verify by Email"}),(0,kt.jsxs)("div",{className:"admin-form",children:[(0,kt.jsx)("input",{type:"email",placeholder:"Enter college email",value:i,onChange:e=>s(e.target.value),className:"admin-input"}),(0,kt.jsx)("button",{onClick:async()=>{try{await Kr.post("http://localhost:5000/api/auth/verify-by-email",{email:i}),l("College verified successfully by email!"),s(""),u()}catch(e){console.error("Error verifying college:",e),l("Error verifying college by email")}},disabled:!i,className:"auth-btn",style:{maxWidth:"120px"},children:"Verify"})]})]}),(0,kt.jsxs)("div",{className:"admin-section",children:[(0,kt.jsx)("h3",{children:"Unverified Colleges"}),(0,kt.jsx)("button",{onClick:u,className:"auth-btn",style:{marginBottom:"20px",maxWidth:"150px"},children:"Refresh List"}),n?(0,kt.jsx)("p",{children:"Loading..."}):0===e.length?(0,kt.jsx)("p",{children:"No unverified colleges found."}):(0,kt.jsx)("div",{className:"admin-colleges-list",children:e.map((e=>(0,kt.jsxs)("div",{className:"admin-college-card",children:[(0,kt.jsx)("h4",{children:e.name}),(0,kt.jsxs)("p",{children:[(0,kt.jsx)("strong",{children:"Email:"})," ",e.email]}),(0,kt.jsxs)("p",{children:[(0,kt.jsx)("strong",{children:"Address:"})," ",e.address]}),(0,kt.jsxs)("p",{children:[(0,kt.jsx)("strong",{children:"Documents:"})," ",e.verificationDocuments.length," files uploaded"]}),(0,kt.jsxs)("p",{children:[(0,kt.jsx)("strong",{children:"Registered:"})," ",new Date(e.createdAt).toLocaleDateString()]}),(0,kt.jsx)("button",{onClick:()=>(async e=>{try{await Kr.put("http://localhost:5000/api/auth/verify/".concat(e)),l("College verified successfully!"),u()}catch(t){console.error("Error verifying college:",t),l("Error verifying college")}})(e._id),className:"auth-btn",style:{maxWidth:"150px",marginTop:"10px"},children:"Verify College"})]},e._id)))})]})]})})};function ta(){const e="/"===fe().pathname;return(0,kt.jsxs)("div",{className:"app-container",children:[!e&&(0,kt.jsx)("header",{className:"header",children:(0,kt.jsxs)("div",{className:"header-content",children:[(0,kt.jsx)(mt,{to:"/",className:"logo",children:(0,kt.jsx)("h1",{children:"College Events Platform"})}),(0,kt.jsx)("nav",{children:(0,kt.jsxs)("ul",{children:[(0,kt.jsx)("li",{children:(0,kt.jsx)(mt,{to:"/",children:"Home"})}),(0,kt.jsx)("li",{children:(0,kt.jsx)(mt,{to:"/events",children:"Events"})}),(0,kt.jsx)("li",{children:(0,kt.jsx)(mt,{to:"/login",children:"Login"})}),(0,kt.jsx)("li",{children:(0,kt.jsx)(mt,{to:"/register",children:"Register"})}),(0,kt.jsx)("li",{children:(0,kt.jsx)(mt,{to:"/admin",children:"Admin"})})]})})]})}),(0,kt.jsx)("main",{className:e?"main-home":"main",children:(0,kt.jsxs)(_e,{children:[(0,kt.jsx)(Te,{path:"/",element:(0,kt.jsx)(xt,{})}),(0,kt.jsx)(Te,{path:"/login",element:(0,kt.jsx)(Yr,{})}),(0,kt.jsx)(Te,{path:"/register",element:(0,kt.jsx)(Xr,{})}),(0,kt.jsx)(Te,{path:"/register/student",element:(0,kt.jsx)(Gr,{})}),(0,kt.jsx)(Te,{path:"/register/college",element:(0,kt.jsx)(Zr,{})}),(0,kt.jsx)(Te,{path:"/admin",element:(0,kt.jsx)(ea,{})}),(0,kt.jsx)(Te,{path:"/events",element:(0,kt.jsx)("div",{children:"Events page coming soon..."})})]})}),!e&&(0,kt.jsx)("footer",{className:"footer",children:(0,kt.jsx)("p",{children:"\xa9 2025 College Events Platform. All rights reserved."})})]})}const na=function(){return(0,kt.jsx)(pt,{children:(0,kt.jsx)(ta,{})})};o.createRoot(document.getElementById("root")).render((0,kt.jsx)(a.StrictMode,{children:(0,kt.jsx)(na,{})}))})();
//# sourceMappingURL=main.fdc4093e.js.map