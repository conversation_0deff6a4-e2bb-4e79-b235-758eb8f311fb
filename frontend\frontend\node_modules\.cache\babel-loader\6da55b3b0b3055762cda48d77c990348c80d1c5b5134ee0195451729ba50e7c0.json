{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student'\n  });\n  const [error, setError] = useState('');\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Login failed. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-container\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'red',\n          marginBottom: '15px'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"userType\",\n            children: \"I am a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"userType\",\n            name: \"userType\",\n            value: formData.userType,\n            onChange: handleChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"student\",\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"college\",\n              children: \"College\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"PbvND6DChikgLkMFxrNMRMgRKlU=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "login", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "formData", "setFormData", "email", "password", "userType", "error", "setError", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "err", "_err$response", "_err$response$data", "response", "data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "marginBottom", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\n\nconst Login: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student' as 'student' | 'college'\n  });\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>Login</h1>\n      <div className=\"form-container\">\n        {error && <div style={{ color: 'red', marginBottom: '15px' }}>{error}</div>}\n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"userType\">I am a</label>\n            <select\n              id=\"userType\"\n              name=\"userType\"\n              value={formData.userType}\n              onChange={handleChange}\n              required\n            >\n              <option value=\"student\">Student</option>\n              <option value=\"college\">College</option>\n            </select>\n          </div>\n          <button type=\"submit\" className=\"btn btn-primary\">Login</button>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,YAAY,GAAIC,CAA0D,IAAK;IACnFP,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACQ,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAkB,IAAK;IACjDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBP,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMZ,KAAK,CAACM,QAAQ,CAAC;MACrBD,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBV,QAAQ,CAAC,EAAAS,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBG,OAAO,KAAI,iCAAiC,CAAC;IAC5E;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzB,OAAA;MAAAyB,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACd7B,OAAA;MAAKwB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC5BhB,KAAK,iBAAIT,OAAA;QAAK8B,KAAK,EAAE;UAAEC,KAAK,EAAE,KAAK;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAEhB;MAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3E7B,OAAA;QAAMiC,QAAQ,EAAEjB,YAAa;QAAAS,QAAA,gBAC3BzB,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzB,OAAA;YAAOkC,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpC7B,OAAA;YACEmC,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVtB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEX,QAAQ,CAACE,KAAM;YACtB+B,QAAQ,EAAE1B,YAAa;YACvB2B,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzB,OAAA;YAAOkC,OAAO,EAAC,UAAU;YAAAT,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C7B,OAAA;YACEmC,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbtB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEX,QAAQ,CAACG,QAAS;YACzB8B,QAAQ,EAAE1B,YAAa;YACvB2B,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzB,OAAA;YAAOkC,OAAO,EAAC,UAAU;YAAAT,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxC7B,OAAA;YACEoC,EAAE,EAAC,UAAU;YACbtB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEX,QAAQ,CAACI,QAAS;YACzB6B,QAAQ,EAAE1B,YAAa;YACvB2B,QAAQ;YAAAb,QAAA,gBAERzB,OAAA;cAAQe,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7B,OAAA;cAAQe,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7B,OAAA;UAAQmC,IAAI,EAAC,QAAQ;UAACX,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA1EID,KAAe;EAAA,QACFJ,WAAW;AAAA;AAAA0C,EAAA,GADxBtC,KAAe;AA4ErB,eAAeA,KAAK;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}