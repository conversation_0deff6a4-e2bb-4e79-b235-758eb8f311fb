{"version": 3, "file": "compile.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/nth-check/639fd2a4000b69f82350aad8c34cb43f77e483ba/src/", "sources": ["compile.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAAgC;AAEhC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAgB,OAAO,CACnB,MAA8B;IAE9B,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,6DAA6D;IAC7D,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAExB;;;;;;OAMG;IACH,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;IAE/C,mFAAmF;IACnF,IAAI,CAAC,KAAK,CAAC,CAAC;QAAE,OAAO,UAAC,KAAK,IAAK,OAAA,KAAK,IAAI,CAAC,EAAV,CAAU,CAAC;IAC3C,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,UAAC,KAAK,IAAK,OAAA,KAAK,KAAK,CAAC,EAAX,CAAW,CAAC;IAC3C,uDAAuD;IACvD,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,IAAI,CAAC,EAAV,CAAU,CAAC;IAEtE;;;;OAIG;IACH,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,0CAA0C;IAC1C,IAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAExC,OAAO,CAAC,GAAG,CAAC;QACR,CAAC,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,EAAnC,CAAmC;QAChD,CAAC,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,EAAnC,CAAmC,CAAC;AACzD,CAAC;AAlCD,0BAkCC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,SAAgB,QAAQ,CAAC,MAA8B;IACnD,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,6DAA6D;IAC7D,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtB,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,oDAAoD;IACpD,IAAI,CAAC,GAAG,CAAC,EAAE;QACP,IAAM,MAAI,GAAG,CAAC,CAAC,CAAC;QAChB,gBAAgB;QAChB,IAAM,UAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,MAAI,CAAC,GAAG,MAAI,CAAC,GAAG,MAAI,CAAC;QAC5C,OAAO;YACH,IAAM,GAAG,GAAG,UAAQ,GAAG,MAAI,GAAG,CAAC,EAAE,CAAC;YAElC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;QAChC,CAAC,CAAC;KACL;IAED,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,GAAG,CAAC;YACR,CAAC,CAAC,6CAA6C;gBAC7C,cAAM,OAAA,IAAI,EAAJ,CAAI;YACZ,CAAC,CAAC,0BAA0B;gBAC1B,cAAM,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAtB,CAAsB,CAAC;IAEvC,IAAI,CAAC,GAAG,CAAC,EAAE;QACP,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9B;IAED,OAAO,cAAM,OAAA,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAX,CAAW,CAAC;AAC7B,CAAC;AA/BD,4BA+BC"}