{"ast": null, "code": "import React from'react';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(\"header\",{className:\"App-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"College Events Platform\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Welcome to the College Events Platform\"})]})});}export default App;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "App", "className", "children"], "sources": ["C:/Users/<USER>/workuuu/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>College Events Platform</h1>\n        <p>Welcome to the College Events Platform</p>\n      </header>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAKI,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,KAAA,WAAQE,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC5BL,IAAA,OAAAK,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCL,IAAA,MAAAK,QAAA,CAAG,wCAAsC,CAAG,CAAC,EACvC,CAAC,CACN,CAAC,CAEV,CAEA,cAAe,CAAAF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}