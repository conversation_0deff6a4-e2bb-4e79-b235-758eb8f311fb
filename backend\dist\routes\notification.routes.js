"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const notification_controller_1 = require("../controllers/notification.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = express_1.default.Router();
// Notification routes (protected)
router.post('/', auth_middleware_1.authMiddleware, notification_controller_1.createNotification);
router.get('/', auth_middleware_1.authMiddleware, notification_controller_1.getCollegeNotifications);
router.post('/event/:eventId', auth_middleware_1.authMiddleware, notification_controller_1.notifyEventParticipants);
router.get('/analytics', auth_middleware_1.authMiddleware, notification_controller_1.getNotificationAnalytics);
router.put('/:notificationId/read', notification_controller_1.markNotificationAsRead);
router.put('/:notificationId/click', notification_controller_1.trackNotificationClick);
exports.default = router;
