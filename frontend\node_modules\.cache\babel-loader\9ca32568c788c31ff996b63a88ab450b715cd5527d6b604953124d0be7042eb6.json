{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import{BrowserRouter}from'react-router-dom';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(App,{})})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/Users/<USER>/workuuu/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { BrowserRouter } from 'react-router-dom';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <App />\n    </BrowserRouter>\n  </React.StrictMode>\n);\n\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,aAAa,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEjD,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC,CAEDH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACL,KAAK,CAACW,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,aAAa,EAAAS,QAAA,cACZP,IAAA,CAACH,GAAG,GAAE,CAAC,CACM,CAAC,CACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}