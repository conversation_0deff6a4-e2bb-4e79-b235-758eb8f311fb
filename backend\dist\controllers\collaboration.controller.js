"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPotentialPartners = exports.getCollaborationAnalytics = exports.respondToCollaborationRequest = exports.getCollaborationRequests = exports.sendCollaborationRequest = void 0;
const Collaboration_1 = __importDefault(require("../models/Collaboration"));
const College_1 = __importDefault(require("../models/College"));
const Event_1 = __importDefault(require("../models/Event"));
const Notification_1 = __importDefault(require("../models/Notification"));
// Send collaboration request
const sendCollaborationRequest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { requesteeId, eventId, proposedEvent, message, terms } = req.body;
        // @ts-ignore
        const requesterId = req.college.id;
        // Check if requestee college exists
        const requesteeCollege = yield College_1.default.findById(requesteeId);
        if (!requesteeCollege) {
            return res.status(404).json({ message: 'Requestee college not found' });
        }
        // Check if collaboration already exists
        const existingCollaboration = yield Collaboration_1.default.findOne({
            requester: requesterId,
            requestee: requesteeId,
            status: 'pending'
        });
        if (existingCollaboration) {
            return res.status(400).json({
                message: 'Collaboration request already pending with this college'
            });
        }
        const collaboration = new Collaboration_1.default({
            requester: requesterId,
            requestee: requesteeId,
            eventId,
            proposedEvent,
            message,
            terms,
            status: 'pending'
        });
        yield collaboration.save();
        // Update college collaboration requests
        yield College_1.default.findByIdAndUpdate(requesterId, {
            $addToSet: { 'collaborationRequests.sent': collaboration._id }
        });
        yield College_1.default.findByIdAndUpdate(requesteeId, {
            $addToSet: { 'collaborationRequests.received': collaboration._id }
        });
        // Send notification to requestee
        const notification = new Notification_1.default({
            title: 'New Collaboration Request',
            message: `You have received a collaboration request from ${req.college.name}`,
            type: 'collaboration_request',
            recipients: {
                students: [],
                colleges: [requesteeId]
            },
            sender: requesterId,
            status: 'sent',
            sentAt: new Date()
        });
        yield notification.save();
        res.status(201).json({
            success: true,
            message: 'Collaboration request sent successfully',
            collaboration
        });
    }
    catch (error) {
        console.error('Send collaboration request error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.sendCollaborationRequest = sendCollaborationRequest;
// Get collaboration requests (sent and received)
const getCollaborationRequests = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const { type } = req.query; // 'sent' or 'received'
        let query = {};
        if (type === 'sent') {
            query.requester = collegeId;
        }
        else if (type === 'received') {
            query.requestee = collegeId;
        }
        else {
            query = {
                $or: [
                    { requester: collegeId },
                    { requestee: collegeId }
                ]
            };
        }
        const collaborations = yield Collaboration_1.default.find(query)
            .populate('requester', 'name email')
            .populate('requestee', 'name email')
            .populate('eventId', 'title startDate')
            .sort({ createdAt: -1 });
        res.json({
            success: true,
            collaborations
        });
    }
    catch (error) {
        console.error('Get collaboration requests error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollaborationRequests = getCollaborationRequests;
// Respond to collaboration request
const respondToCollaborationRequest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { collaborationId } = req.params;
        const { status, response } = req.body; // 'accepted' or 'rejected'
        // @ts-ignore
        const collegeId = req.college.id;
        const collaboration = yield Collaboration_1.default.findById(collaborationId);
        if (!collaboration) {
            return res.status(404).json({ message: 'Collaboration request not found' });
        }
        // Check if the college is the requestee
        if (collaboration.requestee.toString() !== collegeId) {
            return res.status(403).json({ message: 'Not authorized to respond to this request' });
        }
        // Check if already responded
        if (collaboration.status !== 'pending') {
            return res.status(400).json({ message: 'Request already responded to' });
        }
        collaboration.status = status;
        collaboration.response = response;
        collaboration.respondedAt = new Date();
        yield collaboration.save();
        // If accepted, add to event collaborators
        if (status === 'accepted' && collaboration.eventId) {
            yield Event_1.default.findByIdAndUpdate(collaboration.eventId, {
                $addToSet: { collaborators: collegeId }
            });
        }
        // Send notification to requester
        const notification = new Notification_1.default({
            title: `Collaboration Request ${status.charAt(0).toUpperCase() + status.slice(1)}`,
            message: `Your collaboration request has been ${status}`,
            type: 'collaboration_request',
            recipients: {
                students: [],
                colleges: [collaboration.requester]
            },
            sender: collegeId,
            status: 'sent',
            sentAt: new Date()
        });
        yield notification.save();
        res.json({
            success: true,
            message: `Collaboration request ${status}`,
            collaboration
        });
    }
    catch (error) {
        console.error('Respond to collaboration request error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.respondToCollaborationRequest = respondToCollaborationRequest;
// Get collaboration analytics
const getCollaborationAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const sentRequests = yield Collaboration_1.default.countDocuments({ requester: collegeId });
        const receivedRequests = yield Collaboration_1.default.countDocuments({ requestee: collegeId });
        const acceptedSent = yield Collaboration_1.default.countDocuments({
            requester: collegeId,
            status: 'accepted'
        });
        const acceptedReceived = yield Collaboration_1.default.countDocuments({
            requestee: collegeId,
            status: 'accepted'
        });
        const activeCollaborations = yield Collaboration_1.default.countDocuments({
            $or: [
                { requester: collegeId },
                { requestee: collegeId }
            ],
            status: 'accepted'
        });
        res.json({
            success: true,
            analytics: {
                sentRequests,
                receivedRequests,
                acceptedSent,
                acceptedReceived,
                activeCollaborations,
                successRate: sentRequests > 0 ? (acceptedSent / sentRequests) * 100 : 0
            }
        });
    }
    catch (error) {
        console.error('Get collaboration analytics error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollaborationAnalytics = getCollaborationAnalytics;
// Get potential collaboration partners
const getPotentialPartners = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        // Get colleges that are verified and not the current college
        const potentialPartners = yield College_1.default.find({
            _id: { $ne: collegeId },
            isVerified: true
        }).select('name email address departments totalStudents establishedYear');
        res.json({
            success: true,
            partners: potentialPartners
        });
    }
    catch (error) {
        console.error('Get potential partners error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getPotentialPartners = getPotentialPartners;
