import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface College {
  _id: string;
  name: string;
  email: string;
  address: string;
  verificationDocuments: string[];
  isVerified: boolean;
  createdAt: string;
}

const Admin: React.FC = () => {
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [emailToVerify, setEmailToVerify] = useState('');

  const fetchUnverifiedColleges = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/auth/unverified-colleges');
      setColleges(response.data.colleges);
    } catch (error) {
      console.error('Error fetching colleges:', error);
      setMessage('Error fetching unverified colleges');
    } finally {
      setLoading(false);
    }
  };

  const verifyCollegeById = async (id: string) => {
    try {
      await axios.put(`http://localhost:5000/api/auth/verify/${id}`);
      setMessage('College verified successfully!');
      fetchUnverifiedColleges(); // Refresh the list
    } catch (error) {
      console.error('Error verifying college:', error);
      setMessage('Error verifying college');
    }
  };

  const verifyCollegeByEmail = async () => {
    try {
      await axios.post('http://localhost:5000/api/auth/verify-by-email', {
        email: emailToVerify
      });
      setMessage('College verified successfully by email!');
      setEmailToVerify('');
      fetchUnverifiedColleges(); // Refresh the list
    } catch (error) {
      console.error('Error verifying college:', error);
      setMessage('Error verifying college by email');
    }
  };

  useEffect(() => {
    fetchUnverifiedColleges();
  }, []);

  return (
    <div className="auth-container">
      <div className="auth-box" style={{ maxWidth: '800px' }}>
        <div className="auth-header">
          <h1>Admin Panel</h1>
          <p>College Verification Management</p>
        </div>

        {message && (
          <div className={message.includes('Error') ? 'error-message' : 'success-message'}>
            {message}
          </div>
        )}

        {/* Quick verification by email */}
        <div className="admin-section">
          <h3>Quick Verify by Email</h3>
          <div className="admin-form">
            <input
              type="email"
              placeholder="Enter college email"
              value={emailToVerify}
              onChange={(e) => setEmailToVerify(e.target.value)}
              className="admin-input"
            />
            <button
              onClick={verifyCollegeByEmail}
              disabled={!emailToVerify}
              className="auth-btn"
              style={{ maxWidth: '120px' }}
            >
              Verify
            </button>
          </div>
        </div>

        {/* List of unverified colleges */}
        <div className="admin-section">
          <h3>Unverified Colleges</h3>
          <button
            onClick={fetchUnverifiedColleges}
            className="auth-btn"
            style={{ marginBottom: '20px', maxWidth: '150px' }}
          >
            Refresh List
          </button>

          {loading ? (
            <p>Loading...</p>
          ) : colleges.length === 0 ? (
            <p>No unverified colleges found.</p>
          ) : (
            <div className="admin-colleges-list">
              {colleges.map((college) => (
                <div key={college._id} className="admin-college-card">
                  <h4>{college.name}</h4>
                  <p><strong>Email:</strong> {college.email}</p>
                  <p><strong>Address:</strong> {college.address}</p>
                  <p><strong>Documents:</strong> {college.verificationDocuments.length} files uploaded</p>
                  <p><strong>Registered:</strong> {new Date(college.createdAt).toLocaleDateString()}</p>

                  <button
                    onClick={() => verifyCollegeById(college._id)}
                    className="auth-btn"
                    style={{ maxWidth: '150px', marginTop: '10px' }}
                  >
                    Verify College
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Admin;
