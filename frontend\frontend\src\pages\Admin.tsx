import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface College {
  _id: string;
  name: string;
  email: string;
  address: string;
  verificationDocuments: string[];
  isVerified: boolean;
  createdAt: string;
}

const Admin: React.FC = () => {
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [emailToVerify, setEmailToVerify] = useState('');

  const fetchUnverifiedColleges = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/auth/unverified-colleges');
      setColleges(response.data.colleges);
    } catch (error) {
      console.error('Error fetching colleges:', error);
      setMessage('Error fetching unverified colleges');
    } finally {
      setLoading(false);
    }
  };

  const verifyCollegeById = async (id: string) => {
    try {
      await axios.put(`http://localhost:5000/api/auth/verify/${id}`);
      setMessage('College verified successfully!');
      fetchUnverifiedColleges(); // Refresh the list
    } catch (error) {
      console.error('Error verifying college:', error);
      setMessage('Error verifying college');
    }
  };

  const verifyCollegeByEmail = async () => {
    try {
      await axios.post('http://localhost:5000/api/auth/verify-by-email', {
        email: emailToVerify
      });
      setMessage('College verified successfully by email!');
      setEmailToVerify('');
      fetchUnverifiedColleges(); // Refresh the list
    } catch (error) {
      console.error('Error verifying college:', error);
      setMessage('Error verifying college by email');
    }
  };

  useEffect(() => {
    fetchUnverifiedColleges();
  }, []);

  return (
    <div className="container">
      <h1>Admin - College Verification</h1>
      
      {message && (
        <div style={{ 
          padding: '10px', 
          marginBottom: '20px', 
          backgroundColor: message.includes('Error') ? '#ffebee' : '#e8f5e8',
          color: message.includes('Error') ? '#c62828' : '#2e7d32',
          borderRadius: '4px'
        }}>
          {message}
        </div>
      )}

      {/* Quick verification by email */}
      <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
        <h3>Quick Verify by Email</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <input
            type="email"
            placeholder="Enter college email"
            value={emailToVerify}
            onChange={(e) => setEmailToVerify(e.target.value)}
            style={{ flex: 1, padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
          />
          <button
            onClick={verifyCollegeByEmail}
            disabled={!emailToVerify}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Verify
          </button>
        </div>
      </div>

      {/* List of unverified colleges */}
      <div>
        <h3>Unverified Colleges</h3>
        <button 
          onClick={fetchUnverifiedColleges}
          style={{
            marginBottom: '20px',
            padding: '8px 16px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh List
        </button>

        {loading ? (
          <p>Loading...</p>
        ) : colleges.length === 0 ? (
          <p>No unverified colleges found.</p>
        ) : (
          <div>
            {colleges.map((college) => (
              <div key={college._id} style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px',
                backgroundColor: '#f9f9f9'
              }}>
                <h4>{college.name}</h4>
                <p><strong>Email:</strong> {college.email}</p>
                <p><strong>Address:</strong> {college.address}</p>
                <p><strong>Documents:</strong> {college.verificationDocuments.length} files uploaded</p>
                <p><strong>Registered:</strong> {new Date(college.createdAt).toLocaleDateString()}</p>
                
                <button
                  onClick={() => verifyCollegeById(college._id)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Verify College
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Admin;
