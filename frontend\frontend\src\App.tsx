import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import './App.css';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import StudentSignup from './components/auth/StudentSignup';
import CollegeSignup from './components/auth/CollegeSignup';
import Admin from './pages/Admin';

function AppContent() {
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  return (
    <div className="app-container">
      {!isHomePage && (
        <header className="header">
          <div className="header-content">
            <Link to="/" className="logo">
              <h1>College Events Platform</h1>
            </Link>
            <nav>
              <ul>
                <li><Link to="/">Home</Link></li>
                <li><Link to="/events">Events</Link></li>
                <li><Link to="/login">Login</Link></li>
                <li><Link to="/register">Register</Link></li>
                <li><Link to="/admin">Admin</Link></li>
              </ul>
            </nav>
          </div>
        </header>
      )}

      <main className={isHomePage ? "main-home" : "main"}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/register/student" element={<StudentSignup />} />
          <Route path="/register/college" element={<CollegeSignup />} />
          <Route path="/admin" element={<Admin />} />
          <Route path="/events" element={<div>Events page coming soon...</div>} />
        </Routes>
      </main>

      {!isHomePage && (
        <footer className="footer">
          <p>&copy; 2025 College Events Platform. All rights reserved.</p>
        </footer>
      )}
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;
