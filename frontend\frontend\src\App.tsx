import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import StudentSignup from './components/auth/StudentSignup';
import CollegeSignup from './components/auth/CollegeSignup';

function App() {
  return (
    <Router>
      <div className="app-container">
        <header className="header">
          <h1>College Events Platform</h1>
          <nav>
            <ul>
              <li><Link to="/">Home</Link></li>
              <li><Link to="/events">Events</Link></li>
              <li><Link to="/login">Login</Link></li>
              <li><Link to="/register">Register</Link></li>
            </ul>
          </nav>
        </header>

        <main className="main">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/register/student" element={<StudentSignup />} />
            <Route path="/register/college" element={<CollegeSignup />} />
            <Route path="/events" element={<div>Events page coming soon...</div>} />
          </Routes>
        </main>

        <footer className="footer">
          <p>&copy; 2023 College Events Platform</p>
        </footer>
      </div>
    </Router>
  );
}

export default App;
