{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Link,useLocation}from'react-router-dom';import'./App.css';import Home from'./pages/Home';import Login from'./pages/Login';import Register from'./pages/Register';import StudentSignup from'./components/auth/StudentSignup';import CollegeSignup from'./components/auth/CollegeSignup';import Admin from'./pages/Admin';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AppContent(){const location=useLocation();const isHomePage=location.pathname==='/';return/*#__PURE__*/_jsxs(\"div\",{className:\"app-container\",children:[!isHomePage&&/*#__PURE__*/_jsx(\"header\",{className:\"header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"header-content\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"logo\",children:/*#__PURE__*/_jsx(\"h1\",{children:\"College Events Platform\"})}),/*#__PURE__*/_jsx(\"nav\",{children:/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/\",children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/events\",children:\"Events\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/login\",children:\"Login\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/register\",children:\"Register\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/admin\",children:\"Admin\"})})]})})]})}),/*#__PURE__*/_jsx(\"main\",{className:isHomePage?\"main-home\":\"main\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register/student\",element:/*#__PURE__*/_jsx(StudentSignup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register/college\",element:/*#__PURE__*/_jsx(CollegeSignup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(Admin,{})}),/*#__PURE__*/_jsx(Route,{path:\"/events\",element:/*#__PURE__*/_jsx(\"div\",{children:\"Events page coming soon...\"})})]})}),!isHomePage&&/*#__PURE__*/_jsx(\"footer\",{className:\"footer\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2025 College Events Platform. All rights reserved.\"})})]});}function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(AppContent,{})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "Home", "<PERSON><PERSON>", "Register", "StudentSignup", "CollegeSignup", "Admin", "jsx", "_jsx", "jsxs", "_jsxs", "A<PERSON><PERSON><PERSON>nt", "location", "isHomePage", "pathname", "className", "children", "to", "path", "element", "App"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport './App.css';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport StudentSignup from './components/auth/StudentSignup';\nimport CollegeSignup from './components/auth/CollegeSignup';\nimport Admin from './pages/Admin';\n\nfunction AppContent() {\n  const location = useLocation();\n  const isHomePage = location.pathname === '/';\n\n  return (\n    <div className=\"app-container\">\n      {!isHomePage && (\n        <header className=\"header\">\n          <div className=\"header-content\">\n            <Link to=\"/\" className=\"logo\">\n              <h1>College Events Platform</h1>\n            </Link>\n            <nav>\n              <ul>\n                <li><Link to=\"/\">Home</Link></li>\n                <li><Link to=\"/events\">Events</Link></li>\n                <li><Link to=\"/login\">Login</Link></li>\n                <li><Link to=\"/register\">Register</Link></li>\n                <li><Link to=\"/admin\">Admin</Link></li>\n              </ul>\n            </nav>\n          </div>\n        </header>\n      )}\n\n      <main className={isHomePage ? \"main-home\" : \"main\"}>\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/register\" element={<Register />} />\n          <Route path=\"/register/student\" element={<StudentSignup />} />\n          <Route path=\"/register/college\" element={<CollegeSignup />} />\n          <Route path=\"/admin\" element={<Admin />} />\n          <Route path=\"/events\" element={<div>Events page coming soon...</div>} />\n        </Routes>\n      </main>\n\n      {!isHomePage && (\n        <footer className=\"footer\">\n          <p>&copy; 2025 College Events Platform. All rights reserved.</p>\n        </footer>\n      )}\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CAC5F,MAAO,WAAW,CAClB,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,KAAK,KAAM,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElC,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,UAAU,CAAGD,QAAQ,CAACE,QAAQ,GAAK,GAAG,CAE5C,mBACEJ,KAAA,QAAKK,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3B,CAACH,UAAU,eACVL,IAAA,WAAQO,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACxBN,KAAA,QAAKK,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,MAAM,CAAAC,QAAA,cAC3BR,IAAA,OAAAQ,QAAA,CAAI,yBAAuB,CAAI,CAAC,CAC5B,CAAC,cACPR,IAAA,QAAAQ,QAAA,cACEN,KAAA,OAAAM,QAAA,eACER,IAAA,OAAAQ,QAAA,cAAIR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,GAAG,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAI,CAAC,cACjCR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,SAAS,CAAAD,QAAA,CAAC,QAAM,CAAM,CAAC,CAAI,CAAC,cACzCR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,QAAQ,CAAAD,QAAA,CAAC,OAAK,CAAM,CAAC,CAAI,CAAC,cACvCR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,WAAW,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,cAC7CR,IAAA,OAAAQ,QAAA,cAAIR,IAAA,CAACT,IAAI,EAACkB,EAAE,CAAC,QAAQ,CAAAD,QAAA,CAAC,OAAK,CAAM,CAAC,CAAI,CAAC,EACrC,CAAC,CACF,CAAC,EACH,CAAC,CACA,CACT,cAEDR,IAAA,SAAMO,SAAS,CAAEF,UAAU,CAAG,WAAW,CAAG,MAAO,CAAAG,QAAA,cACjDN,KAAA,CAACb,MAAM,EAAAmB,QAAA,eACLR,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEX,IAAA,CAACP,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCO,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEX,IAAA,CAACN,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CM,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEX,IAAA,CAACL,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDK,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEX,IAAA,CAACJ,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9DI,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEX,IAAA,CAACH,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9DG,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEX,IAAA,CAACF,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CE,IAAA,CAACV,KAAK,EAACoB,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEX,IAAA,QAAAQ,QAAA,CAAK,4BAA0B,CAAK,CAAE,CAAE,CAAC,EAClE,CAAC,CACL,CAAC,CAEN,CAACH,UAAU,eACVL,IAAA,WAAQO,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACxBR,IAAA,MAAAQ,QAAA,CAAG,yDAAyD,CAAG,CAAC,CAC1D,CACT,EACE,CAAC,CAEV,CAEA,QAAS,CAAAI,GAAGA,CAAA,CAAG,CACb,mBACEZ,IAAA,CAACZ,MAAM,EAAAoB,QAAA,cACLR,IAAA,CAACG,UAAU,GAAE,CAAC,CACR,CAAC,CAEb,CAEA,cAAe,CAAAS,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}