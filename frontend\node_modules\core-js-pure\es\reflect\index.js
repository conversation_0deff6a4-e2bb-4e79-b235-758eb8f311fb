'use strict';
require('../../modules/es.object.to-string');
require('../../modules/es.reflect.apply');
require('../../modules/es.reflect.construct');
require('../../modules/es.reflect.define-property');
require('../../modules/es.reflect.delete-property');
require('../../modules/es.reflect.get');
require('../../modules/es.reflect.get-own-property-descriptor');
require('../../modules/es.reflect.get-prototype-of');
require('../../modules/es.reflect.has');
require('../../modules/es.reflect.is-extensible');
require('../../modules/es.reflect.own-keys');
require('../../modules/es.reflect.prevent-extensions');
require('../../modules/es.reflect.set');
require('../../modules/es.reflect.set-prototype-of');
require('../../modules/es.reflect.to-string-tag');
var path = require('../../internals/path');

module.exports = path.Reflect;
