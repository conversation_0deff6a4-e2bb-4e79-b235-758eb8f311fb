{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///long.js", "webpack:///webpack/bootstrap f96e8d1360c0487f2545", "webpack:///./src/long.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "<PERSON>", "low", "high", "unsigned", "isLong", "obj", "fromInt", "value", "cachedObj", "cache", "UINT_CACHE", "fromBits", "INT_CACHE", "fromNumber", "isNaN", "UZERO", "ZERO", "TWO_PWR_64_DBL", "MAX_UNSIGNED_VALUE", "TWO_PWR_63_DBL", "MIN_VALUE", "MAX_VALUE", "neg", "TWO_PWR_32_DBL", "lowBits", "highBits", "fromString", "str", "radix", "length", "Error", "RangeError", "indexOf", "substring", "radixToPower", "pow_dbl", "result", "size", "Math", "min", "parseInt", "power", "mul", "add", "fromValue", "val", "wasm", "WebAssembly", "Instance", "<PERSON><PERSON><PERSON>", "Uint8Array", "e", "__isLong__", "pow", "TWO_PWR_16_DBL", "TWO_PWR_24", "ONE", "UONE", "NEG_ONE", "LongPrototype", "toInt", "toNumber", "toString", "isZero", "isNegative", "eq", "radixLong", "div", "rem1", "sub", "rem", "remDiv", "intval", "digits", "getHighBits", "getHighBitsUnsigned", "getLowBits", "getLowBitsUnsigned", "getNumBitsAbs", "bit", "eqz", "isPositive", "isOdd", "isEven", "equals", "other", "notEquals", "neq", "ne", "lessThan", "comp", "lt", "lessThanOrEqual", "lte", "le", "greaterThan", "gt", "greaterThanOrEqual", "gte", "ge", "compare", "thisNeg", "otherNeg", "negate", "not", "addend", "a48", "a32", "a16", "a00", "b48", "b32", "b16", "b00", "c48", "c32", "c16", "c00", "subtract", "subtrahend", "multiply", "multiplier", "divide", "divisor", "approx", "res", "toUnsigned", "shru", "shr", "shl", "max", "floor", "log2", "ceil", "log", "LN2", "delta", "approxRes", "approxRem", "modulo", "mod", "and", "or", "xor", "shiftLeft", "numBits", "shiftRight", "shiftRightUnsigned", "shr_u", "rotateLeft", "b", "rotl", "rotateRight", "rotr", "toSigned", "toBytes", "toBytesLE", "toBytesBE", "hi", "lo", "fromBytes", "bytes", "fromBytesLE", "fromBytesBE"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,KAAAD,IAEAD,EAAA,KAAAC,KACC,mBAAAK,WAAAC,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAR,OAGA,IAAAC,GAAAQ,EAAAD,IACAE,EAAAF,EACAG,GAAA,EACAX,WAUA,OANAM,GAAAE,GAAAI,KAAAX,EAAAD,QAAAC,IAAAD,QAAAO,GAGAN,EAAAU,GAAA,EAGAV,EAAAD,QAvBA,GAAAS,KA4DA,OAhCAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,SAAAf,EAAAgB,EAAAC,GACAV,EAAAW,EAAAlB,EAAAgB,IACAG,OAAAC,eAAApB,EAAAgB,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAvB,GACA,GAAAgB,GAAAhB,KAAAwB,WACA,WAA2B,MAAAxB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAM,GAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDpB,EAAAuB,EAAA,GAGAvB,IAAAwB,EAAA,KDgBM,SAAU9B,EAAQD,GEpDxB,QAAAgC,GAAAC,EAAAC,EAAAC,GAMA9B,KAAA4B,IAAA,EAAAA,EAMA5B,KAAA6B,KAAA,EAAAA,EAMA7B,KAAA8B,aAoCA,QAAAC,GAAAC,GACA,YAAAA,KAAA,YA+BA,QAAAC,GAAAC,EAAAJ,GACA,GAAAE,GAAAG,EAAAC,CACA,OAAAN,IACAI,KAAA,GACAE,EAAA,GAAAF,KAAA,OACAC,EAAAE,EAAAH,IAEAC,GAEAH,EAAAM,EAAAJ,GAAA,EAAAA,GAAA,WACAE,IACAC,EAAAH,GAAAF,GACAA,KAEAE,GAAA,GACAE,GAAA,KAAAF,KAAA,OACAC,EAAAI,EAAAL,IAEAC,GAEAH,EAAAM,EAAAJ,IAAA,WACAE,IACAG,EAAAL,GAAAF,GACAA,IAmBA,QAAAQ,GAAAN,EAAAJ,GACA,GAAAW,MAAAP,GACA,MAAAJ,GAAAY,EAAAC,CACA,IAAAb,EAAA,CACA,GAAAI,EAAA,EACA,MAAAQ,EACA,IAAAR,GAAAU,EACA,MAAAC,OACK,CACL,GAAAX,IAAAY,EACA,MAAAC,EACA,IAAAb,EAAA,GAAAY,EACA,MAAAE,GAEA,MAAAd,GAAA,EACAM,GAAAN,EAAAJ,GAAAmB,MACAX,EAAAJ,EAAAgB,EAAA,EAAAhB,EAAAgB,EAAA,EAAApB,GAmBA,QAAAQ,GAAAa,EAAAC,EAAAtB,GACA,UAAAH,GAAAwB,EAAAC,EAAAtB,GA8BA,QAAAuB,GAAAC,EAAAxB,EAAAyB,GACA,OAAAD,EAAAE,OACA,KAAAC,OAAA,eACA,YAAAH,GAAA,aAAAA,GAAA,cAAAA,GAAA,cAAAA,EACA,MAAAX,EASA,IARA,gBAAAb,IAEAyB,EAAAzB,EACAA,GAAA,GAEAA,OAEAyB,KAAA,IACA,MAAAA,EACA,KAAAG,YAAA,QAEA,IAAAjC,EACA,KAAAA,EAAA6B,EAAAK,QAAA,QACA,KAAAF,OAAA,kBACA,QAAAhC,EACA,MAAA4B,GAAAC,EAAAM,UAAA,GAAA9B,EAAAyB,GAAAN,KAQA,QAHAY,GAAArB,EAAAsB,EAAAP,EAAA,IAEAQ,EAAApB,EACAtC,EAAA,EAAmBA,EAAAiD,EAAAE,OAAgBnD,GAAA,GACnC,GAAA2D,GAAAC,KAAAC,IAAA,EAAAZ,EAAAE,OAAAnD,GACA6B,EAAAiC,SAAAb,EAAAM,UAAAvD,IAAA2D,GAAAT,EACA,IAAAS,EAAA,GACA,GAAAI,GAAA5B,EAAAsB,EAAAP,EAAAS,GACAD,KAAAM,IAAAD,GAAAE,IAAA9B,EAAAN,QAEA6B,KAAAM,IAAAR,GACAE,IAAAO,IAAA9B,EAAAN,IAIA,MADA6B,GAAAjC,WACAiC,EAoBA,QAAAQ,GAAAC,EAAA1C,GACA,sBAAA0C,GACAhC,EAAAgC,EAAA1C,GACA,gBAAA0C,GACAnB,EAAAmB,EAAA1C,GAEAQ,EAAAkC,EAAA5C,IAAA4C,EAAA3C,KAAA,iBAAAC,KAAA0C,EAAA1C,UA7RAlC,EAAAD,QAAAgC,CAKA,IAAA8C,GAAA,IAEA,KACAA,EAAA,GAAAC,aAAAC,SAAA,GAAAD,aAAAE,OAAA,GAAAC,aACA,u2BACSlF,QACR,MAAAmF,IA0DDnD,EAAAJ,UAAAwD,WAEAjE,OAAAC,eAAAY,EAAAJ,UAAA,cAAqDW,OAAA,IAkBrDP,EAAAI,QAOA,IAAAQ,MAOAF,IA0CAV,GAAAM,UAkCAN,EAAAa,aAsBAb,EAAAW,UASA,IAAAwB,GAAAG,KAAAe,GA4DArD,GAAA0B,aAyBA1B,EAAA4C,WAUA,IAcArB,GAAA+B,WAOArC,EAAAM,IAOAJ,EAAAF,EAAA,EAOAsC,EAAAjD,EA5BA,OAkCAU,EAAAV,EAAA,EAMAN,GAAAgB,MAMA,IAAAD,GAAAT,EAAA,KAMAN,GAAAe,OAMA,IAAAyC,GAAAlD,EAAA,EAMAN,GAAAwD,KAMA,IAAAC,GAAAnD,EAAA,KAMAN,GAAAyD,MAMA,IAAAC,GAAApD,GAAA,EAMAN,GAAA0D,SAMA,IAAArC,GAAAV,GAAA,gBAMAX,GAAAqB,WAMA,IAAAH,GAAAP,GAAA,QAMAX,GAAAkB,oBAMA,IAAAE,GAAAT,EAAA,iBAMAX,GAAAoB,WAMA,IAAAuC,GAAA3D,EAAAJ,SAOA+D,GAAAC,MAAA,WACA,MAAAvF,MAAA8B,SAAA9B,KAAA4B,MAAA,EAAA5B,KAAA4B,KAQA0D,EAAAE,SAAA,WACA,MAAAxF,MAAA8B,UACA9B,KAAA6B,OAAA,GAAAqB,GAAAlD,KAAA4B,MAAA,GACA5B,KAAA6B,KAAAqB,GAAAlD,KAAA4B,MAAA,IAWA0D,EAAAG,SAAA,SAAAlC,GAEA,IADAA,KAAA,IACA,MAAAA,EACA,KAAAG,YAAA,QACA,IAAA1D,KAAA0F,SACA,SACA,IAAA1F,KAAA2F,aAAA,CACA,GAAA3F,KAAA4F,GAAA7C,GAAA,CAGA,GAAA8C,GAAArD,EAAAe,GACAuC,EAAA9F,KAAA8F,IAAAD,GACAE,EAAAD,EAAAzB,IAAAwB,GAAAG,IAAAhG,KACA,OAAA8F,GAAAL,SAAAlC,GAAAwC,EAAAR,QAAAE,SAAAlC,GAEA,UAAAvD,KAAAiD,MAAAwC,SAAAlC,GAQA,IAHA,GAAAM,GAAArB,EAAAsB,EAAAP,EAAA,GAAAvD,KAAA8B,UACAmE,EAAAjG,KACA+D,EAAA,KACA,CACA,GAAAmC,GAAAD,EAAAH,IAAAjC,GACAsC,EAAAF,EAAAD,IAAAE,EAAA7B,IAAAR,IAAA0B,UAAA,EACAa,EAAAD,EAAAV,SAAAlC,EAEA,IADA0C,EAAAC,EACAD,EAAAP,SACA,MAAAU,GAAArC,CAEA,MAAAqC,EAAA5C,OAAA,GACA4C,EAAA,IAAAA,CACArC,GAAA,GAAAqC,EAAArC,IAUAuB,EAAAe,YAAA,WACA,MAAArG,MAAA6B,MAQAyD,EAAAgB,oBAAA,WACA,MAAAtG,MAAA6B,OAAA,GAQAyD,EAAAiB,WAAA,WACA,MAAAvG,MAAA4B,KAQA0D,EAAAkB,mBAAA,WACA,MAAAxG,MAAA4B,MAAA,GAQA0D,EAAAmB,cAAA,WACA,GAAAzG,KAAA2F,aACA,MAAA3F,MAAA4F,GAAA7C,GAAA,GAAA/C,KAAAiD,MAAAwD,eAEA,QADAjC,GAAA,GAAAxE,KAAA6B,KAAA7B,KAAA6B,KAAA7B,KAAA4B,IACA8E,EAAA,GAAsBA,EAAA,GACtB,IAAAlC,EAAA,GAAAkC,GAD+BA,KAG/B,UAAA1G,KAAA6B,KAAA6E,EAAA,GAAAA,EAAA,GAQApB,EAAAI,OAAA,WACA,WAAA1F,KAAA6B,MAAA,IAAA7B,KAAA4B,KAOA0D,EAAAqB,IAAArB,EAAAI,OAOAJ,EAAAK,WAAA,WACA,OAAA3F,KAAA8B,UAAA9B,KAAA6B,KAAA,GAQAyD,EAAAsB,WAAA,WACA,MAAA5G,MAAA8B,UAAA9B,KAAA6B,MAAA,GAQAyD,EAAAuB,MAAA,WACA,aAAA7G,KAAA4B,MAQA0D,EAAAwB,OAAA,WACA,aAAA9G,KAAA4B,MASA0D,EAAAyB,OAAA,SAAAC,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,KACAhH,KAAA8B,WAAAkF,EAAAlF,UAAA9B,KAAA6B,OAAA,OAAAmF,EAAAnF,OAAA,SAEA7B,KAAA6B,OAAAmF,EAAAnF,MAAA7B,KAAA4B,MAAAoF,EAAApF,MASA0D,EAAAM,GAAAN,EAAAyB,OAQAzB,EAAA2B,UAAA,SAAAD,GACA,OAAAhH,KAAA4F,GAAAoB,IASA1B,EAAA4B,IAAA5B,EAAA2B,UAQA3B,EAAA6B,GAAA7B,EAAA2B,UAQA3B,EAAA8B,SAAA,SAAAJ,GACA,MAAAhH,MAAAqH,KAAAL,GAAA,GASA1B,EAAAgC,GAAAhC,EAAA8B,SAQA9B,EAAAiC,gBAAA,SAAAP,GACA,MAAAhH,MAAAqH,KAAAL,IAAA,GASA1B,EAAAkC,IAAAlC,EAAAiC,gBAQAjC,EAAAmC,GAAAnC,EAAAiC,gBAQAjC,EAAAoC,YAAA,SAAAV,GACA,MAAAhH,MAAAqH,KAAAL,GAAA,GASA1B,EAAAqC,GAAArC,EAAAoC,YAQApC,EAAAsC,mBAAA,SAAAZ,GACA,MAAAhH,MAAAqH,KAAAL,IAAA,GASA1B,EAAAuC,IAAAvC,EAAAsC,mBAQAtC,EAAAwC,GAAAxC,EAAAsC,mBASAtC,EAAAyC,QAAA,SAAAf,GAGA,GAFAjF,EAAAiF,KACAA,EAAAzC,EAAAyC,IACAhH,KAAA4F,GAAAoB,GACA,QACA,IAAAgB,GAAAhI,KAAA2F,aACAsC,EAAAjB,EAAArB,YACA,OAAAqC,KAAAC,GACA,GACAD,GAAAC,EACA,EAEAjI,KAAA8B,SAGAkF,EAAAnF,OAAA,EAAA7B,KAAA6B,OAAA,GAAAmF,EAAAnF,OAAA7B,KAAA6B,MAAAmF,EAAApF,MAAA,EAAA5B,KAAA4B,MAAA,OAFA5B,KAAAgG,IAAAgB,GAAArB,cAAA,KAYAL,EAAA+B,KAAA/B,EAAAyC,QAOAzC,EAAA4C,OAAA,WACA,OAAAlI,KAAA8B,UAAA9B,KAAA4F,GAAA7C,GACAA,EACA/C,KAAAmI,MAAA7D,IAAAa,IAQAG,EAAArC,IAAAqC,EAAA4C,OAQA5C,EAAAhB,IAAA,SAAA8D,GACArG,EAAAqG,KACAA,EAAA7D,EAAA6D,GAIA,IAAAC,GAAArI,KAAA6B,OAAA,GACAyG,EAAA,MAAAtI,KAAA6B,KACA0G,EAAAvI,KAAA4B,MAAA,GACA4G,EAAA,MAAAxI,KAAA4B,IAEA6G,EAAAL,EAAAvG,OAAA,GACA6G,EAAA,MAAAN,EAAAvG,KACA8G,EAAAP,EAAAxG,MAAA,GACAgH,EAAA,MAAAR,EAAAxG,IAEAiH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,CAYA,OAXAA,IAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAI,EACAI,GAAA,MACAvG,EAAAyG,GAAA,GAAAC,EAAAH,GAAA,GAAAC,EAAA9I,KAAA8B,WASAwD,EAAA2D,SAAA,SAAAC,GAGA,MAFAnH,GAAAmH,KACAA,EAAA3E,EAAA2E,IACAlJ,KAAAsE,IAAA4E,EAAAjG,QASAqC,EAAAU,IAAAV,EAAA2D,SAQA3D,EAAA6D,SAAA,SAAAC,GACA,GAAApJ,KAAA0F,SACA,MAAA/C,EAKA,IAJAZ,EAAAqH,KACAA,EAAA7E,EAAA6E,IAGA3E,EAAA,CAKA,MAAAnC,GAJAmC,EAAA,IAAAzE,KAAA4B,IACA5B,KAAA6B,KACAuH,EAAAxH,IACAwH,EAAAvH,MACA4C,EAAA,WAAAzE,KAAA8B,UAGA,GAAAsH,EAAA1D,SACA,MAAA/C,EACA,IAAA3C,KAAA4F,GAAA7C,GACA,MAAAqG,GAAAvC,QAAA9D,EAAAJ,CACA,IAAAyG,EAAAxD,GAAA7C,GACA,MAAA/C,MAAA6G,QAAA9D,EAAAJ,CAEA,IAAA3C,KAAA2F,aACA,MAAAyD,GAAAzD,aACA3F,KAAAiD,MAAAoB,IAAA+E,EAAAnG,OAEAjD,KAAAiD,MAAAoB,IAAA+E,GAAAnG,KACK,IAAAmG,EAAAzD,aACL,MAAA3F,MAAAqE,IAAA+E,EAAAnG,YAGA,IAAAjD,KAAAsH,GAAApC,IAAAkE,EAAA9B,GAAApC,GACA,MAAA1C,GAAAxC,KAAAwF,WAAA4D,EAAA5D,WAAAxF,KAAA8B,SAKA,IAAAuG,GAAArI,KAAA6B,OAAA,GACAyG,EAAA,MAAAtI,KAAA6B,KACA0G,EAAAvI,KAAA4B,MAAA,GACA4G,EAAA,MAAAxI,KAAA4B,IAEA6G,EAAAW,EAAAvH,OAAA,GACA6G,EAAA,MAAAU,EAAAvH,KACA8G,EAAAS,EAAAxH,MAAA,GACAgH,EAAA,MAAAQ,EAAAxH,IAEAiH,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,CAqBA,OApBAA,IAAAR,EAAAI,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAK,EACAE,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAP,EAAAG,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAM,EACAC,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAP,EAAAI,EACAE,GAAAC,IAAA,GACAA,GAAA,MACAA,GAAAN,EAAAE,EACAG,GAAAC,IAAA,GACAA,GAAA,MACAD,GAAAR,EAAAO,EAAAN,EAAAK,EAAAJ,EAAAG,EAAAF,EAAAC,EACAI,GAAA,MACAvG,EAAAyG,GAAA,GAAAC,EAAAH,GAAA,GAAAC,EAAA9I,KAAA8B,WASAwD,EAAAjB,IAAAiB,EAAA6D,SASA7D,EAAA+D,OAAA,SAAAC,GAGA,GAFAvH,EAAAuH,KACAA,EAAA/E,EAAA+E,IACAA,EAAA5D,SACA,KAAAjC,OAAA,mBAGA,IAAAgB,EAAA,CAIA,IAAAzE,KAAA8B,WACA,aAAA9B,KAAA6B,OACA,IAAAyH,EAAA1H,MAAA,IAAA0H,EAAAzH,KAEA,MAAA7B,KAQA,OAAAsC,IANAtC,KAAA8B,SAAA2C,EAAA,MAAAA,EAAA,OACAzE,KAAA4B,IACA5B,KAAA6B,KACAyH,EAAA1H,IACA0H,EAAAzH,MAEA4C,EAAA,WAAAzE,KAAA8B,UAGA,GAAA9B,KAAA0F,SACA,MAAA1F,MAAA8B,SAAAY,EAAAC,CACA,IAAA4G,GAAAtD,EAAAuD,CACA,IAAAxJ,KAAA8B,SA6BK,CAKL,GAFAwH,EAAAxH,WACAwH,IAAAG,cACAH,EAAA3B,GAAA3H,MACA,MAAA0C,EACA,IAAA4G,EAAA3B,GAAA3H,KAAA0J,KAAA,IACA,MAAAtE,EACAoE,GAAA9G,MAtCA,CAGA,GAAA1C,KAAA4F,GAAA7C,GAAA,CACA,GAAAuG,EAAA1D,GAAAT,IAAAmE,EAAA1D,GAAAP,GACA,MAAAtC,EACA,IAAAuG,EAAA1D,GAAA7C,GACA,MAAAoC,EAKA,OADAoE,GADAvJ,KAAA2J,IAAA,GACA7D,IAAAwD,GAAAM,IAAA,GACAL,EAAA3D,GAAAjD,GACA2G,EAAA3D,aAAAR,EAAAE,GAEAY,EAAAjG,KAAAgG,IAAAsD,EAAAjF,IAAAkF,IACAC,EAAAD,EAAAjF,IAAA2B,EAAAH,IAAAwD,KAIS,GAAAA,EAAA1D,GAAA7C,GACT,MAAA/C,MAAA8B,SAAAY,EAAAC,CACA,IAAA3C,KAAA2F,aACA,MAAA2D,GAAA3D,aACA3F,KAAAiD,MAAA6C,IAAAwD,EAAArG,OACAjD,KAAAiD,MAAA6C,IAAAwD,GAAArG,KACS,IAAAqG,EAAA3D,aACT,MAAA3F,MAAA8F,IAAAwD,EAAArG,YACAuG,GAAA7G,EAmBA,IADAsD,EAAAjG,KACAiG,EAAA4B,IAAAyB,IAAA,CAGAC,EAAAtF,KAAA4F,IAAA,EAAA5F,KAAA6F,MAAA7D,EAAAT,WAAA8D,EAAA9D,YAWA,KAPA,GAAAuE,GAAA9F,KAAA+F,KAAA/F,KAAAgG,IAAAV,GAAAtF,KAAAiG,KACAC,EAAAJ,GAAA,KAAAjG,EAAA,EAAAiG,EAAA,IAIAK,EAAA5H,EAAA+G,GACAc,EAAAD,EAAA/F,IAAAiF,GACAe,EAAA1E,cAAA0E,EAAA1C,GAAA1B,IACAsD,GAAAY,EACAC,EAAA5H,EAAA+G,EAAAvJ,KAAA8B,UACAuI,EAAAD,EAAA/F,IAAAiF,EAKAc,GAAA1E,WACA0E,EAAAjF,GAEAqE,IAAAlF,IAAA8F,GACAnE,IAAAD,IAAAqE,GAEA,MAAAb,IASAlE,EAAAQ,IAAAR,EAAA+D,OAQA/D,EAAAgF,OAAA,SAAAhB,GAKA,GAJAvH,EAAAuH,KACAA,EAAA/E,EAAA+E,IAGA7E,EAAA,CAOA,MAAAnC,IANAtC,KAAA8B,SAAA2C,EAAA,MAAAA,EAAA,OACAzE,KAAA4B,IACA5B,KAAA6B,KACAyH,EAAA1H,IACA0H,EAAAzH,MAEA4C,EAAA,WAAAzE,KAAA8B,UAGA,MAAA9B,MAAAgG,IAAAhG,KAAA8F,IAAAwD,GAAAjF,IAAAiF,KASAhE,EAAAiF,IAAAjF,EAAAgF,OAQAhF,EAAAW,IAAAX,EAAAgF,OAOAhF,EAAA6C,IAAA,WACA,MAAA7F,IAAAtC,KAAA4B,KAAA5B,KAAA6B,KAAA7B,KAAA8B,WASAwD,EAAAkF,IAAA,SAAAxD,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WASAwD,EAAAmF,GAAA,SAAAzD,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WASAwD,EAAAoF,IAAA,SAAA1D,GAGA,MAFAjF,GAAAiF,KACAA,EAAAzC,EAAAyC,IACA1E,EAAAtC,KAAA4B,IAAAoF,EAAApF,IAAA5B,KAAA6B,KAAAmF,EAAAnF,KAAA7B,KAAA8B,WASAwD,EAAAqF,UAAA,SAAAC,GAGA,MAFA7I,GAAA6I,KACAA,IAAArF,SACA,IAAAqF,GAAA,IACA5K,KACA4K,EAAA,GACAtI,EAAAtC,KAAA4B,KAAAgJ,EAAA5K,KAAA6B,MAAA+I,EAAA5K,KAAA4B,MAAA,GAAAgJ,EAAA5K,KAAA8B,UAEAQ,EAAA,EAAAtC,KAAA4B,KAAAgJ,EAAA,GAAA5K,KAAA8B,WASAwD,EAAAsE,IAAAtE,EAAAqF,UAQArF,EAAAuF,WAAA,SAAAD,GAGA,MAFA7I,GAAA6I,KACAA,IAAArF,SACA,IAAAqF,GAAA,IACA5K,KACA4K,EAAA,GACAtI,EAAAtC,KAAA4B,MAAAgJ,EAAA5K,KAAA6B,MAAA,GAAA+I,EAAA5K,KAAA6B,MAAA+I,EAAA5K,KAAA8B,UAEAQ,EAAAtC,KAAA6B,MAAA+I,EAAA,GAAA5K,KAAA6B,MAAA,OAAA7B,KAAA8B,WASAwD,EAAAqE,IAAArE,EAAAuF,WAQAvF,EAAAwF,mBAAA,SAAAF,GAEA,MADA7I,GAAA6I,SAAArF,SACA,IAAAqF,GAAA,IAAA5K,KACA4K,EAAA,GAAAtI,EAAAtC,KAAA4B,MAAAgJ,EAAA5K,KAAA6B,MAAA,GAAA+I,EAAA5K,KAAA6B,OAAA+I,EAAA5K,KAAA8B,UACA,KAAA8I,EAAAtI,EAAAtC,KAAA6B,KAAA,EAAA7B,KAAA8B,UACAQ,EAAAtC,KAAA6B,OAAA+I,EAAA,KAAA5K,KAAA8B,WASAwD,EAAAoE,KAAApE,EAAAwF,mBAQAxF,EAAAyF,MAAAzF,EAAAwF,mBAQAxF,EAAA0F,WAAA,SAAAJ,GACA,GAAAK,EAEA,OADAlJ,GAAA6I,SAAArF,SACA,IAAAqF,GAAA,IAAA5K,KACA,KAAA4K,EAAAtI,EAAAtC,KAAA6B,KAAA7B,KAAA4B,IAAA5B,KAAA8B,UACA8I,EAAA,IACAK,EAAA,GAAAL,EACAtI,EAAAtC,KAAA4B,KAAAgJ,EAAA5K,KAAA6B,OAAAoJ,EAAAjL,KAAA6B,MAAA+I,EAAA5K,KAAA4B,MAAAqJ,EAAAjL,KAAA8B,YAEA8I,GAAA,GACAK,EAAA,GAAAL,EACAtI,EAAAtC,KAAA6B,MAAA+I,EAAA5K,KAAA4B,MAAAqJ,EAAAjL,KAAA4B,KAAAgJ,EAAA5K,KAAA6B,OAAAoJ,EAAAjL,KAAA8B,YAQAwD,EAAA4F,KAAA5F,EAAA0F,WAQA1F,EAAA6F,YAAA,SAAAP,GACA,GAAAK,EAEA,OADAlJ,GAAA6I,SAAArF,SACA,IAAAqF,GAAA,IAAA5K,KACA,KAAA4K,EAAAtI,EAAAtC,KAAA6B,KAAA7B,KAAA4B,IAAA5B,KAAA8B,UACA8I,EAAA,IACAK,EAAA,GAAAL,EACAtI,EAAAtC,KAAA6B,MAAAoJ,EAAAjL,KAAA4B,MAAAgJ,EAAA5K,KAAA4B,KAAAqJ,EAAAjL,KAAA6B,OAAA+I,EAAA5K,KAAA8B,YAEA8I,GAAA,GACAK,EAAA,GAAAL,EACAtI,EAAAtC,KAAA4B,KAAAqJ,EAAAjL,KAAA6B,OAAA+I,EAAA5K,KAAA6B,MAAAoJ,EAAAjL,KAAA4B,MAAAgJ,EAAA5K,KAAA8B,YAQAwD,EAAA8F,KAAA9F,EAAA6F,YAOA7F,EAAA+F,SAAA,WACA,MAAArL,MAAA8B,SAEAQ,EAAAtC,KAAA4B,IAAA5B,KAAA6B,MAAA,GADA7B,MASAsF,EAAAmE,WAAA,WACA,MAAAzJ,MAAA8B,SACA9B,KACAsC,EAAAtC,KAAA4B,IAAA5B,KAAA6B,MAAA,IASAyD,EAAAgG,QAAA,SAAA7D,GACA,MAAAA,GAAAzH,KAAAuL,YAAAvL,KAAAwL,aAQAlG,EAAAiG,UAAA,WACA,GAAAE,GAAAzL,KAAA6B,KACA6J,EAAA1L,KAAA4B,GACA,QACA,IAAA8J,EACAA,IAAA,MACAA,IAAA,OACAA,IAAA,GACA,IAAAD,EACAA,IAAA,MACAA,IAAA,OACAA,IAAA,KASAnG,EAAAkG,UAAA,WACA,GAAAC,GAAAzL,KAAA6B,KACA6J,EAAA1L,KAAA4B,GACA,QACA6J,IAAA,GACAA,IAAA,OACAA,IAAA,MACA,IAAAA,EACAC,IAAA,GACAA,IAAA,OACAA,IAAA,MACA,IAAAA,IAWA/J,EAAAgK,UAAA,SAAAC,EAAA9J,EAAA2F,GACA,MAAAA,GAAA9F,EAAAkK,YAAAD,EAAA9J,GAAAH,EAAAmK,YAAAF,EAAA9J,IASAH,EAAAkK,YAAA,SAAAD,EAAA9J,GACA,UAAAH,GACAiK,EAAA,GACAA,EAAA,MACAA,EAAA,OACAA,EAAA,OACAA,EAAA,GACAA,EAAA,MACAA,EAAA,OACAA,EAAA,OACA9J,IAUAH,EAAAmK,YAAA,SAAAF,EAAA9J,GACA,UAAAH,GACAiK,EAAA,OACAA,EAAA,OACAA,EAAA,MACAA,EAAA,GACAA,EAAA,OACAA,EAAA,OACAA,EAAA,MACAA,EAAA,GACA9J", "file": "long.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Long\"] = factory();\n\telse\n\t\troot[\"Long\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Long\"] = factory();\n\telse\n\t\troot[\"Long\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = Long;\n\n/**\n * wasm optimizations, to do native i64 multiplication and divide\n */\nvar wasm = null;\n\ntry {\n  wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\n    0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11\n  ])), {}).exports;\n} catch (e) {\n  // no wasm support :(\n}\n\n/**\n * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n *  See the from* functions below for more convenient ways of constructing Longs.\n * @exports Long\n * @class A Long class for representing a 64 bit two's-complement integer value.\n * @param {number} low The low (signed) 32 bits of the long\n * @param {number} high The high (signed) 32 bits of the long\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @constructor\n */\nfunction Long(low, high, unsigned) {\n\n    /**\n     * The low 32 bits as a signed value.\n     * @type {number}\n     */\n    this.low = low | 0;\n\n    /**\n     * The high 32 bits as a signed value.\n     * @type {number}\n     */\n    this.high = high | 0;\n\n    /**\n     * Whether unsigned or not.\n     * @type {boolean}\n     */\n    this.unsigned = !!unsigned;\n}\n\n// The internal representation of a long is the two given signed, 32-bit values.\n// We use 32-bit pieces because these are the size of integers on which\n// Javascript performs bit-operations.  For operations like addition and\n// multiplication, we split each number into 16 bit pieces, which can easily be\n// multiplied within Javascript's floating-point representation without overflow\n// or change in sign.\n//\n// In the algorithms below, we frequently reduce the negative case to the\n// positive case by negating the input(s) and then post-processing the result.\n// Note that we must ALWAYS check specially whether those values are MIN_VALUE\n// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n// a positive number, it overflows back into a negative).  Not handling this\n// case would often result in infinite recursion.\n//\n// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n// methods on which they depend.\n\n/**\n * An indicator used to reliably determine if an object is a Long or not.\n * @type {boolean}\n * @const\n * @private\n */\nLong.prototype.__isLong__;\n\nObject.defineProperty(Long.prototype, \"__isLong__\", { value: true });\n\n/**\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n * @inner\n */\nfunction isLong(obj) {\n    return (obj && obj[\"__isLong__\"]) === true;\n}\n\n/**\n * Tests if the specified object is a Long.\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n */\nLong.isLong = isLong;\n\n/**\n * A cache of the Long representations of small integer values.\n * @type {!Object}\n * @inner\n */\nvar INT_CACHE = {};\n\n/**\n * A cache of the Long representations of small unsigned integer values.\n * @type {!Object}\n * @inner\n */\nvar UINT_CACHE = {};\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromInt(value, unsigned) {\n    var obj, cachedObj, cache;\n    if (unsigned) {\n        value >>>= 0;\n        if (cache = (0 <= value && value < 256)) {\n            cachedObj = UINT_CACHE[value];\n            if (cachedObj)\n                return cachedObj;\n        }\n        obj = fromBits(value, (value | 0) < 0 ? -1 : 0, true);\n        if (cache)\n            UINT_CACHE[value] = obj;\n        return obj;\n    } else {\n        value |= 0;\n        if (cache = (-128 <= value && value < 128)) {\n            cachedObj = INT_CACHE[value];\n            if (cachedObj)\n                return cachedObj;\n        }\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\n        if (cache)\n            INT_CACHE[value] = obj;\n        return obj;\n    }\n}\n\n/**\n * Returns a Long representing the given 32 bit integer value.\n * @function\n * @param {number} value The 32 bit integer in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromInt = fromInt;\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromNumber(value, unsigned) {\n    if (isNaN(value))\n        return unsigned ? UZERO : ZERO;\n    if (unsigned) {\n        if (value < 0)\n            return UZERO;\n        if (value >= TWO_PWR_64_DBL)\n            return MAX_UNSIGNED_VALUE;\n    } else {\n        if (value <= -TWO_PWR_63_DBL)\n            return MIN_VALUE;\n        if (value + 1 >= TWO_PWR_63_DBL)\n            return MAX_VALUE;\n    }\n    if (value < 0)\n        return fromNumber(-value, unsigned).neg();\n    return fromBits((value % TWO_PWR_32_DBL) | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);\n}\n\n/**\n * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n * @function\n * @param {number} value The number in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromNumber = fromNumber;\n\n/**\n * @param {number} lowBits\n * @param {number} highBits\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromBits(lowBits, highBits, unsigned) {\n    return new Long(lowBits, highBits, unsigned);\n}\n\n/**\n * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n *  assumed to use 32 bits.\n * @function\n * @param {number} lowBits The low 32 bits\n * @param {number} highBits The high 32 bits\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromBits = fromBits;\n\n/**\n * @function\n * @param {number} base\n * @param {number} exponent\n * @returns {number}\n * @inner\n */\nvar pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n/**\n * @param {string} str\n * @param {(boolean|number)=} unsigned\n * @param {number=} radix\n * @returns {!Long}\n * @inner\n */\nfunction fromString(str, unsigned, radix) {\n    if (str.length === 0)\n        throw Error('empty string');\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\")\n        return ZERO;\n    if (typeof unsigned === 'number') {\n        // For goog.math.long compatibility\n        radix = unsigned,\n        unsigned = false;\n    } else {\n        unsigned = !! unsigned;\n    }\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix)\n        throw RangeError('radix');\n\n    var p;\n    if ((p = str.indexOf('-')) > 0)\n        throw Error('interior hyphen');\n    else if (p === 0) {\n        return fromString(str.substring(1), unsigned, radix).neg();\n    }\n\n    // Do several (8) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\n\n    var result = ZERO;\n    for (var i = 0; i < str.length; i += 8) {\n        var size = Math.min(8, str.length - i),\n            value = parseInt(str.substring(i, i + size), radix);\n        if (size < 8) {\n            var power = fromNumber(pow_dbl(radix, size));\n            result = result.mul(power).add(fromNumber(value));\n        } else {\n            result = result.mul(radixToPower);\n            result = result.add(fromNumber(value));\n        }\n    }\n    result.unsigned = unsigned;\n    return result;\n}\n\n/**\n * Returns a Long representation of the given string, written using the specified radix.\n * @function\n * @param {string} str The textual representation of the Long\n * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n * @returns {!Long} The corresponding Long value\n */\nLong.fromString = fromString;\n\n/**\n * @function\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromValue(val, unsigned) {\n    if (typeof val === 'number')\n        return fromNumber(val, unsigned);\n    if (typeof val === 'string')\n        return fromString(val, unsigned);\n    // Throws for non-objects, converts non-instanceof Long:\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\n}\n\n/**\n * Converts the specified value to a Long using the appropriate from* function for its type.\n * @function\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val Value\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long}\n */\nLong.fromValue = fromValue;\n\n// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n// no runtime penalty for these.\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_16_DBL = 1 << 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_24_DBL = 1 << 24;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n/**\n * @type {!Long}\n * @const\n * @inner\n */\nvar TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ZERO = fromInt(0);\n\n/**\n * Signed zero.\n * @type {!Long}\n */\nLong.ZERO = ZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UZERO = fromInt(0, true);\n\n/**\n * Unsigned zero.\n * @type {!Long}\n */\nLong.UZERO = UZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ONE = fromInt(1);\n\n/**\n * Signed one.\n * @type {!Long}\n */\nLong.ONE = ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UONE = fromInt(1, true);\n\n/**\n * Unsigned one.\n * @type {!Long}\n */\nLong.UONE = UONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar NEG_ONE = fromInt(-1);\n\n/**\n * Signed negative one.\n * @type {!Long}\n */\nLong.NEG_ONE = NEG_ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_VALUE = fromBits(0xFFFFFFFF|0, 0x7FFFFFFF|0, false);\n\n/**\n * Maximum signed value.\n * @type {!Long}\n */\nLong.MAX_VALUE = MAX_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF|0, 0xFFFFFFFF|0, true);\n\n/**\n * Maximum unsigned value.\n * @type {!Long}\n */\nLong.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MIN_VALUE = fromBits(0, 0x80000000|0, false);\n\n/**\n * Minimum signed value.\n * @type {!Long}\n */\nLong.MIN_VALUE = MIN_VALUE;\n\n/**\n * @alias Long.prototype\n * @inner\n */\nvar LongPrototype = Long.prototype;\n\n/**\n * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toInt = function toInt() {\n    return this.unsigned ? this.low >>> 0 : this.low;\n};\n\n/**\n * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toNumber = function toNumber() {\n    if (this.unsigned)\n        return ((this.high >>> 0) * TWO_PWR_32_DBL) + (this.low >>> 0);\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n};\n\n/**\n * Converts the Long to a string written in the specified radix.\n * @this {!Long}\n * @param {number=} radix Radix (2-36), defaults to 10\n * @returns {string}\n * @override\n * @throws {RangeError} If `radix` is out of range\n */\nLongPrototype.toString = function toString(radix) {\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix)\n        throw RangeError('radix');\n    if (this.isZero())\n        return '0';\n    if (this.isNegative()) { // Unsigned Longs are never negative\n        if (this.eq(MIN_VALUE)) {\n            // We need to change the Long value before it can be negated, so we remove\n            // the bottom-most digit in this base and then recurse to do the rest.\n            var radixLong = fromNumber(radix),\n                div = this.div(radixLong),\n                rem1 = div.mul(radixLong).sub(this);\n            return div.toString(radix) + rem1.toInt().toString(radix);\n        } else\n            return '-' + this.neg().toString(radix);\n    }\n\n    // Do several (6) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n        rem = this;\n    var result = '';\n    while (true) {\n        var remDiv = rem.div(radixToPower),\n            intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n            digits = intval.toString(radix);\n        rem = remDiv;\n        if (rem.isZero())\n            return digits + result;\n        else {\n            while (digits.length < 6)\n                digits = '0' + digits;\n            result = '' + digits + result;\n        }\n    }\n};\n\n/**\n * Gets the high 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed high bits\n */\nLongPrototype.getHighBits = function getHighBits() {\n    return this.high;\n};\n\n/**\n * Gets the high 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned high bits\n */\nLongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n    return this.high >>> 0;\n};\n\n/**\n * Gets the low 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed low bits\n */\nLongPrototype.getLowBits = function getLowBits() {\n    return this.low;\n};\n\n/**\n * Gets the low 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned low bits\n */\nLongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n    return this.low >>> 0;\n};\n\n/**\n * Gets the number of bits needed to represent the absolute value of this Long.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n    if (this.isNegative()) // Unsigned Longs are never negative\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n    var val = this.high != 0 ? this.high : this.low;\n    for (var bit = 31; bit > 0; bit--)\n        if ((val & (1 << bit)) != 0)\n            break;\n    return this.high != 0 ? bit + 33 : bit + 1;\n};\n\n/**\n * Tests if this Long's value equals zero.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isZero = function isZero() {\n    return this.high === 0 && this.low === 0;\n};\n\n/**\n * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n * @returns {boolean}\n */\nLongPrototype.eqz = LongPrototype.isZero;\n\n/**\n * Tests if this Long's value is negative.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isNegative = function isNegative() {\n    return !this.unsigned && this.high < 0;\n};\n\n/**\n * Tests if this Long's value is positive.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isPositive = function isPositive() {\n    return this.unsigned || this.high >= 0;\n};\n\n/**\n * Tests if this Long's value is odd.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isOdd = function isOdd() {\n    return (this.low & 1) === 1;\n};\n\n/**\n * Tests if this Long's value is even.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isEven = function isEven() {\n    return (this.low & 1) === 0;\n};\n\n/**\n * Tests if this Long's value equals the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.equals = function equals(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    if (this.unsigned !== other.unsigned && (this.high >>> 31) === 1 && (other.high >>> 31) === 1)\n        return false;\n    return this.high === other.high && this.low === other.low;\n};\n\n/**\n * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.eq = LongPrototype.equals;\n\n/**\n * Tests if this Long's value differs from the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.notEquals = function notEquals(other) {\n    return !this.eq(/* validates */ other);\n};\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.neq = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ne = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value is less than the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThan = function lessThan(other) {\n    return this.comp(/* validates */ other) < 0;\n};\n\n/**\n * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lt = LongPrototype.lessThan;\n\n/**\n * Tests if this Long's value is less than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n    return this.comp(/* validates */ other) <= 0;\n};\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.le = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThan = function greaterThan(other) {\n    return this.comp(/* validates */ other) > 0;\n};\n\n/**\n * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gt = LongPrototype.greaterThan;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n    return this.comp(/* validates */ other) >= 0;\n};\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n/**\n * Compares this Long's value with the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.compare = function compare(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    if (this.eq(other))\n        return 0;\n    var thisNeg = this.isNegative(),\n        otherNeg = other.isNegative();\n    if (thisNeg && !otherNeg)\n        return -1;\n    if (!thisNeg && otherNeg)\n        return 1;\n    // At this point the sign bits are the same\n    if (!this.unsigned)\n        return this.sub(other).isNegative() ? -1 : 1;\n    // Both are positive if at least one is unsigned\n    return (other.high >>> 0) > (this.high >>> 0) || (other.high === this.high && (other.low >>> 0) > (this.low >>> 0)) ? -1 : 1;\n};\n\n/**\n * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.comp = LongPrototype.compare;\n\n/**\n * Negates this Long's value.\n * @this {!Long}\n * @returns {!Long} Negated Long\n */\nLongPrototype.negate = function negate() {\n    if (!this.unsigned && this.eq(MIN_VALUE))\n        return MIN_VALUE;\n    return this.not().add(ONE);\n};\n\n/**\n * Negates this Long's value. This is an alias of {@link Long#negate}.\n * @function\n * @returns {!Long} Negated Long\n */\nLongPrototype.neg = LongPrototype.negate;\n\n/**\n * Returns the sum of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} addend Addend\n * @returns {!Long} Sum\n */\nLongPrototype.add = function add(addend) {\n    if (!isLong(addend))\n        addend = fromValue(addend);\n\n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n\n    var b48 = addend.high >>> 16;\n    var b32 = addend.high & 0xFFFF;\n    var b16 = addend.low >>> 16;\n    var b00 = addend.low & 0xFFFF;\n\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n    c00 += a00 + b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 + b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 + b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 + b48;\n    c48 &= 0xFFFF;\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the difference of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.subtract = function subtract(subtrahend) {\n    if (!isLong(subtrahend))\n        subtrahend = fromValue(subtrahend);\n    return this.add(subtrahend.neg());\n};\n\n/**\n * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n * @function\n * @param {!Long|number|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.sub = LongPrototype.subtract;\n\n/**\n * Returns the product of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.multiply = function multiply(multiplier) {\n    if (this.isZero())\n        return ZERO;\n    if (!isLong(multiplier))\n        multiplier = fromValue(multiplier);\n\n    // use wasm support if present\n    if (wasm) {\n        var low = wasm[\"mul\"](this.low,\n                              this.high,\n                              multiplier.low,\n                              multiplier.high);\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    if (multiplier.isZero())\n        return ZERO;\n    if (this.eq(MIN_VALUE))\n        return multiplier.isOdd() ? MIN_VALUE : ZERO;\n    if (multiplier.eq(MIN_VALUE))\n        return this.isOdd() ? MIN_VALUE : ZERO;\n\n    if (this.isNegative()) {\n        if (multiplier.isNegative())\n            return this.neg().mul(multiplier.neg());\n        else\n            return this.neg().mul(multiplier).neg();\n    } else if (multiplier.isNegative())\n        return this.mul(multiplier.neg()).neg();\n\n    // If both longs are small, use float multiplication\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n        return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n\n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n    // We can skip products that would overflow.\n\n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n\n    var b48 = multiplier.high >>> 16;\n    var b32 = multiplier.high & 0xFFFF;\n    var b16 = multiplier.low >>> 16;\n    var b00 = multiplier.low & 0xFFFF;\n\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n    c00 += a00 * b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 * b00;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c16 += a00 * b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 * b00;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a16 * b16;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a00 * b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n    c48 &= 0xFFFF;\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n * @function\n * @param {!Long|number|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.mul = LongPrototype.multiply;\n\n/**\n * Returns this Long divided by the specified. The result is signed if this Long is signed or\n *  unsigned if this Long is unsigned.\n * @this {!Long}\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.divide = function divide(divisor) {\n    if (!isLong(divisor))\n        divisor = fromValue(divisor);\n    if (divisor.isZero())\n        throw Error('division by zero');\n\n    // use wasm support if present\n    if (wasm) {\n        // guard against signed division overflow: the largest\n        // negative number / -1 would be 1 larger than the largest\n        // positive number, due to two's complement.\n        if (!this.unsigned &&\n            this.high === -0x80000000 &&\n            divisor.low === -1 && divisor.high === -1) {\n            // be consistent with non-wasm code path\n            return this;\n        }\n        var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n            this.low,\n            this.high,\n            divisor.low,\n            divisor.high\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    if (this.isZero())\n        return this.unsigned ? UZERO : ZERO;\n    var approx, rem, res;\n    if (!this.unsigned) {\n        // This section is only relevant for signed longs and is derived from the\n        // closure library as a whole.\n        if (this.eq(MIN_VALUE)) {\n            if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n                return MIN_VALUE;  // recall that -MIN_VALUE == MIN_VALUE\n            else if (divisor.eq(MIN_VALUE))\n                return ONE;\n            else {\n                // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n                var halfThis = this.shr(1);\n                approx = halfThis.div(divisor).shl(1);\n                if (approx.eq(ZERO)) {\n                    return divisor.isNegative() ? ONE : NEG_ONE;\n                } else {\n                    rem = this.sub(divisor.mul(approx));\n                    res = approx.add(rem.div(divisor));\n                    return res;\n                }\n            }\n        } else if (divisor.eq(MIN_VALUE))\n            return this.unsigned ? UZERO : ZERO;\n        if (this.isNegative()) {\n            if (divisor.isNegative())\n                return this.neg().div(divisor.neg());\n            return this.neg().div(divisor).neg();\n        } else if (divisor.isNegative())\n            return this.div(divisor.neg()).neg();\n        res = ZERO;\n    } else {\n        // The algorithm below has not been made for unsigned longs. It's therefore\n        // required to take special care of the MSB prior to running it.\n        if (!divisor.unsigned)\n            divisor = divisor.toUnsigned();\n        if (divisor.gt(this))\n            return UZERO;\n        if (divisor.gt(this.shru(1))) // 15 >>> 1 = 7 ; with divisor = 8 ; true\n            return UONE;\n        res = UZERO;\n    }\n\n    // Repeat the following until the remainder is less than other:  find a\n    // floating-point that approximates remainder / other *from below*, add this\n    // into the result, and subtract it from the remainder.  It is critical that\n    // the approximate value is less than or equal to the real value so that the\n    // remainder never becomes negative.\n    rem = this;\n    while (rem.gte(divisor)) {\n        // Approximate the result of division. This may be a little greater or\n        // smaller than the actual value.\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n        // We will tweak the approximate result by changing it in the 48-th digit or\n        // the smallest non-fractional digit, whichever is larger.\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n            delta = (log2 <= 48) ? 1 : pow_dbl(2, log2 - 48),\n\n        // Decrease the approximation until it is smaller than the remainder.  Note\n        // that if it is too large, the product overflows and is negative.\n            approxRes = fromNumber(approx),\n            approxRem = approxRes.mul(divisor);\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\n            approx -= delta;\n            approxRes = fromNumber(approx, this.unsigned);\n            approxRem = approxRes.mul(divisor);\n        }\n\n        // We know the answer can't be zero... and actually, zero would cause\n        // infinite recursion since we would make no progress.\n        if (approxRes.isZero())\n            approxRes = ONE;\n\n        res = res.add(approxRes);\n        rem = rem.sub(approxRem);\n    }\n    return res;\n};\n\n/**\n * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.div = LongPrototype.divide;\n\n/**\n * Returns this Long modulo the specified.\n * @this {!Long}\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.modulo = function modulo(divisor) {\n    if (!isLong(divisor))\n        divisor = fromValue(divisor);\n\n    // use wasm support if present\n    if (wasm) {\n        var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n            this.low,\n            this.high,\n            divisor.low,\n            divisor.high\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    return this.sub(this.div(divisor).mul(divisor));\n};\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.mod = LongPrototype.modulo;\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.rem = LongPrototype.modulo;\n\n/**\n * Returns the bitwise NOT of this Long.\n * @this {!Long}\n * @returns {!Long}\n */\nLongPrototype.not = function not() {\n    return fromBits(~this.low, ~this.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise AND of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.and = function and(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise OR of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.or = function or(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise XOR of this Long and the given one.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.xor = function xor(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftLeft = function shiftLeft(numBits) {\n    if (isLong(numBits))\n        numBits = numBits.toInt();\n    if ((numBits &= 63) === 0)\n        return this;\n    else if (numBits < 32)\n        return fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);\n    else\n        return fromBits(0, this.low << (numBits - 32), this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shl = LongPrototype.shiftLeft;\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRight = function shiftRight(numBits) {\n    if (isLong(numBits))\n        numBits = numBits.toInt();\n    if ((numBits &= 63) === 0)\n        return this;\n    else if (numBits < 32)\n        return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);\n    else\n        return fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);\n};\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr = LongPrototype.shiftRight;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits < 32) return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >>> numBits, this.unsigned);\n    if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n    return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n};\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits rotated to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateLeft = function rotateLeft(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n        b = (32 - numBits);\n        return fromBits(((this.low << numBits) | (this.high >>> b)), ((this.high << numBits) | (this.low >>> b)), this.unsigned);\n    }\n    numBits -= 32;\n    b = (32 - numBits);\n    return fromBits(((this.high << numBits) | (this.low >>> b)), ((this.low << numBits) | (this.high >>> b)), this.unsigned);\n}\n/**\n * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotl = LongPrototype.rotateLeft;\n\n/**\n * Returns this Long with bits rotated to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateRight = function rotateRight(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n        b = (32 - numBits);\n        return fromBits(((this.high << b) | (this.low >>> numBits)), ((this.low << b) | (this.high >>> numBits)), this.unsigned);\n    }\n    numBits -= 32;\n    b = (32 - numBits);\n    return fromBits(((this.low << b) | (this.high >>> numBits)), ((this.high << b) | (this.low >>> numBits)), this.unsigned);\n}\n/**\n * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotr = LongPrototype.rotateRight;\n\n/**\n * Converts this Long to signed.\n * @this {!Long}\n * @returns {!Long} Signed long\n */\nLongPrototype.toSigned = function toSigned() {\n    if (!this.unsigned)\n        return this;\n    return fromBits(this.low, this.high, false);\n};\n\n/**\n * Converts this Long to unsigned.\n * @this {!Long}\n * @returns {!Long} Unsigned long\n */\nLongPrototype.toUnsigned = function toUnsigned() {\n    if (this.unsigned)\n        return this;\n    return fromBits(this.low, this.high, true);\n};\n\n/**\n * Converts this Long to its byte representation.\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @this {!Long}\n * @returns {!Array.<number>} Byte representation\n */\nLongPrototype.toBytes = function toBytes(le) {\n    return le ? this.toBytesLE() : this.toBytesBE();\n};\n\n/**\n * Converts this Long to its little endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Little endian byte representation\n */\nLongPrototype.toBytesLE = function toBytesLE() {\n    var hi = this.high,\n        lo = this.low;\n    return [\n        lo        & 0xff,\n        lo >>>  8 & 0xff,\n        lo >>> 16 & 0xff,\n        lo >>> 24       ,\n        hi        & 0xff,\n        hi >>>  8 & 0xff,\n        hi >>> 16 & 0xff,\n        hi >>> 24\n    ];\n};\n\n/**\n * Converts this Long to its big endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Big endian byte representation\n */\nLongPrototype.toBytesBE = function toBytesBE() {\n    var hi = this.high,\n        lo = this.low;\n    return [\n        hi >>> 24       ,\n        hi >>> 16 & 0xff,\n        hi >>>  8 & 0xff,\n        hi        & 0xff,\n        lo >>> 24       ,\n        lo >>> 16 & 0xff,\n        lo >>>  8 & 0xff,\n        lo        & 0xff\n    ];\n};\n\n/**\n * Creates a Long from its byte representation.\n * @param {!Array.<number>} bytes Byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytes = function fromBytes(bytes, unsigned, le) {\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\n};\n\n/**\n * Creates a Long from its little endian byte representation.\n * @param {!Array.<number>} bytes Little endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n    return new Long(\n        bytes[0]       |\n        bytes[1] <<  8 |\n        bytes[2] << 16 |\n        bytes[3] << 24,\n        bytes[4]       |\n        bytes[5] <<  8 |\n        bytes[6] << 16 |\n        bytes[7] << 24,\n        unsigned\n    );\n};\n\n/**\n * Creates a Long from its big endian byte representation.\n * @param {!Array.<number>} bytes Big endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n    return new Long(\n        bytes[4] << 24 |\n        bytes[5] << 16 |\n        bytes[6] <<  8 |\n        bytes[7],\n        bytes[0] << 24 |\n        bytes[1] << 16 |\n        bytes[2] <<  8 |\n        bytes[3],\n        unsigned\n    );\n};\n\n\n/***/ })\n/******/ ]);\n});\n\n\n// WEBPACK FOOTER //\n// long.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap f96e8d1360c0487f2545", "module.exports = Long;\n\n/**\n * wasm optimizations, to do native i64 multiplication and divide\n */\nvar wasm = null;\n\ntry {\n  wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\n    0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11\n  ])), {}).exports;\n} catch (e) {\n  // no wasm support :(\n}\n\n/**\n * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n *  See the from* functions below for more convenient ways of constructing Longs.\n * @exports Long\n * @class A Long class for representing a 64 bit two's-complement integer value.\n * @param {number} low The low (signed) 32 bits of the long\n * @param {number} high The high (signed) 32 bits of the long\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @constructor\n */\nfunction Long(low, high, unsigned) {\n\n    /**\n     * The low 32 bits as a signed value.\n     * @type {number}\n     */\n    this.low = low | 0;\n\n    /**\n     * The high 32 bits as a signed value.\n     * @type {number}\n     */\n    this.high = high | 0;\n\n    /**\n     * Whether unsigned or not.\n     * @type {boolean}\n     */\n    this.unsigned = !!unsigned;\n}\n\n// The internal representation of a long is the two given signed, 32-bit values.\n// We use 32-bit pieces because these are the size of integers on which\n// Javascript performs bit-operations.  For operations like addition and\n// multiplication, we split each number into 16 bit pieces, which can easily be\n// multiplied within Javascript's floating-point representation without overflow\n// or change in sign.\n//\n// In the algorithms below, we frequently reduce the negative case to the\n// positive case by negating the input(s) and then post-processing the result.\n// Note that we must ALWAYS check specially whether those values are MIN_VALUE\n// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n// a positive number, it overflows back into a negative).  Not handling this\n// case would often result in infinite recursion.\n//\n// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n// methods on which they depend.\n\n/**\n * An indicator used to reliably determine if an object is a Long or not.\n * @type {boolean}\n * @const\n * @private\n */\nLong.prototype.__isLong__;\n\nObject.defineProperty(Long.prototype, \"__isLong__\", { value: true });\n\n/**\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n * @inner\n */\nfunction isLong(obj) {\n    return (obj && obj[\"__isLong__\"]) === true;\n}\n\n/**\n * Tests if the specified object is a Long.\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n */\nLong.isLong = isLong;\n\n/**\n * A cache of the Long representations of small integer values.\n * @type {!Object}\n * @inner\n */\nvar INT_CACHE = {};\n\n/**\n * A cache of the Long representations of small unsigned integer values.\n * @type {!Object}\n * @inner\n */\nvar UINT_CACHE = {};\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromInt(value, unsigned) {\n    var obj, cachedObj, cache;\n    if (unsigned) {\n        value >>>= 0;\n        if (cache = (0 <= value && value < 256)) {\n            cachedObj = UINT_CACHE[value];\n            if (cachedObj)\n                return cachedObj;\n        }\n        obj = fromBits(value, (value | 0) < 0 ? -1 : 0, true);\n        if (cache)\n            UINT_CACHE[value] = obj;\n        return obj;\n    } else {\n        value |= 0;\n        if (cache = (-128 <= value && value < 128)) {\n            cachedObj = INT_CACHE[value];\n            if (cachedObj)\n                return cachedObj;\n        }\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\n        if (cache)\n            INT_CACHE[value] = obj;\n        return obj;\n    }\n}\n\n/**\n * Returns a Long representing the given 32 bit integer value.\n * @function\n * @param {number} value The 32 bit integer in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromInt = fromInt;\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromNumber(value, unsigned) {\n    if (isNaN(value))\n        return unsigned ? UZERO : ZERO;\n    if (unsigned) {\n        if (value < 0)\n            return UZERO;\n        if (value >= TWO_PWR_64_DBL)\n            return MAX_UNSIGNED_VALUE;\n    } else {\n        if (value <= -TWO_PWR_63_DBL)\n            return MIN_VALUE;\n        if (value + 1 >= TWO_PWR_63_DBL)\n            return MAX_VALUE;\n    }\n    if (value < 0)\n        return fromNumber(-value, unsigned).neg();\n    return fromBits((value % TWO_PWR_32_DBL) | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);\n}\n\n/**\n * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n * @function\n * @param {number} value The number in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromNumber = fromNumber;\n\n/**\n * @param {number} lowBits\n * @param {number} highBits\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromBits(lowBits, highBits, unsigned) {\n    return new Long(lowBits, highBits, unsigned);\n}\n\n/**\n * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n *  assumed to use 32 bits.\n * @function\n * @param {number} lowBits The low 32 bits\n * @param {number} highBits The high 32 bits\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromBits = fromBits;\n\n/**\n * @function\n * @param {number} base\n * @param {number} exponent\n * @returns {number}\n * @inner\n */\nvar pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n/**\n * @param {string} str\n * @param {(boolean|number)=} unsigned\n * @param {number=} radix\n * @returns {!Long}\n * @inner\n */\nfunction fromString(str, unsigned, radix) {\n    if (str.length === 0)\n        throw Error('empty string');\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\")\n        return ZERO;\n    if (typeof unsigned === 'number') {\n        // For goog.math.long compatibility\n        radix = unsigned,\n        unsigned = false;\n    } else {\n        unsigned = !! unsigned;\n    }\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix)\n        throw RangeError('radix');\n\n    var p;\n    if ((p = str.indexOf('-')) > 0)\n        throw Error('interior hyphen');\n    else if (p === 0) {\n        return fromString(str.substring(1), unsigned, radix).neg();\n    }\n\n    // Do several (8) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\n\n    var result = ZERO;\n    for (var i = 0; i < str.length; i += 8) {\n        var size = Math.min(8, str.length - i),\n            value = parseInt(str.substring(i, i + size), radix);\n        if (size < 8) {\n            var power = fromNumber(pow_dbl(radix, size));\n            result = result.mul(power).add(fromNumber(value));\n        } else {\n            result = result.mul(radixToPower);\n            result = result.add(fromNumber(value));\n        }\n    }\n    result.unsigned = unsigned;\n    return result;\n}\n\n/**\n * Returns a Long representation of the given string, written using the specified radix.\n * @function\n * @param {string} str The textual representation of the Long\n * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n * @returns {!Long} The corresponding Long value\n */\nLong.fromString = fromString;\n\n/**\n * @function\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromValue(val, unsigned) {\n    if (typeof val === 'number')\n        return fromNumber(val, unsigned);\n    if (typeof val === 'string')\n        return fromString(val, unsigned);\n    // Throws for non-objects, converts non-instanceof Long:\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\n}\n\n/**\n * Converts the specified value to a Long using the appropriate from* function for its type.\n * @function\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val Value\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long}\n */\nLong.fromValue = fromValue;\n\n// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n// no runtime penalty for these.\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_16_DBL = 1 << 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_24_DBL = 1 << 24;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n/**\n * @type {!Long}\n * @const\n * @inner\n */\nvar TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ZERO = fromInt(0);\n\n/**\n * Signed zero.\n * @type {!Long}\n */\nLong.ZERO = ZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UZERO = fromInt(0, true);\n\n/**\n * Unsigned zero.\n * @type {!Long}\n */\nLong.UZERO = UZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ONE = fromInt(1);\n\n/**\n * Signed one.\n * @type {!Long}\n */\nLong.ONE = ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UONE = fromInt(1, true);\n\n/**\n * Unsigned one.\n * @type {!Long}\n */\nLong.UONE = UONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar NEG_ONE = fromInt(-1);\n\n/**\n * Signed negative one.\n * @type {!Long}\n */\nLong.NEG_ONE = NEG_ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_VALUE = fromBits(0xFFFFFFFF|0, 0x7FFFFFFF|0, false);\n\n/**\n * Maximum signed value.\n * @type {!Long}\n */\nLong.MAX_VALUE = MAX_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF|0, 0xFFFFFFFF|0, true);\n\n/**\n * Maximum unsigned value.\n * @type {!Long}\n */\nLong.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MIN_VALUE = fromBits(0, 0x80000000|0, false);\n\n/**\n * Minimum signed value.\n * @type {!Long}\n */\nLong.MIN_VALUE = MIN_VALUE;\n\n/**\n * @alias Long.prototype\n * @inner\n */\nvar LongPrototype = Long.prototype;\n\n/**\n * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toInt = function toInt() {\n    return this.unsigned ? this.low >>> 0 : this.low;\n};\n\n/**\n * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toNumber = function toNumber() {\n    if (this.unsigned)\n        return ((this.high >>> 0) * TWO_PWR_32_DBL) + (this.low >>> 0);\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n};\n\n/**\n * Converts the Long to a string written in the specified radix.\n * @this {!Long}\n * @param {number=} radix Radix (2-36), defaults to 10\n * @returns {string}\n * @override\n * @throws {RangeError} If `radix` is out of range\n */\nLongPrototype.toString = function toString(radix) {\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix)\n        throw RangeError('radix');\n    if (this.isZero())\n        return '0';\n    if (this.isNegative()) { // Unsigned Longs are never negative\n        if (this.eq(MIN_VALUE)) {\n            // We need to change the Long value before it can be negated, so we remove\n            // the bottom-most digit in this base and then recurse to do the rest.\n            var radixLong = fromNumber(radix),\n                div = this.div(radixLong),\n                rem1 = div.mul(radixLong).sub(this);\n            return div.toString(radix) + rem1.toInt().toString(radix);\n        } else\n            return '-' + this.neg().toString(radix);\n    }\n\n    // Do several (6) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n        rem = this;\n    var result = '';\n    while (true) {\n        var remDiv = rem.div(radixToPower),\n            intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n            digits = intval.toString(radix);\n        rem = remDiv;\n        if (rem.isZero())\n            return digits + result;\n        else {\n            while (digits.length < 6)\n                digits = '0' + digits;\n            result = '' + digits + result;\n        }\n    }\n};\n\n/**\n * Gets the high 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed high bits\n */\nLongPrototype.getHighBits = function getHighBits() {\n    return this.high;\n};\n\n/**\n * Gets the high 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned high bits\n */\nLongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n    return this.high >>> 0;\n};\n\n/**\n * Gets the low 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed low bits\n */\nLongPrototype.getLowBits = function getLowBits() {\n    return this.low;\n};\n\n/**\n * Gets the low 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned low bits\n */\nLongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n    return this.low >>> 0;\n};\n\n/**\n * Gets the number of bits needed to represent the absolute value of this Long.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n    if (this.isNegative()) // Unsigned Longs are never negative\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n    var val = this.high != 0 ? this.high : this.low;\n    for (var bit = 31; bit > 0; bit--)\n        if ((val & (1 << bit)) != 0)\n            break;\n    return this.high != 0 ? bit + 33 : bit + 1;\n};\n\n/**\n * Tests if this Long's value equals zero.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isZero = function isZero() {\n    return this.high === 0 && this.low === 0;\n};\n\n/**\n * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n * @returns {boolean}\n */\nLongPrototype.eqz = LongPrototype.isZero;\n\n/**\n * Tests if this Long's value is negative.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isNegative = function isNegative() {\n    return !this.unsigned && this.high < 0;\n};\n\n/**\n * Tests if this Long's value is positive.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isPositive = function isPositive() {\n    return this.unsigned || this.high >= 0;\n};\n\n/**\n * Tests if this Long's value is odd.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isOdd = function isOdd() {\n    return (this.low & 1) === 1;\n};\n\n/**\n * Tests if this Long's value is even.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isEven = function isEven() {\n    return (this.low & 1) === 0;\n};\n\n/**\n * Tests if this Long's value equals the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.equals = function equals(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    if (this.unsigned !== other.unsigned && (this.high >>> 31) === 1 && (other.high >>> 31) === 1)\n        return false;\n    return this.high === other.high && this.low === other.low;\n};\n\n/**\n * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.eq = LongPrototype.equals;\n\n/**\n * Tests if this Long's value differs from the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.notEquals = function notEquals(other) {\n    return !this.eq(/* validates */ other);\n};\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.neq = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ne = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value is less than the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThan = function lessThan(other) {\n    return this.comp(/* validates */ other) < 0;\n};\n\n/**\n * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lt = LongPrototype.lessThan;\n\n/**\n * Tests if this Long's value is less than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n    return this.comp(/* validates */ other) <= 0;\n};\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.le = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThan = function greaterThan(other) {\n    return this.comp(/* validates */ other) > 0;\n};\n\n/**\n * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gt = LongPrototype.greaterThan;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n    return this.comp(/* validates */ other) >= 0;\n};\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n/**\n * Compares this Long's value with the specified's.\n * @this {!Long}\n * @param {!Long|number|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.compare = function compare(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    if (this.eq(other))\n        return 0;\n    var thisNeg = this.isNegative(),\n        otherNeg = other.isNegative();\n    if (thisNeg && !otherNeg)\n        return -1;\n    if (!thisNeg && otherNeg)\n        return 1;\n    // At this point the sign bits are the same\n    if (!this.unsigned)\n        return this.sub(other).isNegative() ? -1 : 1;\n    // Both are positive if at least one is unsigned\n    return (other.high >>> 0) > (this.high >>> 0) || (other.high === this.high && (other.low >>> 0) > (this.low >>> 0)) ? -1 : 1;\n};\n\n/**\n * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n * @function\n * @param {!Long|number|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.comp = LongPrototype.compare;\n\n/**\n * Negates this Long's value.\n * @this {!Long}\n * @returns {!Long} Negated Long\n */\nLongPrototype.negate = function negate() {\n    if (!this.unsigned && this.eq(MIN_VALUE))\n        return MIN_VALUE;\n    return this.not().add(ONE);\n};\n\n/**\n * Negates this Long's value. This is an alias of {@link Long#negate}.\n * @function\n * @returns {!Long} Negated Long\n */\nLongPrototype.neg = LongPrototype.negate;\n\n/**\n * Returns the sum of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} addend Addend\n * @returns {!Long} Sum\n */\nLongPrototype.add = function add(addend) {\n    if (!isLong(addend))\n        addend = fromValue(addend);\n\n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n\n    var b48 = addend.high >>> 16;\n    var b32 = addend.high & 0xFFFF;\n    var b16 = addend.low >>> 16;\n    var b00 = addend.low & 0xFFFF;\n\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n    c00 += a00 + b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 + b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 + b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 + b48;\n    c48 &= 0xFFFF;\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the difference of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.subtract = function subtract(subtrahend) {\n    if (!isLong(subtrahend))\n        subtrahend = fromValue(subtrahend);\n    return this.add(subtrahend.neg());\n};\n\n/**\n * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n * @function\n * @param {!Long|number|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.sub = LongPrototype.subtract;\n\n/**\n * Returns the product of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.multiply = function multiply(multiplier) {\n    if (this.isZero())\n        return ZERO;\n    if (!isLong(multiplier))\n        multiplier = fromValue(multiplier);\n\n    // use wasm support if present\n    if (wasm) {\n        var low = wasm[\"mul\"](this.low,\n                              this.high,\n                              multiplier.low,\n                              multiplier.high);\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    if (multiplier.isZero())\n        return ZERO;\n    if (this.eq(MIN_VALUE))\n        return multiplier.isOdd() ? MIN_VALUE : ZERO;\n    if (multiplier.eq(MIN_VALUE))\n        return this.isOdd() ? MIN_VALUE : ZERO;\n\n    if (this.isNegative()) {\n        if (multiplier.isNegative())\n            return this.neg().mul(multiplier.neg());\n        else\n            return this.neg().mul(multiplier).neg();\n    } else if (multiplier.isNegative())\n        return this.mul(multiplier.neg()).neg();\n\n    // If both longs are small, use float multiplication\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n        return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n\n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n    // We can skip products that would overflow.\n\n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n\n    var b48 = multiplier.high >>> 16;\n    var b32 = multiplier.high & 0xFFFF;\n    var b16 = multiplier.low >>> 16;\n    var b00 = multiplier.low & 0xFFFF;\n\n    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n    c00 += a00 * b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 * b00;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c16 += a00 * b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 * b00;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a16 * b16;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a00 * b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n    c48 &= 0xFFFF;\n    return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n * @function\n * @param {!Long|number|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.mul = LongPrototype.multiply;\n\n/**\n * Returns this Long divided by the specified. The result is signed if this Long is signed or\n *  unsigned if this Long is unsigned.\n * @this {!Long}\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.divide = function divide(divisor) {\n    if (!isLong(divisor))\n        divisor = fromValue(divisor);\n    if (divisor.isZero())\n        throw Error('division by zero');\n\n    // use wasm support if present\n    if (wasm) {\n        // guard against signed division overflow: the largest\n        // negative number / -1 would be 1 larger than the largest\n        // positive number, due to two's complement.\n        if (!this.unsigned &&\n            this.high === -0x80000000 &&\n            divisor.low === -1 && divisor.high === -1) {\n            // be consistent with non-wasm code path\n            return this;\n        }\n        var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n            this.low,\n            this.high,\n            divisor.low,\n            divisor.high\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    if (this.isZero())\n        return this.unsigned ? UZERO : ZERO;\n    var approx, rem, res;\n    if (!this.unsigned) {\n        // This section is only relevant for signed longs and is derived from the\n        // closure library as a whole.\n        if (this.eq(MIN_VALUE)) {\n            if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n                return MIN_VALUE;  // recall that -MIN_VALUE == MIN_VALUE\n            else if (divisor.eq(MIN_VALUE))\n                return ONE;\n            else {\n                // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n                var halfThis = this.shr(1);\n                approx = halfThis.div(divisor).shl(1);\n                if (approx.eq(ZERO)) {\n                    return divisor.isNegative() ? ONE : NEG_ONE;\n                } else {\n                    rem = this.sub(divisor.mul(approx));\n                    res = approx.add(rem.div(divisor));\n                    return res;\n                }\n            }\n        } else if (divisor.eq(MIN_VALUE))\n            return this.unsigned ? UZERO : ZERO;\n        if (this.isNegative()) {\n            if (divisor.isNegative())\n                return this.neg().div(divisor.neg());\n            return this.neg().div(divisor).neg();\n        } else if (divisor.isNegative())\n            return this.div(divisor.neg()).neg();\n        res = ZERO;\n    } else {\n        // The algorithm below has not been made for unsigned longs. It's therefore\n        // required to take special care of the MSB prior to running it.\n        if (!divisor.unsigned)\n            divisor = divisor.toUnsigned();\n        if (divisor.gt(this))\n            return UZERO;\n        if (divisor.gt(this.shru(1))) // 15 >>> 1 = 7 ; with divisor = 8 ; true\n            return UONE;\n        res = UZERO;\n    }\n\n    // Repeat the following until the remainder is less than other:  find a\n    // floating-point that approximates remainder / other *from below*, add this\n    // into the result, and subtract it from the remainder.  It is critical that\n    // the approximate value is less than or equal to the real value so that the\n    // remainder never becomes negative.\n    rem = this;\n    while (rem.gte(divisor)) {\n        // Approximate the result of division. This may be a little greater or\n        // smaller than the actual value.\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n        // We will tweak the approximate result by changing it in the 48-th digit or\n        // the smallest non-fractional digit, whichever is larger.\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n            delta = (log2 <= 48) ? 1 : pow_dbl(2, log2 - 48),\n\n        // Decrease the approximation until it is smaller than the remainder.  Note\n        // that if it is too large, the product overflows and is negative.\n            approxRes = fromNumber(approx),\n            approxRem = approxRes.mul(divisor);\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\n            approx -= delta;\n            approxRes = fromNumber(approx, this.unsigned);\n            approxRem = approxRes.mul(divisor);\n        }\n\n        // We know the answer can't be zero... and actually, zero would cause\n        // infinite recursion since we would make no progress.\n        if (approxRes.isZero())\n            approxRes = ONE;\n\n        res = res.add(approxRes);\n        rem = rem.sub(approxRem);\n    }\n    return res;\n};\n\n/**\n * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.div = LongPrototype.divide;\n\n/**\n * Returns this Long modulo the specified.\n * @this {!Long}\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.modulo = function modulo(divisor) {\n    if (!isLong(divisor))\n        divisor = fromValue(divisor);\n\n    // use wasm support if present\n    if (wasm) {\n        var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n            this.low,\n            this.high,\n            divisor.low,\n            divisor.high\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n\n    return this.sub(this.div(divisor).mul(divisor));\n};\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.mod = LongPrototype.modulo;\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.rem = LongPrototype.modulo;\n\n/**\n * Returns the bitwise NOT of this Long.\n * @this {!Long}\n * @returns {!Long}\n */\nLongPrototype.not = function not() {\n    return fromBits(~this.low, ~this.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise AND of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.and = function and(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise OR of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.or = function or(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise XOR of this Long and the given one.\n * @this {!Long}\n * @param {!Long|number|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.xor = function xor(other) {\n    if (!isLong(other))\n        other = fromValue(other);\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftLeft = function shiftLeft(numBits) {\n    if (isLong(numBits))\n        numBits = numBits.toInt();\n    if ((numBits &= 63) === 0)\n        return this;\n    else if (numBits < 32)\n        return fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);\n    else\n        return fromBits(0, this.low << (numBits - 32), this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shl = LongPrototype.shiftLeft;\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRight = function shiftRight(numBits) {\n    if (isLong(numBits))\n        numBits = numBits.toInt();\n    if ((numBits &= 63) === 0)\n        return this;\n    else if (numBits < 32)\n        return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);\n    else\n        return fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);\n};\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr = LongPrototype.shiftRight;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits < 32) return fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >>> numBits, this.unsigned);\n    if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n    return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n};\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits rotated to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateLeft = function rotateLeft(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n        b = (32 - numBits);\n        return fromBits(((this.low << numBits) | (this.high >>> b)), ((this.high << numBits) | (this.low >>> b)), this.unsigned);\n    }\n    numBits -= 32;\n    b = (32 - numBits);\n    return fromBits(((this.high << numBits) | (this.low >>> b)), ((this.low << numBits) | (this.high >>> b)), this.unsigned);\n}\n/**\n * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotl = LongPrototype.rotateLeft;\n\n/**\n * Returns this Long with bits rotated to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateRight = function rotateRight(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n        b = (32 - numBits);\n        return fromBits(((this.high << b) | (this.low >>> numBits)), ((this.low << b) | (this.high >>> numBits)), this.unsigned);\n    }\n    numBits -= 32;\n    b = (32 - numBits);\n    return fromBits(((this.low << b) | (this.high >>> numBits)), ((this.high << b) | (this.low >>> numBits)), this.unsigned);\n}\n/**\n * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotr = LongPrototype.rotateRight;\n\n/**\n * Converts this Long to signed.\n * @this {!Long}\n * @returns {!Long} Signed long\n */\nLongPrototype.toSigned = function toSigned() {\n    if (!this.unsigned)\n        return this;\n    return fromBits(this.low, this.high, false);\n};\n\n/**\n * Converts this Long to unsigned.\n * @this {!Long}\n * @returns {!Long} Unsigned long\n */\nLongPrototype.toUnsigned = function toUnsigned() {\n    if (this.unsigned)\n        return this;\n    return fromBits(this.low, this.high, true);\n};\n\n/**\n * Converts this Long to its byte representation.\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @this {!Long}\n * @returns {!Array.<number>} Byte representation\n */\nLongPrototype.toBytes = function toBytes(le) {\n    return le ? this.toBytesLE() : this.toBytesBE();\n};\n\n/**\n * Converts this Long to its little endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Little endian byte representation\n */\nLongPrototype.toBytesLE = function toBytesLE() {\n    var hi = this.high,\n        lo = this.low;\n    return [\n        lo        & 0xff,\n        lo >>>  8 & 0xff,\n        lo >>> 16 & 0xff,\n        lo >>> 24       ,\n        hi        & 0xff,\n        hi >>>  8 & 0xff,\n        hi >>> 16 & 0xff,\n        hi >>> 24\n    ];\n};\n\n/**\n * Converts this Long to its big endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Big endian byte representation\n */\nLongPrototype.toBytesBE = function toBytesBE() {\n    var hi = this.high,\n        lo = this.low;\n    return [\n        hi >>> 24       ,\n        hi >>> 16 & 0xff,\n        hi >>>  8 & 0xff,\n        hi        & 0xff,\n        lo >>> 24       ,\n        lo >>> 16 & 0xff,\n        lo >>>  8 & 0xff,\n        lo        & 0xff\n    ];\n};\n\n/**\n * Creates a Long from its byte representation.\n * @param {!Array.<number>} bytes Byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytes = function fromBytes(bytes, unsigned, le) {\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\n};\n\n/**\n * Creates a Long from its little endian byte representation.\n * @param {!Array.<number>} bytes Little endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n    return new Long(\n        bytes[0]       |\n        bytes[1] <<  8 |\n        bytes[2] << 16 |\n        bytes[3] << 24,\n        bytes[4]       |\n        bytes[5] <<  8 |\n        bytes[6] << 16 |\n        bytes[7] << 24,\n        unsigned\n    );\n};\n\n/**\n * Creates a Long from its big endian byte representation.\n * @param {!Array.<number>} bytes Big endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n    return new Long(\n        bytes[4] << 24 |\n        bytes[5] << 16 |\n        bytes[6] <<  8 |\n        bytes[7],\n        bytes[0] << 24 |\n        bytes[1] << 16 |\n        bytes[2] <<  8 |\n        bytes[3],\n        unsigned\n    );\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/long.js\n// module id = 0\n// module chunks = 0"], "sourceRoot": ""}