{"version": 3, "file": "workbox-cacheable-response.prod.js", "sources": ["../_version.js", "../CacheableResponse.js", "../CacheableResponsePlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:cacheable-response:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * This class allows you to set up rules determining what\n * status codes and/or headers need to be present in order for a\n * [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n * to be considered cacheable.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponse {\n    /**\n     * To construct a new CacheableResponse instance you must provide at least\n     * one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.statuses || config.headers)) {\n                throw new WorkboxError('statuses-or-headers-required', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.statuses) {\n                assert.isArray(config.statuses, {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.statuses',\n                });\n            }\n            if (config.headers) {\n                assert.isType(config.headers, 'object', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.headers',\n                });\n            }\n        }\n        this._statuses = config.statuses;\n        this._headers = config.headers;\n    }\n    /**\n     * Checks a response to see whether it's cacheable or not, based on this\n     * object's configuration.\n     *\n     * @param {Response} response The response whose cacheability is being\n     * checked.\n     * @return {boolean} `true` if the `Response` is cacheable, and `false`\n     * otherwise.\n     */\n    isResponseCacheable(response) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(response, Response, {\n                moduleName: 'workbox-cacheable-response',\n                className: 'CacheableResponse',\n                funcName: 'isResponseCacheable',\n                paramName: 'response',\n            });\n        }\n        let cacheable = true;\n        if (this._statuses) {\n            cacheable = this._statuses.includes(response.status);\n        }\n        if (this._headers && cacheable) {\n            cacheable = Object.keys(this._headers).some((headerName) => {\n                return response.headers.get(headerName) === this._headers[headerName];\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!cacheable) {\n                logger.groupCollapsed(`The request for ` +\n                    `'${getFriendlyURL(response.url)}' returned a response that does ` +\n                    `not meet the criteria for being cached.`);\n                logger.groupCollapsed(`View cacheability criteria here.`);\n                logger.log(`Cacheable statuses: ` + JSON.stringify(this._statuses));\n                logger.log(`Cacheable headers: ` + JSON.stringify(this._headers, null, 2));\n                logger.groupEnd();\n                const logFriendlyHeaders = {};\n                response.headers.forEach((value, key) => {\n                    logFriendlyHeaders[key] = value;\n                });\n                logger.groupCollapsed(`View response status and headers here.`);\n                logger.log(`Response status: ${response.status}`);\n                logger.log(`Response headers: ` + JSON.stringify(logFriendlyHeaders, null, 2));\n                logger.groupEnd();\n                logger.groupCollapsed(`View full response details here.`);\n                logger.log(response.headers);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n            }\n        }\n        return cacheable;\n    }\n}\nexport { CacheableResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { CacheableResponse, } from './CacheableResponse.js';\nimport './_version.js';\n/**\n * A class implementing the `cacheWillUpdate` lifecycle callback. This makes it\n * easier to add in cacheability checks to requests made via Workbox's built-in\n * strategies.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponsePlugin {\n    /**\n     * To construct a new CacheableResponsePlugin instance you must provide at\n     * least one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config) {\n        /**\n         * @param {Object} options\n         * @param {Response} options.response\n         * @return {Response|null}\n         * @private\n         */\n        this.cacheWillUpdate = async ({ response }) => {\n            if (this._cacheableResponse.isResponseCacheable(response)) {\n                return response;\n            }\n            return null;\n        };\n        this._cacheableResponse = new CacheableResponse(config);\n    }\n}\nexport { CacheableResponsePlugin };\n"], "names": ["self", "_", "e", "CacheableResponse", "constructor", "config", "_statuses", "statuses", "_headers", "headers", "isResponseCacheable", "response", "cacheable", "this", "includes", "status", "Object", "keys", "some", "headerName", "get", "cacheWillUpdate", "async", "_cacheableResponse"], "mappings": "sFAEA,IACIA,KAAK,qCAAuCC,GAC/C,CACD,MAAOC,ICeP,MAAMC,EAeFC,YAAYC,EAAS,SA0BZC,EAAYD,EAAOE,cACnBC,EAAWH,EAAOI,QAW3BC,oBAAoBC,OASZC,GAAY,SACZC,KAAKP,IACLM,EAAYC,KAAKP,EAAUQ,SAASH,EAASI,SAE7CF,KAAKL,GAAYI,IACjBA,EAAYI,OAAOC,KAAKJ,KAAKL,GAAUU,MAAMC,GAClCR,EAASF,QAAQW,IAAID,KAAgBN,KAAKL,EAASW,MA2B3DP,0DCnGf,MAeIR,YAAYC,QAOHgB,gBAAkBC,OAASX,cACxBE,KAAKU,EAAmBb,oBAAoBC,GACrCA,EAEJ,UAENY,EAAqB,IAAIpB,EAAkBE"}