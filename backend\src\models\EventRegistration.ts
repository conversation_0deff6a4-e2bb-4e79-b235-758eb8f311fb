import mongoose, { Document, Schema } from 'mongoose';

export interface IEventRegistration extends Document {
  event: mongoose.Types.ObjectId;
  student: mongoose.Types.ObjectId;
  registrationDate: Date;
  status: 'registered' | 'attended' | 'cancelled';
  paymentStatus?: 'pending' | 'completed' | 'failed';
  paymentAmount?: number;
  feedback?: {
    rating: number;
    comment: string;
    submittedAt: Date;
  };
  checkInTime?: Date;
  checkOutTime?: Date;
  certificateIssued?: boolean;
  createdAt: Date;
}

const EventRegistrationSchema: Schema = new Schema({
  event: { type: Schema.Types.ObjectId, ref: 'Event', required: true },
  student: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  registrationDate: { type: Date, default: Date.now },
  status: { 
    type: String, 
    enum: ['registered', 'attended', 'cancelled'],
    default: 'registered'
  },
  paymentStatus: { 
    type: String, 
    enum: ['pending', 'completed', 'failed']
  },
  paymentAmount: { type: Number },
  feedback: {
    rating: { type: Number, min: 1, max: 5 },
    comment: { type: String },
    submittedAt: { type: Date }
  },
  checkInTime: { type: Date },
  checkOutTime: { type: Date },
  certificateIssued: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now }
});

// Compound index to prevent duplicate registrations
EventRegistrationSchema.index({ event: 1, student: 1 }, { unique: true });

export default mongoose.model<IEventRegistration>('EventRegistration', EventRegistrationSchema);
