{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,uBAAuB,EAAW,MAAM,GAAG,CAAC;AAEhE,IAAM,UAAU,GAAiC;IAC/C,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,YAAY,CAAC;IAC7B,CAAC,aAAa,EAAE,YAAY,CAAC;IAC7B,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,WAAW,EAAE,QAAQ,CAAC;IACvB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,aAAa,EAAE,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC;CAC1E,CAAC;AAEF,QAAQ,CAAC,YAAY,EAAE;4BACT,KAAK,EAAE,MAAM,EAAE,OAAO;QAChC,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;;IAHL,KAAuC,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAAtC,IAAA,qBAAwB,EAAvB,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,OAAO,QAAA;gBAAtB,KAAK,EAAE,MAAM,EAAE,OAAO;KAIjC;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { camelCase, camelCaseTransformMerge, Options } from \".\";\n\nconst TEST_CASES: [string, string, Options?][] = [\n  [\"\", \"\"],\n  [\"test\", \"test\"],\n  [\"test string\", \"testString\"],\n  [\"Test String\", \"testString\"],\n  [\"TestV2\", \"testV2\"],\n  [\"_foo_bar_\", \"fooBar\"],\n  [\"version 1.2.10\", \"version_1_2_10\"],\n  [\"version 1.21.0\", \"version_1_21_0\"],\n  [\"version 1.2.10\", \"version1210\", { transform: camelCaseTransformMerge }],\n];\n\ndescribe(\"camel case\", () => {\n  for (const [input, result, options] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(camelCase(input, options)).toEqual(result);\n    });\n  }\n});\n"]}