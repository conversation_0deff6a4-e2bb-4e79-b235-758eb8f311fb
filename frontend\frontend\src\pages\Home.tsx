import React from 'react';
import { Link } from 'react-router-dom';

const Home: React.FC = () => {
  return (
    <div className="home-container">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-overlay">
          <div className="hero-content">
            <h1 className="hero-title">
              Discover Amazing
              <span className="highlight"> College Events</span>
            </h1>
            <p className="hero-subtitle">
              Connect with events happening at colleges near you. Join communities,
              learn new skills, and make lasting memories.
            </p>
            <div className="hero-buttons">
              <Link to="/events" className="btn-hero primary">
                <span>🎯</span>
                Browse Events
              </Link>
              <Link to="/register" className="btn-hero secondary">
                <span>🚀</span>
                Get Started
              </Link>
            </div>
            <div className="hero-stats">
              <div className="stat">
                <h3>500+</h3>
                <p>Active Events</p>
              </div>
              <div className="stat">
                <h3>50+</h3>
                <p>Partner Colleges</p>
              </div>
              <div className="stat">
                <h3>10K+</h3>
                <p>Students Connected</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container">
          <div className="section-header">
            <h2>Why Choose Our Platform?</h2>
            <p>Everything you need to discover and participate in college events</p>
          </div>

          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h3>Discover Events</h3>
              <p>Find events based on your interests, location, and academic field. Never miss out on opportunities that matter to you.</p>
              <div className="feature-link">
                <Link to="/events">Explore Events →</Link>
              </div>
            </div>

            <div className="feature-card">
              <div className="feature-icon">🤝</div>
              <h3>Connect with Peers</h3>
              <p>Meet like-minded students, build your network, and create meaningful connections that last beyond college.</p>
              <div className="feature-link">
                <Link to="/register">Join Community →</Link>
              </div>
            </div>

            <div className="feature-card">
              <div className="feature-icon">📅</div>
              <h3>Manage Events</h3>
              <p>Create, organize, and promote your own events. Reach thousands of students across multiple colleges.</p>
              <div className="feature-link">
                <Link to="/register/college">Start Organizing →</Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of students already discovering amazing events</p>
            <div className="cta-buttons">
              <Link to="/register/student" className="btn-cta primary">
                Register as Student
              </Link>
              <Link to="/register/college" className="btn-cta secondary">
                Register as College
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;