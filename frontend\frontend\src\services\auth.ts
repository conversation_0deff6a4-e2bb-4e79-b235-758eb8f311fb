import axios from 'axios';

const API_URL = 'http://localhost:5000/api/auth';

interface LoginData {
  email: string;
  password: string;
  userType: 'student' | 'college';
}

interface RegisterStudentData {
  name: string;
  email: string;
  password: string;
  age: number;
  collegeName: string;
  class: string;
  adharId: string;
  studentCardId: string;
}

interface RegisterCollegeData {
  name: string;
  email: string;
  password: string;
  address: string;
  documents?: File[];
}

// Create axios instance with error handling
const api = axios.create({
  baseURL: API_URL,
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.data);
    return response;
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const login = async (data: LoginData) => {
  try {
    const response = await api.post('/login', data);
    
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    
    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const registerStudent = async (data: RegisterStudentData) => {
  try {
    const response = await api.post('/register/student', data);
    return response.data;
  } catch (error) {
    console.error('Student registration error:', error);
    throw error;
  }
};

export const registerCollege = async (data: RegisterCollegeData) => {
  try {
    const formData = new FormData();
    
    // Add text fields
    formData.append('name', data.name);
    formData.append('email', data.email);
    formData.append('password', data.password);
    formData.append('address', data.address);
    
    // Add documents if any
    if (data.documents && data.documents.length > 0) {
      for (let i = 0; i < data.documents.length; i++) {
        formData.append('documents', data.documents[i]);
      }
    }
    
    // Log form data for debugging
    console.log('Form data entries:');
    for (const pair of formData.entries()) {
      console.log(pair[0], pair[1]);
    }
    
    const response = await api.post('/register/college', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('College registration error:', error);
    throw error;
  }
};

export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

export const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  if (userStr) return JSON.parse(userStr);
  return null;
};