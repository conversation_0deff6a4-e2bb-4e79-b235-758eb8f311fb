{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport './App.css';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport StudentSignup from './components/auth/StudentSignup';\nimport CollegeSignup from './components/auth/CollegeSignup';\nimport Admin from './pages/Admin';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const location = useLocation();\n  const isHomePage = location.pathname === '/';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [!isHomePage && /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"logo\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"College Events Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/events\",\n                children: \"Events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/dashboard\",\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: isHomePage ? \"main-home\" : \"main\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register/student\",\n          element: /*#__PURE__*/_jsxDEV(StudentSignup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register/college\",\n          element: /*#__PURE__*/_jsxDEV(CollegeSignup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(Admin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/events\",\n          element: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Events page coming soon...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), !isHomePage && /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA9 2025 College Events Platform. All rights reserved.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_s(AppContent, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "Home", "<PERSON><PERSON>", "Register", "StudentSignup", "CollegeSignup", "Admin", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "isHomePage", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport './App.css';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport StudentSignup from './components/auth/StudentSignup';\nimport CollegeSignup from './components/auth/CollegeSignup';\nimport Admin from './pages/Admin';\nimport CollegeDashboard from './pages/CollegeDashboard';\n\nfunction AppContent() {\n  const location = useLocation();\n  const isHomePage = location.pathname === '/';\n\n  return (\n    <div className=\"app-container\">\n      {!isHomePage && (\n        <header className=\"header\">\n          <div className=\"header-content\">\n            <Link to=\"/\" className=\"logo\">\n              <h1>College Events Platform</h1>\n            </Link>\n            <nav>\n              <ul>\n                <li><Link to=\"/\">Home</Link></li>\n                <li><Link to=\"/events\">Events</Link></li>\n                <li><Link to=\"/dashboard\">Dashboard</Link></li>\n                <li><Link to=\"/login\">Login</Link></li>\n                <li><Link to=\"/register\">Register</Link></li>\n                <li><Link to=\"/admin\">Admin</Link></li>\n              </ul>\n            </nav>\n          </div>\n        </header>\n      )}\n\n      <main className={isHomePage ? \"main-home\" : \"main\"}>\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/register\" element={<Register />} />\n          <Route path=\"/register/student\" element={<StudentSignup />} />\n          <Route path=\"/register/college\" element={<CollegeSignup />} />\n          <Route path=\"/admin\" element={<Admin />} />\n          <Route path=\"/events\" element={<div>Events page coming soon...</div>} />\n        </Routes>\n      </main>\n\n      {!isHomePage && (\n        <footer className=\"footer\">\n          <p>&copy; 2025 College Events Platform. All rights reserved.</p>\n        </footer>\n      )}\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <Router>\n      <AppContent />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC5F,OAAO,WAAW;AAClB,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlC,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,UAAU,GAAGD,QAAQ,CAACE,QAAQ,KAAK,GAAG;EAE5C,oBACEL,OAAA;IAAKM,SAAS,EAAC,eAAe;IAAAC,QAAA,GAC3B,CAACH,UAAU,iBACVJ,OAAA;MAAQM,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBP,OAAA;QAAKM,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BP,OAAA,CAACT,IAAI;UAACiB,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,MAAM;UAAAC,QAAA,eAC3BP,OAAA;YAAAO,QAAA,EAAI;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACPZ,OAAA;UAAAO,QAAA,eACEP,OAAA;YAAAO,QAAA,gBACEP,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,GAAG;gBAAAD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCZ,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCZ,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CZ,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCZ,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,WAAW;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CZ,OAAA;cAAAO,QAAA,eAAIP,OAAA,CAACT,IAAI;gBAACiB,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACT,eAEDZ,OAAA;MAAMM,SAAS,EAAEF,UAAU,GAAG,WAAW,GAAG,MAAO;MAAAG,QAAA,eACjDP,OAAA,CAACX,MAAM;QAAAkB,QAAA,gBACLP,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEd,OAAA,CAACP,IAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEd,OAAA,CAACN,KAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEd,OAAA,CAACL,QAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEd,OAAA,CAACJ,aAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEd,OAAA,CAACH,aAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEd,OAAA,CAACF,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CZ,OAAA,CAACV,KAAK;UAACuB,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEd,OAAA;YAAAO,QAAA,EAAK;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEN,CAACR,UAAU,iBACVJ,OAAA;MAAQM,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBP,OAAA;QAAAO,QAAA,EAAG;MAAyD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACV,EAAA,CA7CQD,UAAU;EAAA,QACAT,WAAW;AAAA;AAAAuB,EAAA,GADrBd,UAAU;AA+CnB,SAASe,GAAGA,CAAA,EAAG;EACb,oBACEhB,OAAA,CAACZ,MAAM;IAAAmB,QAAA,eACLP,OAAA,CAACC,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEb;AAACK,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}