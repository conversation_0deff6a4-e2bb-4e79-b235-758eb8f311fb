"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=e(require("postcss-selector-parser"));const t=e=>{e=Object(e);const t=Boolean(!("preserve"in e)||e.preserve),r=String(e.replaceWith||".focus-visible"),o=s.default().astSync(r);return{postcssPlugin:"postcss-focus-visible",Rule(e,{result:r}){if(!e.selector.includes(":focus-visible"))return;let c;try{const t=s.default((e=>{e.walkPseudos((e=>{":focus-visible"===e.value&&(e.nodes&&e.nodes.length||e.replaceWith(o.clone({})))}))})).processSync(e.selector);c=String(t)}catch(s){return void e.warn(r,`Failed to parse selector : ${e.selector}`)}if(void 0===c)return;if(c===e.selector)return;const l=e.clone({selector:c});t?e.before(l):e.replaceWith(l)}}};t.postcss=!0,module.exports=t;
