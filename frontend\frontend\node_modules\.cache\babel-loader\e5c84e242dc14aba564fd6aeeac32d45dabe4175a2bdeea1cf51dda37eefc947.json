{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\CollegeDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CollegeDashboard = () => {\n  _s();\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5000/api/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data.dashboard);\n      } else {\n        setError('Failed to fetch dashboard data');\n      }\n    } catch (error) {\n      setError('Network error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-error\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error Loading Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        className: \"btn-primary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this);\n  }\n  if (!dashboardData) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"college-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"College Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Welcome back! Here's what's happening with your events.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card events\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83C\\uDFAF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.eventStats.total\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-detail\",\n            children: [dashboardData.eventStats.published, \" Published\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card registrations\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.registrationStats.totalRegistrations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Registrations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-detail\",\n            children: [dashboardData.registrationStats.attendedCount, \" Attended\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card notifications\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83D\\uDCE2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.notificationStats.total\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Notifications Sent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-detail\",\n            children: [dashboardData.notificationStats.scheduled, \" Scheduled\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card collaborations\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-icon\",\n          children: \"\\uD83E\\uDD1D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.collaborationStats.accepted\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Active Collaborations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-detail\",\n            children: [dashboardData.collaborationStats.pending, \" Pending\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quick-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/events/create\",\n          className: \"action-btn create-event\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2795\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), \"Create New Event\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/notifications/create\",\n          className: \"action-btn send-notification\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDCE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), \"Send Notification\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/collaborations\",\n          className: \"action-btn manage-collaborations\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83E\\uDD1D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), \"Manage Collaborations\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/analytics\",\n          className: \"action-btn view-analytics\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), \"View Analytics\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-sections\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recent Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/events/manage\",\n            className: \"view-all\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"events-list\",\n          children: dashboardData.recentEvents.length > 0 ? dashboardData.recentEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"event-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"event-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: event.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: new Date(event.startDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"event-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${event.status}`,\n                children: event.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"participants\",\n                children: [event.currentParticipants, \"/\", event.maxParticipants || '∞']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this)]\n          }, event._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-data\",\n            children: \"No recent events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Upcoming Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/events/upcoming\",\n            className: \"view-all\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"events-list\",\n          children: dashboardData.upcomingEvents.length > 0 ? dashboardData.upcomingEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"event-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"event-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: event.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: new Date(event.startDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"location\",\n                children: event.location.city\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"event-stats\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"participants\",\n                children: [event.currentParticipants, \"/\", event.maxParticipants || '∞']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)]\n          }, event._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-data\",\n            children: \"No upcoming events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Pending Collaboration Requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/collaborations\",\n            className: \"view-all\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"collaborations-list\",\n          children: dashboardData.pendingCollaborations.length > 0 ? dashboardData.pendingCollaborations.map(collab => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"collaboration-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"collaboration-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: collab.requester.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: collab.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"collaboration-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-accept\",\n                children: \"Accept\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-reject\",\n                children: \"Reject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 19\n            }, this)]\n          }, collab._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-data\",\n            children: \"No pending collaboration requests\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recent Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/notifications\",\n            className: \"view-all\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notifications-list\",\n          children: dashboardData.recentNotifications.length > 0 ? dashboardData.recentNotifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: notification.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: notification.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status ${notification.status}`,\n                children: notification.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"clicks\",\n                children: [notification.clickCount, \" clicks\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)]\n          }, notification._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"no-data\",\n            children: \"No recent notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(CollegeDashboard, \"9UNUfqT2inwUtv8xjZQdQFFnbHk=\");\n_c = CollegeDashboard;\nexport default CollegeDashboard;\nvar _c;\n$RefreshReg$(_c, \"CollegeDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "CollegeDashboard", "_s", "dashboardData", "setDashboardData", "loading", "setLoading", "error", "setError", "fetchDashboardData", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "dashboard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "eventStats", "total", "published", "registrationStats", "totalRegistrations", "attendedCount", "notificationStats", "scheduled", "collaborationStats", "accepted", "pending", "to", "recentEvents", "length", "map", "event", "title", "Date", "startDate", "toLocaleDateString", "status", "currentParticipants", "maxParticipants", "_id", "upcomingEvents", "location", "city", "pendingCollaborations", "collab", "requester", "name", "message", "recentNotifications", "notification", "type", "clickCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/CollegeDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\ninterface DashboardData {\n  eventStats: {\n    total: number;\n    published: number;\n    ongoing: number;\n    completed: number;\n    draft: number;\n  };\n  registrationStats: {\n    totalRegistrations: number;\n    attendedCount: number;\n    cancelledCount: number;\n  };\n  notificationStats: {\n    total: number;\n    sent: number;\n    scheduled: number;\n  };\n  collaborationStats: {\n    sent: number;\n    received: number;\n    accepted: number;\n    pending: number;\n  };\n  recentEvents: any[];\n  upcomingEvents: any[];\n  recentNotifications: any[];\n  pendingCollaborations: any[];\n}\n\nconst CollegeDashboard: React.FC = () => {\n  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5000/api/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data.dashboard);\n      } else {\n        setError('Failed to fetch dashboard data');\n      }\n    } catch (error) {\n      setError('Network error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading dashboard...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"dashboard-error\">\n        <h2>Error Loading Dashboard</h2>\n        <p>{error}</p>\n        <button onClick={fetchDashboardData} className=\"btn-primary\">\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  if (!dashboardData) return null;\n\n  return (\n    <div className=\"college-dashboard\">\n      <div className=\"dashboard-header\">\n        <h1>College Dashboard</h1>\n        <p>Welcome back! Here's what's happening with your events.</p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card events\">\n          <div className=\"stat-icon\">🎯</div>\n          <div className=\"stat-content\">\n            <h3>{dashboardData.eventStats.total}</h3>\n            <p>Total Events</p>\n            <span className=\"stat-detail\">\n              {dashboardData.eventStats.published} Published\n            </span>\n          </div>\n        </div>\n\n        <div className=\"stat-card registrations\">\n          <div className=\"stat-icon\">👥</div>\n          <div className=\"stat-content\">\n            <h3>{dashboardData.registrationStats.totalRegistrations}</h3>\n            <p>Total Registrations</p>\n            <span className=\"stat-detail\">\n              {dashboardData.registrationStats.attendedCount} Attended\n            </span>\n          </div>\n        </div>\n\n        <div className=\"stat-card notifications\">\n          <div className=\"stat-icon\">📢</div>\n          <div className=\"stat-content\">\n            <h3>{dashboardData.notificationStats.total}</h3>\n            <p>Notifications Sent</p>\n            <span className=\"stat-detail\">\n              {dashboardData.notificationStats.scheduled} Scheduled\n            </span>\n          </div>\n        </div>\n\n        <div className=\"stat-card collaborations\">\n          <div className=\"stat-icon\">🤝</div>\n          <div className=\"stat-content\">\n            <h3>{dashboardData.collaborationStats.accepted}</h3>\n            <p>Active Collaborations</p>\n            <span className=\"stat-detail\">\n              {dashboardData.collaborationStats.pending} Pending\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"quick-actions\">\n        <h2>Quick Actions</h2>\n        <div className=\"action-buttons\">\n          <Link to=\"/events/create\" className=\"action-btn create-event\">\n            <span>➕</span>\n            Create New Event\n          </Link>\n          <Link to=\"/notifications/create\" className=\"action-btn send-notification\">\n            <span>📢</span>\n            Send Notification\n          </Link>\n          <Link to=\"/collaborations\" className=\"action-btn manage-collaborations\">\n            <span>🤝</span>\n            Manage Collaborations\n          </Link>\n          <Link to=\"/analytics\" className=\"action-btn view-analytics\">\n            <span>📊</span>\n            View Analytics\n          </Link>\n        </div>\n      </div>\n\n      {/* Dashboard Sections */}\n      <div className=\"dashboard-sections\">\n        {/* Recent Events */}\n        <div className=\"dashboard-section\">\n          <div className=\"section-header\">\n            <h3>Recent Events</h3>\n            <Link to=\"/events/manage\" className=\"view-all\">View All</Link>\n          </div>\n          <div className=\"events-list\">\n            {dashboardData.recentEvents.length > 0 ? (\n              dashboardData.recentEvents.map((event) => (\n                <div key={event._id} className=\"event-item\">\n                  <div className=\"event-info\">\n                    <h4>{event.title}</h4>\n                    <p>{new Date(event.startDate).toLocaleDateString()}</p>\n                  </div>\n                  <div className=\"event-stats\">\n                    <span className={`status ${event.status}`}>{event.status}</span>\n                    <span className=\"participants\">\n                      {event.currentParticipants}/{event.maxParticipants || '∞'}\n                    </span>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"no-data\">No recent events</p>\n            )}\n          </div>\n        </div>\n\n        {/* Upcoming Events */}\n        <div className=\"dashboard-section\">\n          <div className=\"section-header\">\n            <h3>Upcoming Events</h3>\n            <Link to=\"/events/upcoming\" className=\"view-all\">View All</Link>\n          </div>\n          <div className=\"events-list\">\n            {dashboardData.upcomingEvents.length > 0 ? (\n              dashboardData.upcomingEvents.map((event) => (\n                <div key={event._id} className=\"event-item\">\n                  <div className=\"event-info\">\n                    <h4>{event.title}</h4>\n                    <p>{new Date(event.startDate).toLocaleDateString()}</p>\n                    <span className=\"location\">{event.location.city}</span>\n                  </div>\n                  <div className=\"event-stats\">\n                    <span className=\"participants\">\n                      {event.currentParticipants}/{event.maxParticipants || '∞'}\n                    </span>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"no-data\">No upcoming events</p>\n            )}\n          </div>\n        </div>\n\n        {/* Pending Collaborations */}\n        <div className=\"dashboard-section\">\n          <div className=\"section-header\">\n            <h3>Pending Collaboration Requests</h3>\n            <Link to=\"/collaborations\" className=\"view-all\">View All</Link>\n          </div>\n          <div className=\"collaborations-list\">\n            {dashboardData.pendingCollaborations.length > 0 ? (\n              dashboardData.pendingCollaborations.map((collab) => (\n                <div key={collab._id} className=\"collaboration-item\">\n                  <div className=\"collaboration-info\">\n                    <h4>{collab.requester.name}</h4>\n                    <p>{collab.message}</p>\n                  </div>\n                  <div className=\"collaboration-actions\">\n                    <button className=\"btn-accept\">Accept</button>\n                    <button className=\"btn-reject\">Reject</button>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"no-data\">No pending collaboration requests</p>\n            )}\n          </div>\n        </div>\n\n        {/* Recent Notifications */}\n        <div className=\"dashboard-section\">\n          <div className=\"section-header\">\n            <h3>Recent Notifications</h3>\n            <Link to=\"/notifications\" className=\"view-all\">View All</Link>\n          </div>\n          <div className=\"notifications-list\">\n            {dashboardData.recentNotifications.length > 0 ? (\n              dashboardData.recentNotifications.map((notification) => (\n                <div key={notification._id} className=\"notification-item\">\n                  <div className=\"notification-info\">\n                    <h4>{notification.title}</h4>\n                    <p>{notification.type}</p>\n                  </div>\n                  <div className=\"notification-stats\">\n                    <span className={`status ${notification.status}`}>\n                      {notification.status}\n                    </span>\n                    <span className=\"clicks\">{notification.clickCount} clicks</span>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <p className=\"no-data\">No recent notifications</p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CollegeDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgCxC,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAuB,IAAI,CAAC;EAC9E,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdY,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCd,gBAAgB,CAACa,IAAI,CAACE,SAAS,CAAC;MAClC,CAAC,MAAM;QACLX,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdC,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKoB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrB,OAAA;QAAKoB,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCzB,OAAA;QAAAqB,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,IAAIlB,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKoB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BrB,OAAA;QAAAqB,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCzB,OAAA;QAAAqB,QAAA,EAAId;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdzB,OAAA;QAAQ0B,OAAO,EAAEjB,kBAAmB;QAACW,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACtB,aAAa,EAAE,OAAO,IAAI;EAE/B,oBACEH,OAAA;IAAKoB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCrB,OAAA;MAAKoB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BrB,OAAA;QAAAqB,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BzB,OAAA;QAAAqB,QAAA,EAAG;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBrB,OAAA;QAAKoB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrB,OAAA;YAAAqB,QAAA,EAAKlB,aAAa,CAACwB,UAAU,CAACC;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCzB,OAAA;YAAAqB,QAAA,EAAG;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnBzB,OAAA;YAAMoB,SAAS,EAAC,aAAa;YAAAC,QAAA,GAC1BlB,aAAa,CAACwB,UAAU,CAACE,SAAS,EAAC,YACtC;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrB,OAAA;YAAAqB,QAAA,EAAKlB,aAAa,CAAC2B,iBAAiB,CAACC;UAAkB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7DzB,OAAA;YAAAqB,QAAA,EAAG;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1BzB,OAAA;YAAMoB,SAAS,EAAC,aAAa;YAAAC,QAAA,GAC1BlB,aAAa,CAAC2B,iBAAiB,CAACE,aAAa,EAAC,WACjD;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrB,OAAA;YAAAqB,QAAA,EAAKlB,aAAa,CAAC8B,iBAAiB,CAACL;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDzB,OAAA;YAAAqB,QAAA,EAAG;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzBzB,OAAA;YAAMoB,SAAS,EAAC,aAAa;YAAAC,QAAA,GAC1BlB,aAAa,CAAC8B,iBAAiB,CAACC,SAAS,EAAC,YAC7C;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrB,OAAA;YAAAqB,QAAA,EAAKlB,aAAa,CAACgC,kBAAkB,CAACC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDzB,OAAA;YAAAqB,QAAA,EAAG;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5BzB,OAAA;YAAMoB,SAAS,EAAC,aAAa;YAAAC,QAAA,GAC1BlB,aAAa,CAACgC,kBAAkB,CAACE,OAAO,EAAC,UAC5C;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BrB,OAAA;QAAAqB,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBzB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrB,OAAA,CAACF,IAAI;UAACwC,EAAE,EAAC,gBAAgB;UAAClB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAC3DrB,OAAA;YAAAqB,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,oBAEhB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPzB,OAAA,CAACF,IAAI;UAACwC,EAAE,EAAC,uBAAuB;UAAClB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBACvErB,OAAA;YAAAqB,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,qBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPzB,OAAA,CAACF,IAAI;UAACwC,EAAE,EAAC,iBAAiB;UAAClB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBACrErB,OAAA;YAAAqB,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,yBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPzB,OAAA,CAACF,IAAI;UAACwC,EAAE,EAAC,YAAY;UAAClB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACzDrB,OAAA;YAAAqB,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,kBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBAEjCrB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UAAKoB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrB,OAAA;YAAAqB,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBzB,OAAA,CAACF,IAAI;YAACwC,EAAE,EAAC,gBAAgB;YAAClB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBlB,aAAa,CAACoC,YAAY,CAACC,MAAM,GAAG,CAAC,GACpCrC,aAAa,CAACoC,YAAY,CAACE,GAAG,CAAEC,KAAK,iBACnC1C,OAAA;YAAqBoB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzCrB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrB,OAAA;gBAAAqB,QAAA,EAAKqB,KAAK,CAACC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBzB,OAAA;gBAAAqB,QAAA,EAAI,IAAIuB,IAAI,CAACF,KAAK,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrB,OAAA;gBAAMoB,SAAS,EAAE,UAAUsB,KAAK,CAACK,MAAM,EAAG;gBAAA1B,QAAA,EAAEqB,KAAK,CAACK;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChEzB,OAAA;gBAAMoB,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC3BqB,KAAK,CAACM,mBAAmB,EAAC,GAAC,EAACN,KAAK,CAACO,eAAe,IAAI,GAAG;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAVEiB,KAAK,CAACQ,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWd,CACN,CAAC,gBAEFzB,OAAA;YAAGoB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC3C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UAAKoB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrB,OAAA;YAAAqB,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzB,OAAA,CAACF,IAAI;YAACwC,EAAE,EAAC,kBAAkB;YAAClB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBlB,aAAa,CAACgD,cAAc,CAACX,MAAM,GAAG,CAAC,GACtCrC,aAAa,CAACgD,cAAc,CAACV,GAAG,CAAEC,KAAK,iBACrC1C,OAAA;YAAqBoB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzCrB,OAAA;cAAKoB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBrB,OAAA;gBAAAqB,QAAA,EAAKqB,KAAK,CAACC;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBzB,OAAA;gBAAAqB,QAAA,EAAI,IAAIuB,IAAI,CAACF,KAAK,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDzB,OAAA;gBAAMoB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEqB,KAAK,CAACU,QAAQ,CAACC;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BrB,OAAA;gBAAMoB,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC3BqB,KAAK,CAACM,mBAAmB,EAAC,GAAC,EAACN,KAAK,CAACO,eAAe,IAAI,GAAG;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAVEiB,KAAK,CAACQ,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWd,CACN,CAAC,gBAEFzB,OAAA;YAAGoB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC7C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UAAKoB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrB,OAAA;YAAAqB,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvCzB,OAAA,CAACF,IAAI;YAACwC,EAAE,EAAC,iBAAiB;YAAClB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjClB,aAAa,CAACmD,qBAAqB,CAACd,MAAM,GAAG,CAAC,GAC7CrC,aAAa,CAACmD,qBAAqB,CAACb,GAAG,CAAEc,MAAM,iBAC7CvD,OAAA;YAAsBoB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAClDrB,OAAA;cAAKoB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCrB,OAAA;gBAAAqB,QAAA,EAAKkC,MAAM,CAACC,SAAS,CAACC;cAAI;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChCzB,OAAA;gBAAAqB,QAAA,EAAIkC,MAAM,CAACG;cAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCrB,OAAA;gBAAQoB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CzB,OAAA;gBAAQoB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,GARE8B,MAAM,CAACL,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASf,CACN,CAAC,gBAEFzB,OAAA;YAAGoB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC5D;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrB,OAAA;UAAKoB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrB,OAAA;YAAAqB,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BzB,OAAA,CAACF,IAAI;YAACwC,EAAE,EAAC,gBAAgB;YAAClB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChClB,aAAa,CAACwD,mBAAmB,CAACnB,MAAM,GAAG,CAAC,GAC3CrC,aAAa,CAACwD,mBAAmB,CAAClB,GAAG,CAAEmB,YAAY,iBACjD5D,OAAA;YAA4BoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACvDrB,OAAA;cAAKoB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrB,OAAA;gBAAAqB,QAAA,EAAKuC,YAAY,CAACjB;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BzB,OAAA;gBAAAqB,QAAA,EAAIuC,YAAY,CAACC;cAAI;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCrB,OAAA;gBAAMoB,SAAS,EAAE,UAAUwC,YAAY,CAACb,MAAM,EAAG;gBAAA1B,QAAA,EAC9CuC,YAAY,CAACb;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACPzB,OAAA;gBAAMoB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,GAAEuC,YAAY,CAACE,UAAU,EAAC,SAAO;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GAVEmC,YAAY,CAACV,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWrB,CACN,CAAC,gBAEFzB,OAAA;YAAGoB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAClD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CArPID,gBAA0B;AAAA8D,EAAA,GAA1B9D,gBAA0B;AAuPhC,eAAeA,gBAAgB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}