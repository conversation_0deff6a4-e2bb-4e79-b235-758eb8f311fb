import { Request, Response } from 'express';
import Event from '../models/Event';
import EventRegistration from '../models/EventRegistration';
import Notification from '../models/Notification';
import Collaboration from '../models/Collaboration';
import College from '../models/College';

// Get comprehensive dashboard data for college
export const getCollegeDashboard = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    // Event Statistics
    const eventStats = {
      total: await Event.countDocuments({ organizer: collegeId }),
      published: await Event.countDocuments({ organizer: collegeId, status: 'published' }),
      ongoing: await Event.countDocuments({ organizer: collegeId, status: 'ongoing' }),
      completed: await Event.countDocuments({ organizer: collegeId, status: 'completed' }),
      draft: await Event.countDocuments({ organizer: collegeId, status: 'draft' })
    };

    // Registration Statistics
    const registrationStats = await EventRegistration.aggregate([
      {
        $lookup: {
          from: 'events',
          localField: 'event',
          foreignField: '_id',
          as: 'eventData'
        }
      },
      {
        $match: {
          'eventData.organizer': collegeId
        }
      },
      {
        $group: {
          _id: null,
          totalRegistrations: { $sum: 1 },
          attendedCount: {
            $sum: { $cond: [{ $eq: ['$status', 'attended'] }, 1, 0] }
          },
          cancelledCount: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          }
        }
      }
    ]);

    const regStats = registrationStats[0] || {
      totalRegistrations: 0,
      attendedCount: 0,
      cancelledCount: 0
    };

    // Notification Statistics
    const notificationStats = {
      total: await Notification.countDocuments({ sender: collegeId }),
      sent: await Notification.countDocuments({ sender: collegeId, status: 'sent' }),
      scheduled: await Notification.countDocuments({ sender: collegeId, status: 'scheduled' })
    };

    // Collaboration Statistics
    const collaborationStats = {
      sent: await Collaboration.countDocuments({ requester: collegeId }),
      received: await Collaboration.countDocuments({ requestee: collegeId }),
      accepted: await Collaboration.countDocuments({
        $or: [{ requester: collegeId }, { requestee: collegeId }],
        status: 'accepted'
      }),
      pending: await Collaboration.countDocuments({
        requestee: collegeId,
        status: 'pending'
      })
    };

    // Recent Events
    const recentEvents = await Event.find({ organizer: collegeId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title startDate status currentParticipants maxParticipants');

    // Upcoming Events
    const upcomingEvents = await Event.find({
      organizer: collegeId,
      startDate: { $gte: new Date() },
      status: 'published'
    })
      .sort({ startDate: 1 })
      .limit(5)
      .select('title startDate location.city currentParticipants maxParticipants');

    // Recent Notifications
    const recentNotifications = await Notification.find({ sender: collegeId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title type status sentAt clickCount');

    // Pending Collaboration Requests
    const pendingCollaborations = await Collaboration.find({
      requestee: collegeId,
      status: 'pending'
    })
      .populate('requester', 'name email')
      .sort({ createdAt: -1 })
      .limit(5);

    // Monthly Event Analytics
    const monthlyAnalytics = await Event.aggregate([
      {
        $match: {
          organizer: collegeId,
          createdAt: {
            $gte: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Top Performing Events
    const topEvents = await Event.find({ organizer: collegeId })
      .sort({ 'analytics.registrations': -1 })
      .limit(5)
      .select('title analytics.registrations analytics.views feedback.averageRating');

    res.json({
      success: true,
      dashboard: {
        eventStats,
        registrationStats: regStats,
        notificationStats,
        collaborationStats,
        recentEvents,
        upcomingEvents,
        recentNotifications,
        pendingCollaborations,
        monthlyAnalytics,
        topEvents
      }
    });
  } catch (error) {
    console.error('Get dashboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get event performance metrics
export const getEventPerformanceMetrics = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;
    const { eventId } = req.params;

    // Verify event ownership
    const event = await Event.findOne({ _id: eventId, organizer: collegeId });
    if (!event) {
      return res.status(404).json({ message: 'Event not found or not authorized' });
    }

    // Registration metrics
    const registrationMetrics = await EventRegistration.aggregate([
      { $match: { event: eventId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Daily registration trend
    const registrationTrend = await EventRegistration.aggregate([
      { $match: { event: eventId } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$registrationDate' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Feedback summary
    const feedbackSummary = await EventRegistration.aggregate([
      { $match: { event: eventId, 'feedback.rating': { $exists: true } } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$feedback.rating' },
          totalFeedbacks: { $sum: 1 },
          ratingDistribution: {
            $push: '$feedback.rating'
          }
        }
      }
    ]);

    res.json({
      success: true,
      metrics: {
        registrationMetrics,
        registrationTrend,
        feedbackSummary: feedbackSummary[0] || null,
        eventAnalytics: event.analytics
      }
    });
  } catch (error) {
    console.error('Get event performance metrics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get college profile summary
export const getCollegeProfileSummary = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    const college = await College.findById(collegeId).select('-password');
    if (!college) {
      return res.status(404).json({ message: 'College not found' });
    }

    // Calculate profile completion percentage
    const requiredFields = ['name', 'email', 'phone', 'address', 'description', 'website'];
    const completedFields = requiredFields.filter(field => college[field as keyof typeof college]);
    const profileCompletion = (completedFields.length / requiredFields.length) * 100;

    res.json({
      success: true,
      profile: {
        ...college.toObject(),
        profileCompletion: Math.round(profileCompletion)
      }
    });
  } catch (error) {
    console.error('Get college profile summary error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
