/*!
 2022 <PERSON> <<EMAIL>>
 @version 8.0.7
*/
!function(i,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(i="undefined"!=typeof globalThis?globalThis:i||self).filesize=t()}(this,(function(){"use strict";const i=/^(b|B)$/,t={iec:{bits:["bit","Kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["bit","Kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],bytes:["B","K<PERSON>","<PERSON>","GB","TB","PB","EB","ZB","YB"]}},e={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]},o={floor:Math.floor,ceil:Math.ceil};function n(n,r={}){let a,b,s,l,c,p,d,f,u,B,h,g,y,M,m,x,v,N,T,j,E,w=[],P=0;if(isNaN(n))throw new TypeError("Invalid number");if(s=!0===r.bits,m=!0===r.unix,g=!0===r.pad,b=r.base||10,y=void 0!==r.round?r.round:m?1:2,d=void 0!==r.locale?r.locale:"",f=r.localeOptions||{},x=void 0!==r.separator?r.separator:"",v=void 0!==r.spacer?r.spacer:m?"":" ",T=r.symbols||{},N=2===b?r.standard||"iec":"jedec",h=r.output||"string",c=!0===r.fullform,p=r.fullforms instanceof Array?r.fullforms:[],a=void 0!==r.exponent?r.exponent:-1,j=o[r.roundingMethod]||Math.round,B=Number(n),u=B<0,l=b>2?1e3:1024,E=!1===isNaN(r.precision)?parseInt(r.precision,10):0,u&&(B=-B),(-1===a||isNaN(a))&&(a=Math.floor(Math.log(B)/Math.log(l)),a<0&&(a=0)),a>8&&(E>0&&(E+=8-a),a=8),"exponent"===h)return a;if(0===B)w[0]=0,M=w[1]=m?"":t[N][s?"bits":"bytes"][a];else{P=B/(2===b?Math.pow(2,10*a):Math.pow(1e3,a)),s&&(P*=8,P>=l&&a<8&&(P/=l,a++));const e=Math.pow(10,a>0?y:0);w[0]=j(P*e)/e,w[0]===l&&a<8&&void 0===r.exponent&&(w[0]=1,a++),M=w[1]=10===b&&1===a?s?"kbit":"kB":t[N][s?"bits":"bytes"][a],m&&(w[1]=w[1].charAt(0),i.test(w[1])&&(w[0]=Math.floor(w[0]),w[1]=""))}if(u&&(w[0]=-w[0]),E>0&&(w[0]=w[0].toPrecision(E)),w[1]=T[w[1]]||w[1],!0===d?w[0]=w[0].toLocaleString():d.length>0?w[0]=w[0].toLocaleString(d,f):x.length>0&&(w[0]=w[0].toString().replace(".",x)),g&&!1===Number.isInteger(w[0])&&y>0){const i=x||".",t=w[0].toString().split(i),e=t[1]||"",o=e.length,n=y-o;w[0]=`${t[0]}${i}${e.padEnd(o+n,"0")}`}return c&&(w[1]=p[a]?p[a]:e[N][a]+(s?"bit":"byte")+(1===w[0]?"":"s")),"array"===h?w:"object"===h?{value:w[0],symbol:w[1],exponent:a,unit:M}:w.join(v)}return n.partial=i=>t=>n(t,i),n}));
//# sourceMappingURL=filesize.es6.min.js.map
