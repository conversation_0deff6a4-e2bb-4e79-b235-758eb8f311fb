{"version": 3, "file": "magic-string.umd.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../node_modules/sourcemap-codec/dist/sourcemap-codec.es.js", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js", "../src/index-legacy.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}\n", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\t// we make these non-enumerable, for sanity while debugging\n\t\tObject.defineProperties(this, {\n\t\t\tprevious: { writable: true, value: null },\n\t\t\tnext: { writable: true, value: null },\n\t\t});\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.end - trimmed.length);\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "var charToInteger = {};\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nfor (var i = 0; i < chars.length; i++) {\n    charToInteger[chars.charCodeAt(i)] = i;\n}\nfunction decode(mappings) {\n    var decoded = [];\n    var line = [];\n    var segment = [\n        0,\n        0,\n        0,\n        0,\n        0,\n    ];\n    var j = 0;\n    for (var i = 0, shift = 0, value = 0; i < mappings.length; i++) {\n        var c = mappings.charCodeAt(i);\n        if (c === 44) { // \",\"\n            segmentify(line, segment, j);\n            j = 0;\n        }\n        else if (c === 59) { // \";\"\n            segmentify(line, segment, j);\n            j = 0;\n            decoded.push(line);\n            line = [];\n            segment[0] = 0;\n        }\n        else {\n            var integer = charToInteger[c];\n            if (integer === undefined) {\n                throw new Error('Invalid character (' + String.fromCharCode(c) + ')');\n            }\n            var hasContinuationBit = integer & 32;\n            integer &= 31;\n            value += integer << shift;\n            if (hasContinuationBit) {\n                shift += 5;\n            }\n            else {\n                var shouldNegate = value & 1;\n                value >>>= 1;\n                if (shouldNegate) {\n                    value = value === 0 ? -0x80000000 : -value;\n                }\n                segment[j] += value;\n                j++;\n                value = shift = 0; // reset\n            }\n        }\n    }\n    segmentify(line, segment, j);\n    decoded.push(line);\n    return decoded;\n}\nfunction segmentify(line, segment, j) {\n    // This looks ugly, but we're creating specialized arrays with a specific\n    // length. This is much faster than creating a new array (which v8 expands to\n    // a capacity of 17 after pushing the first item), or slicing out a subarray\n    // (which is slow). Length 4 is assumed to be the most frequent, followed by\n    // length 5 (since not everything will have an associated name), followed by\n    // length 1 (it's probably rare for a source substring to not have an\n    // associated segment data).\n    if (j === 4)\n        line.push([segment[0], segment[1], segment[2], segment[3]]);\n    else if (j === 5)\n        line.push([segment[0], segment[1], segment[2], segment[3], segment[4]]);\n    else if (j === 1)\n        line.push([segment[0]]);\n}\nfunction encode(decoded) {\n    var sourceFileIndex = 0; // second field\n    var sourceCodeLine = 0; // third field\n    var sourceCodeColumn = 0; // fourth field\n    var nameIndex = 0; // fifth field\n    var mappings = '';\n    for (var i = 0; i < decoded.length; i++) {\n        var line = decoded[i];\n        if (i > 0)\n            mappings += ';';\n        if (line.length === 0)\n            continue;\n        var generatedCodeColumn = 0; // first field\n        var lineMappings = [];\n        for (var _i = 0, line_1 = line; _i < line_1.length; _i++) {\n            var segment = line_1[_i];\n            var segmentMappings = encodeInteger(segment[0] - generatedCodeColumn);\n            generatedCodeColumn = segment[0];\n            if (segment.length > 1) {\n                segmentMappings +=\n                    encodeInteger(segment[1] - sourceFileIndex) +\n                        encodeInteger(segment[2] - sourceCodeLine) +\n                        encodeInteger(segment[3] - sourceCodeColumn);\n                sourceFileIndex = segment[1];\n                sourceCodeLine = segment[2];\n                sourceCodeColumn = segment[3];\n            }\n            if (segment.length === 5) {\n                segmentMappings += encodeInteger(segment[4] - nameIndex);\n                nameIndex = segment[4];\n            }\n            lineMappings.push(segmentMappings);\n        }\n        mappings += lineMappings.join(',');\n    }\n    return mappings;\n}\nfunction encodeInteger(num) {\n    var result = '';\n    num = num < 0 ? (-num << 1) | 1 : num << 1;\n    do {\n        var clamped = num & 31;\n        num >>>= 5;\n        if (num > 0) {\n            clamped |= 32;\n        }\n        result += chars[clamped];\n    } while (num > 0);\n    return result;\n}\n\nexport { decode, encode };\n//# sourceMappingURL=sourcemap-codec.es.js.map\n", "import { encode } from 'sourcemap-codec';\n\nlet btoa = () => {\n\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n};\nif (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n\tbtoa = (str) => window.btoa(unescape(encodeURIComponent(str)));\n} else if (typeof Buffer === 'function') {\n\tbtoa = (str) => Buffer.from(str, 'utf-8').toString('base64');\n}\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter((line) => /^\\t+/.test(line));\n\tconst spaced = lines.filter((line) => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "export default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t}\n\n\t\tthis.advance(content);\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\tthis.rawSegments.push([this.generatedCodeColumn, sourceIndex, loc.line, loc.column]);\n\t\t\t}\n\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t} else {\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false,\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal: { writable: true, value: string },\n\t\t\toutro: { writable: true, value: '' },\n\t\t\tintro: { writable: true, value: '' },\n\t\t\tfirstChunk: { writable: true, value: chunk },\n\t\t\tlastChunk: { writable: true, value: chunk },\n\t\t\tlastSearchedChunk: { writable: true, value: chunk },\n\t\t\tbyStart: { writable: true, value: {} },\n\t\t\tbyEnd: { writable: true, value: {} },\n\t\t\tfilename: { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations: { writable: true, value: new BitSet() },\n\t\t\tstoredNames: { writable: true, value: {} },\n\t\t\tindentStr: { writable: true, value: guessIndent(string) },\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext((chunk) => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: [options.source ? getRelativePath(options.file || '', options.source) : null],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : [null],\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tindentStr = indentStr !== undefined ? indentStr : this.indentStr || '\\t';\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach((exclusion) => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = (match) => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error(\n\t\t\t'magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)'\n\t\t);\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead'\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead'\n\t\t\t); // eslint-disable-line no-console\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot overwrite a zero-length range – use appendLeft or prependRight instead'\n\t\t\t);\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string'\n\t\t\t\t); // eslint-disable-line no-console\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst contentOnly = options !== undefined ? options.contentOnly : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tObject.defineProperty(this.storedNames, original, { writable: true, value: true, enumerable: true });\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tlet chunk = first;\n\t\t\twhile (chunk !== last) {\n\t\t\t\tif (chunk.next !== this.byStart[chunk.end]) {\n\t\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t\t}\n\t\t\t\tchunk = chunk.next;\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, contentOnly);\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length) return this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length) return chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length) return chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length) return chunk.intro[chunk.intro.length - 1];\n\t\t} while ((chunk = chunk.previous));\n\t\tif (this.intro.length) return this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while ((chunk = chunk.previous));\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${chunk.original}\")`\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (\n\t\t\t\t(chunk.intro.length && chunk.intro.trim()) ||\n\t\t\t\t(chunk.content.length && chunk.content.trim()) ||\n\t\t\t\t(chunk.outro.length && chunk.outro.trim())\n\t\t\t)\n\t\t\t\treturn false;\n\t\t} while ((chunk = chunk.next));\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while ((chunk = chunk.next));\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator,\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error(\n\t\t\t\t'bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`'\n\t\t\t);\n\t\t}\n\n\t\t['filename', 'indentExclusionRanges', 'separator'].forEach((option) => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || '',\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator,\n\t\t});\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator,\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tthis.sources.forEach((source) => {\n\t\t\tObject.keys(source.content.storedNames).forEach((name) => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext((chunk) => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tconst indentStr = source.content.indentStr;\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart, //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty() {\n\t\tif (this.intro.length && this.intro.trim()) return false;\n\t\tif (this.sources.some((source) => !source.content.isEmpty())) return false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce(\n\t\t\t(length, source) => length + source.content.length(),\n\t\t\tthis.intro.length\n\t\t);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport Bundle from './Bundle.js';\nimport SourceMap from './SourceMap.js';\n\nMagicString.Bundle = Bundle;\nMagicString.SourceMap = SourceMap;\nMagicString.default = MagicString; // work around TypeScript bug https://github.com/Rich-Harris/magic-string/pull/121\n\nexport default MagicString;\n"], "names": ["const", "let", "this"], "mappings": ";;;;;;CAAe,IAAM,MAAM,GAC1B,eAAW,CAAC,GAAG,EAAE;CAClB,CAAE,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;CAC3D,EAAC;AACF;kBACC,oBAAI,CAAC,EAAE;CACR,CAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;CACpC,EAAC;AACF;kBACC,oBAAI,CAAC,EAAE;CACR,CAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;CAChD;;CCXc,IAAM,KAAK,GACzB,cAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;CAClC,CAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,CAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,CAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC1B;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CAClB,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAClB;CACA,CAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;CACzB,CAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;CACzB,CAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;CACA;CACA,CAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;CAChC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;CAC5C,EAAG,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;CACxC,EAAG,CAAC,CAAC;CACJ,EAAC;AACF;iBACC,kCAAW,OAAO,EAAE;CACrB,CAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACvB,EAAC;AACF;iBACC,oCAAY,OAAO,EAAE;CACtB,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;CACnC,EAAC;AACF;iBACC,0BAAQ;CACT,CAAEA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D;CACA,CAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,CAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,CAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,CAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,CAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;CACA,CAAE,OAAO,KAAK,CAAC;CACd,EAAC;AACF;iBACC,8BAAS,KAAK,EAAE;CACjB,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;CAC/C,EAAC;AACF;iBACC,8BAAS,EAAE,EAAE;CACd,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC;CACnB,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CACb,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG;CACF,EAAC;AACF;iBACC,sCAAa,EAAE,EAAE;CAClB,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC;CACnB,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CACb,EAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;CAC1B,EAAG;CACF,EAAC;AACF;iBACC,sBAAK,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;CACvC,CAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;CACzB,CAAE,IAAI,CAAC,WAAW,EAAE;CACpB,EAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CACnB,EAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CACnB,EAAG;CACH,CAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC7B;CACA,CAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB;CACA,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;iBACC,oCAAY,OAAO,EAAE;CACtB,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAC;AACF;iBACC,sCAAa,OAAO,EAAE;CACvB,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAC;AACF;iBACC,wBAAM,KAAK,EAAE;CACd,CAAED,IAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACxC;CACA,CAAEA,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;CAC5D,CAAEA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACxD;CACA,CAAE,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;AACjC;CACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;CAC7D,CAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC9B,CAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAClB;CACA,CAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;AACnB;CACA,CAAE,IAAI,IAAI,CAAC,MAAM,EAAE;CACnB;CACA,EAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;CAC5B,EAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CACrB,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;CACjC,EAAG;AACH;CACA,CAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC5B,CAAE,IAAI,QAAQ,CAAC,IAAI,IAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAC;CACvD,CAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;CAC3B,CAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AACvB;CACA,CAAE,OAAO,QAAQ,CAAC;CACjB,EAAC;AACF;iBACC,gCAAW;CACZ,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CAC/C,EAAC;AACF;iBACC,4BAAQ,EAAE,EAAE;CACb,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;CACA,CAAEA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C;CACA,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,EAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;CACjC,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;CACtE,GAAI;CACJ,EAAG,OAAO,IAAI,CAAC;CACf,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAClC;CACA,EAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC3C,EAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;CACtC,EAAG;CACF,EAAC;AACF;iBACC,gCAAU,EAAE,EAAE;CACf,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;CACA,CAAEA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/C;CACA,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,EAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;CACjC,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC1C,GAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;CACnC,GAAI;CACJ,EAAG,OAAO,IAAI,CAAC;CACf,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAClC;CACA,EAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC3C,EAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;CACtC,EAAG;CACF;;CCxJD,IAAI,aAAa,GAAG,EAAE,CAAC;CACvB,IAAI,KAAK,GAAG,mEAAmE,CAAC;CAChF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACvC,IAAI,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAC3C,CAAC;CAmED,SAAS,MAAM,CAAC,OAAO,EAAE;CACzB,IAAI,IAAI,eAAe,GAAG,CAAC,CAAC;CAC5B,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC;CAC3B,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC;CAC7B,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;CACtB,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC;CACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC7C,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC9B,QAAQ,IAAI,CAAC,GAAG,CAAC;CACjB,YAAY,QAAQ,IAAI,GAAG,CAAC;CAC5B,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;CAC7B,YAAY,SAAS;CACrB,QAAQ,IAAI,mBAAmB,GAAG,CAAC,CAAC;CACpC,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC;CAC9B,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;CAClE,YAAY,IAAI,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;CACrC,YAAY,IAAI,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC;CAClF,YAAY,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC7C,YAAY,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;CACpC,gBAAgB,eAAe;CAC/B,oBAAoB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;CAC/D,wBAAwB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;CAClE,wBAAwB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC;CACrE,gBAAgB,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC7C,gBAAgB,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC5C,gBAAgB,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC9C,aAAa;CACb,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;CACtC,gBAAgB,eAAe,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;CACzE,gBAAgB,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACvC,aAAa;CACb,YAAY,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;CAC/C,SAAS;CACT,QAAQ,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAC3C,KAAK;CACL,IAAI,OAAO,QAAQ,CAAC;CACpB,CAAC;CACD,SAAS,aAAa,CAAC,GAAG,EAAE;CAC5B,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;CACpB,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;CAC/C,IAAI,GAAG;CACP,QAAQ,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC;CAC/B,QAAQ,GAAG,MAAM,CAAC,CAAC;CACnB,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;CACrB,YAAY,OAAO,IAAI,EAAE,CAAC;CAC1B,SAAS;CACT,QAAQ,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;CACjC,KAAK,QAAQ,GAAG,GAAG,CAAC,EAAE;CACtB,IAAI,OAAO,MAAM,CAAC;CAClB;;CCtHAC,IAAI,IAAI,eAAS;CACjB,CAAC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;CAC5F,CAAC,CAAC;CACF,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;CACxE,CAAC,IAAI,aAAI,GAAG,WAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAC,CAAC;CAChE,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;CACzC,CAAC,IAAI,aAAI,GAAG,WAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAC,CAAC;CAC9D,CAAC;AACD;CACe,IAAM,SAAS,GAC7B,kBAAW,CAAC,UAAU,EAAE;CACzB,CAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;CACnB,CAAE,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;CAC9B,CAAE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;CACpC,CAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;CAClD,CAAE,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;CAChC,CAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;CAC7C,EAAC;AACF;qBACC,gCAAW;CACZ,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CAC7B,EAAC;AACF;qBACC,0BAAQ;CACT,CAAE,OAAO,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC9E;;CC3Bc,SAAS,WAAW,CAAC,IAAI,EAAE;CAC1C,CAACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,CAACA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAE,IAAI,WAAK,MAAM,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;CAC1D,CAACA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAE,IAAI,WAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;AAC5D;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;CACjD,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA;CACA;CACA;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;CACrC,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA;CACA,CAACA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,WAAE,QAAQ,EAAE,OAAO,EAAK;CAClD,EAAEA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;CAClD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;CACvC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACd;CACA,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrC;;CCxBe,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;CAClD,CAACA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;CACvC,CAACA,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC;CACA,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACjB;CACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;CACrC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;CACpB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;CAClB,EAAE;AACF;CACA,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE;CACvB,EAAEC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;CAC3B,EAAE,OAAO,CAAC,EAAE,IAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAC;CAClC,EAAE;AACF;CACA,CAAC,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAC5C;;CCjBAD,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C;CACe,SAAS,QAAQ,CAAC,KAAK,EAAE;CACxC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;CACnD;;CCJe,SAAS,UAAU,CAAC,MAAM,EAAE;CAC3C,CAACA,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CAC1C,CAACA,IAAM,WAAW,GAAG,EAAE,CAAC;AACxB;CACA,CAAC,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACzD,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACxB,EAAE,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;CACrC,EAAE;AACF;CACA,CAAC,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;CAC/B,EAAEA,IAAI,CAAC,GAAG,CAAC,CAAC;CACZ,EAAEA,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;CAChB,GAAGD,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC1B,GAAG,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;CAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;CACV,IAAI,MAAM;CACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACd,IAAI;CACJ,GAAG;CACH,EAAEA,IAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;CACrB,EAAEA,IAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;CAC3C,EAAE,OAAO,QAAE,IAAI,UAAE,MAAM,EAAE,CAAC;CAC1B,EAAE,CAAC;CACH;;CCxBe,IAAM,QAAQ,GAC5B,iBAAW,CAAC,KAAK,EAAE;CACpB,CAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,CAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;CAC7B,CAAE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CAC/B,CAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;CAChB,CAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;CAC3D,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;AACF;oBACC,4BAAQ,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;CAC/C,CAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,EAAGA,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;CACjF,EAAG,IAAI,SAAS,IAAI,CAAC,EAAE;CACvB,GAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CAC5B,GAAI;CACJ,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CAClC,EAAG,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;CAC3B,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CACvC,EAAG;AACH;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;CACxB,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;AACF;oBACC,8CAAiB,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;CACzE,CAAEC,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;CACtC,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;CACA,CAAE,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;CACxC,EAAG,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;CACzE,GAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;CACzF,GAAI;AACJ;CACA,EAAG,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;CAC7C,GAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;CAClB,GAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;CACnB,GAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;CAChC,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;CAC7D,GAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CACjC,GAAI,KAAK,GAAG,IAAI,CAAC;CACjB,GAAI,MAAM;CACV,GAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;CACpB,GAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;CAClC,GAAI,KAAK,GAAG,KAAK,CAAC;CAClB,GAAI;AACJ;CACA,EAAG,iBAAiB,IAAI,CAAC,CAAC;CAC1B,EAAG;AACH;CACA,CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;AACF;oBACC,4BAAQ,GAAG,EAAE;CACd,CAAE,IAAI,CAAC,GAAG,IAAE,SAAO;AACnB;CACA,CAAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,CAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CACxB,EAAG,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9C,GAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;CAC7B,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;CAC7D,GAAI;CACJ,EAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CAChC,EAAG;AACH;CACA,CAAE,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;CAC5D;;CCzDDD,IAAM,CAAC,GAAG,IAAI,CAAC;AACf;CACAA,IAAM,MAAM,GAAG;CACf,CAAC,UAAU,EAAE,KAAK;CAClB,CAAC,WAAW,EAAE,KAAK;CACnB,CAAC,SAAS,EAAE,KAAK;CACjB,CAAC,CAAC;AACF;KACqB,WAAW,GAC/B,oBAAW,CAAC,MAAM,EAAE,OAAY,EAAE;mCAAP,GAAG;AAAK;CACpC,CAAEA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACpD;CACA,CAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;CAChC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;CAC9C,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,EAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CAC/C,EAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CAC9C,EAAG,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CACtD,EAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACzC,EAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,EAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;CACxD,EAAG,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;CAClF,EAAG,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;CAC9D,EAAG,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CAC7C,EAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;CAC5D,EAAG,CAAC,CAAC;AAKL;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;CAC1B,CAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;CACnC,EAAC;AACF;uBACC,sDAAqB,IAAI,EAAE;CAC5B,CAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACnC,EAAC;AACF;uBACC,0BAAO,OAAO,EAAE;CACjB,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;AACzF;CACA,CAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACxB,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,kCAAW,KAAK,EAAE,OAAO,EAAE;CAC5B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;CACA,CAAE,IAAI,KAAK,EAAE;CACb,EAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;CAC7B,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACzB,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC7B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC;CACA,CAAE,IAAI,KAAK,EAAE;CACb,EAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;CAC9B,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACzB,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,0BAAQ;CACT,CAAEA,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC7E;CACA,CAAEC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;CACtC,CAAEA,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3F;CACA,CAAE,OAAO,aAAa,EAAE;CACxB,EAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;CACnD,EAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AAC/C;CACA,EAAGD,IAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC;CAChD,EAAGA,IAAM,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC1E;CACA,EAAG,IAAI,eAAe,EAAE;CACxB,GAAI,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC;CACvC,GAAI,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAC;AAC3C;CACA,GAAI,WAAW,GAAG,eAAe,CAAC;CAClC,GAAI;AACJ;CACA,EAAG,aAAa,GAAG,iBAAiB,CAAC;CACrC,EAAG;AACH;CACA,CAAE,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;AACjC;CACA,CAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;CAClC,EAAG,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;CACrE,EAAG;AACH;CACA,CAAE,MAAM,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAClE;CACA,CAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,CAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B;CACA,CAAE,OAAO,MAAM,CAAC;CACf,EAAC;AACF;uBACC,kDAAmB,OAAO,EAAE;;AAAC;CAC9B,CAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;CACA,CAAEA,IAAM,WAAW,GAAG,CAAC,CAAC;CACxB,CAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;CAC9C,CAAEA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;CACA,CAAEA,IAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C;CACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,EAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAChC,EAAG;AACH;CACA,CAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,WAAE,KAAK,EAAK;CACtC,EAAGA,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnC;CACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AACzD;CACA,EAAG,IAAI,KAAK,CAAC,MAAM,EAAE;CACrB,GAAI,QAAQ,CAAC,OAAO;CACpB,IAAK,WAAW;CAChB,IAAK,KAAK,CAAC,OAAO;CAClB,IAAK,GAAG;CACR,IAAK,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CACzD,IAAK,CAAC;CACN,GAAI,MAAM;CACV,GAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAEE,QAAI,CAAC,QAAQ,EAAE,GAAG,EAAEA,QAAI,CAAC,kBAAkB,CAAC,CAAC;CAC/F,GAAI;AACJ;CACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;CACzD,EAAG,CAAC,CAAC;AACL;CACA,CAAE,OAAO;CACT,EAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;CAChE,EAAG,OAAO,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;CACzF,EAAG,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;CACpE,SAAG,KAAK;CACR,EAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;CACzB,EAAG,CAAC;CACH,EAAC;AACF;uBACC,oCAAY,OAAO,EAAE;CACtB,CAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;CACxD,EAAC;AACF;uBACC,8CAAkB;CACnB,CAAE,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;CACxD,EAAC;AACF;uBACC,0BAAO,SAAS,EAAE,OAAO,EAAE;CAC5B,CAAEF,IAAM,OAAO,GAAG,YAAY,CAAC;AAC/B;CACA,CAAE,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;CAC3B,EAAG,OAAO,GAAG,SAAS,CAAC;CACvB,EAAG,SAAS,GAAG,SAAS,CAAC;CACzB,EAAG;AACH;CACA,CAAE,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;AAC3E;CACA,CAAE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;AACpC;CACA,CAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;CACA;CACA,CAAEA,IAAM,UAAU,GAAG,EAAE,CAAC;AACxB;CACA,CAAE,IAAI,OAAO,CAAC,OAAO,EAAE;CACvB,EAAGA,IAAM,UAAU;CACnB,GAAI,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;CACjF,EAAG,UAAU,CAAC,OAAO,WAAE,SAAS,EAAK;CACrC,GAAI,KAAKC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;CACzD,IAAK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CAC1B,IAAK;CACL,GAAI,CAAC,CAAC;CACN,EAAG;AACH;CACA,CAAEA,IAAI,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;CAChE,CAAED,IAAM,QAAQ,aAAI,KAAK,EAAK;CAC9B,EAAG,IAAI,yBAAyB,IAAE,aAAU,YAAY,SAAQ;CAChE,EAAG,yBAAyB,GAAG,IAAI,CAAC;CACpC,EAAG,OAAO,KAAK,CAAC;CAChB,EAAG,CAAC;AACJ;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACrD;CACA,CAAEC,IAAI,SAAS,GAAG,CAAC,CAAC;CACpB,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B;CACA,CAAE,OAAO,KAAK,EAAE;CAChB,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACzB;CACA,EAAG,IAAI,KAAK,CAAC,MAAM,EAAE;CACrB,GAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;CAChC,IAAK,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC9D;CACA,IAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;CAC/B,KAAM,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC;CACnF,KAAM;CACN,IAAK;CACL,GAAI,MAAM;CACV,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC5B;CACA,GAAI,OAAO,SAAS,GAAG,GAAG,EAAE;CAC5B,IAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;CACjC,KAAMA,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC5C;CACA,KAAM,IAAI,IAAI,KAAK,IAAI,EAAE;CACzB,MAAO,yBAAyB,GAAG,IAAI,CAAC;CACxC,MAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;CAC7D,MAAO,yBAAyB,GAAG,KAAK,CAAC;AACzC;CACA,MAAO,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;CACtC,OAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;CACtC,OAAQ,MAAM;CACd,OAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;CAC3C,OAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CAC3B,OAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;CACtC,OAAQ;CACR,MAAO;CACP,KAAM;AACN;CACA,IAAK,SAAS,IAAI,CAAC,CAAC;CACpB,IAAK;CACL,GAAI;AACJ;CACA,EAAG,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC;CACzB,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG;AACH;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACrD;CACA,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,4BAAS;CACV,CAAE,MAAM,IAAI,KAAK;CACjB,EAAG,iFAAiF;CACpF,EAAG,CAAC;CACH,EAAC;AACF;uBACC,kCAAW,KAAK,EAAE,OAAO,EAAE;CAC5B,CAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;CAC1B,EAAG,OAAO,CAAC,IAAI;CACf,GAAI,oFAAoF;CACxF,GAAI,CAAC;CACL,EAAG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;CAC5B,EAAG;AACH;CACA,CAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACxC,EAAC;AACF;uBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC7B,CAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;CAC3B,EAAG,OAAO,CAAC,IAAI;CACf,GAAI,uFAAuF;CAC3F,GAAI,CAAC;CACL,EAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;CAC7B,EAAG;AACH;CACA,CAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC1C,EAAC;AACF;uBACC,sBAAK,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;CACzB,CAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,GAAC;AAG/F;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;CACnB,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACpC,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;CACA,CAAEA,IAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;CACjC,CAAEA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAC7B;CACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACvC,CAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,IAAE,OAAO,IAAI,GAAC;CACxD,CAAEA,IAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAChE;CACA,CAAE,IAAI,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,GAAC;CACvC,CAAE,IAAI,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,GAAC;AAC5C;CACA,CAAE,IAAI,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,KAAK,GAAC;CACpC,CAAE,IAAI,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAC;AACzC;CACA,CAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAC;CACnD,CAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,EAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;CACnC,EAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;CAC9B,EAAG;AACH;CACA,CAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;CAC3B,CAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;AAC/B;CACA,CAAE,IAAI,CAAC,OAAO,IAAE,IAAI,CAAC,UAAU,GAAG,KAAK,GAAC;CACxC,CAAE,IAAI,CAAC,QAAQ,IAAE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAC;CAGvC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,gCAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;CACzC,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,GAAC;AAC/F;CACA,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;CACA,CAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,GAAC;CAC1E,CAAE,IAAI,KAAK,KAAK,GAAG;CACnB,IAAG,MAAM,IAAI,KAAK;CAClB,GAAI,+EAA+E;CACnF,GAAI,GAAC;AAGL;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnB;CACA,CAAE,IAAI,OAAO,KAAK,IAAI,EAAE;CACxB,EAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;CAC1B,GAAI,OAAO,CAAC,IAAI;CAChB,IAAK,+HAA+H;CACpI,IAAK,CAAC;CACN,GAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;CAC5B,GAAI;AACJ;CACA,EAAG,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;CACjC,EAAG;CACH,CAAEA,IAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;CACtE,CAAEA,IAAM,WAAW,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAC1E;CACA,CAAE,IAAI,SAAS,EAAE;CACjB,EAAGA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CACpD,EAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;CACxG,EAAG;AACH;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACpC,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B;CACA,CAAE,IAAI,KAAK,EAAE;CACb,EAAGC,IAAI,KAAK,GAAG,KAAK,CAAC;CACrB,EAAG,OAAO,KAAK,KAAK,IAAI,EAAE;CAC1B,GAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;CAChD,IAAK,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;CAC9D,IAAK;CACL,GAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACvB,GAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;CAC1B,GAAI;AACJ;CACA,EAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;CAC/C,EAAG,MAAM;CACT;CACA,EAAGD,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACvE;CACA;CACA,EAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;CACxB,EAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;CAC5B,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,4BAAQ,OAAO,EAAE;CAClB,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;AACzF;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACpC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC7B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;CACA,CAAE,IAAI,KAAK,EAAE;CACb,EAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;CAC9B,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACrC,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,sCAAa,KAAK,EAAE,OAAO,EAAE;CAC9B,CAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;AAG5F;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC;CACA,CAAE,IAAI,KAAK,EAAE;CACb,EAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;CAC/B,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACrC,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,0BAAO,KAAK,EAAE,GAAG,EAAE;CACpB,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;CACA,CAAE,IAAI,KAAK,KAAK,GAAG,IAAE,OAAO,IAAI,GAAC;AACjC;CACA,CAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,GAAC;CAC7F,CAAE,IAAI,KAAK,GAAG,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,GAAC;AAGrE;CACA,CAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACrB,CAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnB;CACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAClC;CACA,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;CACpB,EAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;CACpB,EAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB;CACA,EAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CAC5D,EAAG;CAGH,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,gCAAW;CACZ,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CAClE,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;CAC7B,CAAE,GAAG;CACL,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CACtE,EAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CAC5E,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CACtE,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG;CACrC,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CAClE,CAAE,OAAO,EAAE,CAAC;CACX,EAAC;AACF;uBACC,gCAAW;CACZ,CAAEA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC5C,CAAE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAC;CAChE,CAAEA,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;CAC7B,CAAE,GAAG;CACL,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CAC/B,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC3C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;CAC7E,GAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;CACpC,GAAI;AACJ;CACA,EAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;CACjC,GAAI,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC7C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;CAC/E,GAAI,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;CACtC,GAAI;AACJ;CACA,EAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CAC/B,GAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC3C,GAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;CAC7E,GAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;CACpC,GAAI;CACJ,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG;CACrC,CAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACxC,CAAE,IAAI,SAAS,KAAK,CAAC,CAAC,IAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;CAC1E,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;CAC7B,EAAC;AACF;uBACC,wBAAM,KAAS,EAAE,GAA0B,EAAE;gCAAlC,GAAG;4BAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;AAAS;CAC/C,CAAE,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAClD,CAAE,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;AAC9C;CACA,CAAEA,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB;CACA;CACA,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC9B,CAAE,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;CAC/D;CACA,EAAG,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;CAC9C,GAAI,OAAO,MAAM,CAAC;CAClB,GAAI;AACJ;CACA,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG;AACH;CACA,CAAE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;CACpD,IAAG,MAAM,IAAI,KAAK,qCAAkC,KAAK,8BAA0B,GAAC;AACpF;CACA,CAAED,IAAM,UAAU,GAAG,KAAK,CAAC;CAC3B,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;CACvE,GAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;CAC1B,GAAI;AACJ;CACA,EAAGA,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;CAC7D,EAAG,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;CACvD,KAAI,MAAM,IAAI,KAAK,qCAAkC,GAAG,4BAAwB,GAAC;AACjF;CACA,EAAGA,IAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;CACrE,EAAGA,IAAM,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAChG;CACA,EAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACvD;CACA,EAAG,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;CAC3D,GAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;CAC1B,GAAI;AACJ;CACA,EAAG,IAAI,WAAW,EAAE;CACpB,GAAI,MAAM;CACV,GAAI;AACJ;CACA,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG;AACH;CACA,CAAE,OAAO,MAAM,CAAC;CACf,EAAC;AACF;CACC;uBACA,sBAAK,KAAK,EAAE,GAAG,EAAE;CAClB,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;CAC7B,CAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CACzB,CAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3C;CACA,CAAE,OAAO,KAAK,CAAC;CACd,EAAC;AACF;uBACC,0BAAO,KAAK,EAAE;CACf,CAAE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAE,SAAO;AAGvD;CACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;CACrC,CAAED,IAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AAC1C;CACA,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAC;AACpE;CACA,EAAG,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;CAC7E,EAAG;CACF,EAAC;AACF;uBACC,oCAAY,KAAK,EAAE,KAAK,EAAE;CAC3B,CAAE,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;CAC5C;CACA,EAAGA,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;CAChD,EAAG,MAAM,IAAI,KAAK;CAClB,6DAA0D,GAAG,CAAC,KAAI,UAAI,GAAG,CAAC,OAAM,cAAO,KAAK,CAAC,SAAQ;CACrG,GAAI,CAAC;CACL,EAAG;AACH;CACA,CAAEA,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACtC;CACA,CAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;CAC5B,CAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;CACjC,CAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACtC;CACA,CAAE,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAC;AAC1D;CACA,CAAE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;CAEjC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,gCAAW;CACZ,CAAEC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB;CACA,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC9B,CAAE,OAAO,KAAK,EAAE;CAChB,EAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;CAC3B,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG;AACH;CACA,CAAE,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;CACzB,EAAC;AACF;uBACC,8BAAU;CACX,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC9B,CAAE,GAAG;CACL,EAAG;CACH,GAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;CAC7C,IAAK,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAClD,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;CAC9C;CACA,KAAI,OAAO,KAAK,GAAC;CACjB,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG;CACjC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;uBACC,4BAAS;CACV,CAAEA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC9B,CAAEA,IAAI,MAAM,GAAG,CAAC,CAAC;CACjB,CAAE,GAAG;CACL,EAAG,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;CAC5E,EAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG;CACjC,CAAE,OAAO,MAAM,CAAC;CACf,EAAC;AACF;uBACC,kCAAY;CACb,CAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC9B,EAAC;AACF;uBACC,sBAAK,QAAQ,EAAE;CAChB,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CACnD,EAAC;AACF;uBACC,0CAAe,QAAQ,EAAE;CAC1B,CAAED,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;CACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;AAC7B;CACA,CAAE,GAAG;CACL,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACzB,EAAGA,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACrC;CACA;CACA,EAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;CAC1B,GAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;CAClC,IAAK,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;CACjC,IAAK;AACL;CACA,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CAClC,GAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;CAChD,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;CAC5C,GAAI;AACJ;CACA,EAAG,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;CAC5B,EAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;CAC1B,EAAG,QAAQ,KAAK,EAAE;AAClB;CACA,CAAE,OAAO,KAAK,CAAC;CACd,EAAC;AACF;uBACC,4BAAQ,QAAQ,EAAE;CACnB,CAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;CAChC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;uBACD,8CAAiB,QAAQ,EAAE;CAC5B,CAAEA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACzD;CACA,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC1C,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;AACrC;CACA,CAAEC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9B;CACA,CAAE,GAAG;CACL,EAAGD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACzB,EAAGA,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACvC;CACA,EAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;CAC1B;CACA,GAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,GAAC;AAC9D;CACA,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CAClC,GAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;CAChD,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;CAC5C,GAAI;AACJ;CACA,EAAG,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;CAC5B,EAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;CACtB,EAAG,QAAQ,KAAK,EAAE;AAClB;CACA,CAAE,OAAO,KAAK,CAAC;CACd,EAAC;AACF;uBACC,gCAAU,QAAQ,EAAE;CACrB,CAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;CAClC,CAAE,OAAO,IAAI,CAAC;CACb;;CClsBDA,IAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACnD;CACe,IAAM,MAAM,GAC1B,eAAW,CAAC,OAAY,EAAE;mCAAP,GAAG;AAAK;CAC5B,CAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;CACnC,CAAE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;CAC9E,CAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CACpB,CAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;CAC1B,CAAE,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;CACvC,EAAC;AACF;kBACC,gCAAU,MAAM,EAAE;CACnB,CAAE,IAAI,MAAM,YAAY,WAAW,EAAE;CACrC,EAAG,OAAO,IAAI,CAAC,SAAS,CAAC;CACzB,GAAI,OAAO,EAAE,MAAM;CACnB,GAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;CAC7B,GAAI,SAAS,EAAE,IAAI,CAAC,SAAS;CAC7B,GAAI,CAAC,CAAC;CACN,EAAG;AACH;CACA,CAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;CAC5C,EAAG,MAAM,IAAI,KAAK;CAClB,GAAI,sIAAsI;CAC1I,GAAI,CAAC;CACL,EAAG;AACH;CACA,CAAE,CAAC,UAAU,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,WAAE,MAAM,EAAK;CACzE,EAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAC;CACjF,EAAG,CAAC,CAAC;AACL;CACA,CAAE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;CACtC;CACA,EAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACrC,EAAG;AACH;CACA,CAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;CACvB,EAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;CAC5E,GAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;CAClF,GAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC7F,GAAI,MAAM;CACV,GAAIA,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;CAC/F,GAAI,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;CAC1D,IAAK,MAAM,IAAI,KAAK,uCAAmC,MAAM,CAAC,SAAQ,4BAAwB,CAAC;CAC/F,IAAK;CACL,GAAI;CACJ,EAAG;AACH;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC5B,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,0BAAO,GAAG,EAAE,OAAO,EAAE;CACtB,CAAE,IAAI,CAAC,SAAS,CAAC;CACjB,EAAG,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;CAChC,EAAG,SAAS,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;CAClD,EAAG,CAAC,CAAC;AACL;CACA,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,0BAAQ;CACT,CAAEA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;CAC5B,EAAG,KAAK,EAAE,IAAI,CAAC,KAAK;CACpB,EAAG,SAAS,EAAE,IAAI,CAAC,SAAS;CAC5B,EAAG,CAAC,CAAC;AACL;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;CACnC,EAAG,MAAM,CAAC,SAAS,CAAC;CACpB,GAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;CAC7B,GAAI,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;CACnC,GAAI,SAAS,EAAE,MAAM,CAAC,SAAS;CAC/B,GAAI,CAAC,CAAC;CACN,EAAG,CAAC,CAAC;AACL;CACA,CAAE,OAAO,MAAM,CAAC;CACf,EAAC;AACF;kBACC,kDAAmB,OAAY,EAAE;;oCAAP,GAAG;AAAK;CACnC,CAAEA,IAAM,KAAK,GAAG,EAAE,CAAC;CACnB,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;CACnC,EAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,WAAE,IAAI,EAAK;CAC7D,GAAI,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;CAChD,GAAI,CAAC,CAAC;CACN,EAAG,CAAC,CAAC;AACL;CACA,CAAEA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;CACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,EAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAChC,EAAG;AACH;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAK;CACtC,EAAG,IAAI,CAAC,GAAG,CAAC,EAAE;CACd,GAAI,QAAQ,CAAC,OAAO,CAACE,QAAI,CAAC,SAAS,CAAC,CAAC;CACrC,GAAI;AACJ;CACA,EAAGF,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAGE,QAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;CAChG,EAAGF,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;CACtC,EAAGA,IAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnD;CACA,EAAG,IAAI,WAAW,CAAC,KAAK,EAAE;CAC1B,GAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACxC,GAAI;AACJ;CACA,EAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,WAAE,KAAK,EAAK;CAC9C,GAAIA,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC;CACA,GAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;AAC1D;CACA,GAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;CACzB,IAAK,IAAI,KAAK,CAAC,MAAM,EAAE;CACvB,KAAM,QAAQ,CAAC,OAAO;CACtB,MAAO,WAAW;CAClB,MAAO,KAAK,CAAC,OAAO;CACpB,MAAO,GAAG;CACV,MAAO,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CAC3D,MAAO,CAAC;CACR,KAAM,MAAM;CACZ,KAAM,QAAQ,CAAC,gBAAgB;CAC/B,MAAO,WAAW;CAClB,MAAO,KAAK;CACZ,MAAO,WAAW,CAAC,QAAQ;CAC3B,MAAO,GAAG;CACV,MAAO,WAAW,CAAC,kBAAkB;CACrC,MAAO,CAAC;CACR,KAAM;CACN,IAAK,MAAM;CACX,IAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;CACrC,IAAK;AACL;CACA,GAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;CAC1D,GAAI,CAAC,CAAC;AACN;CACA,EAAG,IAAI,WAAW,CAAC,KAAK,EAAE;CAC1B,GAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACxC,GAAI;CACJ,EAAG,CAAC,CAAC;AACL;CACA,CAAE,OAAO;CACT,EAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;CAChE,EAAG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAE,MAAM,EAAK;CAC/C,GAAI,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;CAC3F,GAAI,CAAC;CACL,EAAG,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAE,MAAM,EAAK;CACtD,GAAI,OAAO,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CAC1D,GAAI,CAAC;CACL,SAAG,KAAK;CACR,EAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;CACzB,EAAG,CAAC;CACH,EAAC;AACF;kBACC,oCAAY,OAAO,EAAE;CACtB,CAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;CACxD,EAAC;AACF;kBACC,8CAAkB;CACnB,CAAEA,IAAM,kBAAkB,GAAG,EAAE,CAAC;AAChC;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAK;CACnC,EAAGA,IAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AAC9C;CACA,EAAG,IAAI,SAAS,KAAK,IAAI,IAAE,SAAO;AAClC;CACA,EAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAC;CACzE,EAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CACtC,EAAG,CAAC,CAAC;AACL;CACA,CAAE;CACF,EAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,CAAC,EAAK;CAClD,GAAI,OAAO,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;CACzD,GAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;CAChB,GAAI;CACH,EAAC;AACF;kBACC,0BAAO,SAAS,EAAE;;AAAC;CACpB,CAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;CACzB,EAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;CACtC,EAAG;AACH;CACA,CAAE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;AACpC;CACA,CAAEC,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrE;CACA,CAAE,IAAI,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAK;CACtC,EAAGD,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,QAAI,CAAC,SAAS,CAAC;CACxF,EAAGF,IAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E;CACA,EAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;CACpC,GAAI,OAAO,EAAE,MAAM,CAAC,qBAAqB;CACzC,gBAAI,WAAW;CACf,GAAI,CAAC,CAAC;AACN;CACA,EAAG,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;CACxD,EAAG,CAAC,CAAC;AACL;CACA,CAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,EAAG,IAAI,CAAC,KAAK;CACb,GAAI,SAAS;CACb,GAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,YAAG,KAAK,EAAE,KAAK,EAAK;CACrD,IAAK,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;CAClD,IAAK,CAAC,CAAC;CACP,EAAG;AACH;CACA,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,4BAAQ,GAAG,EAAE;CACd,CAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;CAChC,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,gCAAW;;AAAC;CACb,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO;CAC3B,GAAI,GAAG,WAAE,MAAM,EAAE,CAAC,EAAK;CACvB,GAAIA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,QAAI,CAAC,SAAS,CAAC;CACzF,GAAIF,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrE;CACA,GAAI,OAAO,GAAG,CAAC;CACf,GAAI,CAAC;CACL,GAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACb;CACA,CAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;CAC1B,EAAC;AACF;kBACC,8BAAU;CACX,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAE,OAAO,KAAK,GAAC;CAC3D,CAAE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,WAAE,MAAM,WAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,KAAE,CAAC,IAAE,OAAO,KAAK,GAAC;CAC7E,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,4BAAS;CACV,CAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;CAC5B,YAAI,MAAM,EAAE,MAAM,WAAK,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,KAAE;CACvD,EAAG,IAAI,CAAC,KAAK,CAAC,MAAM;CACpB,EAAG,CAAC;CACH,EAAC;AACF;kBACC,kCAAY;CACb,CAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC9B,EAAC;AACF;kBACC,sBAAK,QAAQ,EAAE;CAChB,CAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CACnD,EAAC;AACF;kBACC,gCAAU,QAAQ,EAAE;CACrB,CAAEA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;CACzD,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C;CACA,CAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACnB,EAAGC,IAAI,MAAM,CAAC;CACd,EAAGA,IAAI,CAAC,GAAG,CAAC,CAAC;AACb;CACA,EAAG,GAAG;CACN,GAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;CAC/B,GAAI,IAAI,CAAC,MAAM,EAAE;CACjB,IAAK,MAAM;CACX,IAAK;CACL,GAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;CACxD,EAAG;AACH;CACA,CAAE,OAAO,IAAI,CAAC;CACb,EAAC;AACF;kBACC,4BAAQ,QAAQ,EAAE;CACnB,CAAED,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;CACA,CAAEC,IAAI,MAAM,CAAC;CACb,CAAEA,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC;CACA,CAAE,GAAG;CACL,EAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;CAC9B,EAAG,IAAI,CAAC,MAAM,EAAE;CAChB,GAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC5C,GAAI,MAAM;CACV,GAAI;CACJ,EAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACrD;CACA,CAAE,OAAO,IAAI,CAAC;CACb;;CC1RD,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;CAC5B,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;CAClC,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC;;;;;;;;"}