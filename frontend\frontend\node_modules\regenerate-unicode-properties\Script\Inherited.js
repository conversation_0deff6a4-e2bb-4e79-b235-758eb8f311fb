const set = require('regenerate')(0x670, 0x1CED, 0x1CF4, 0x101FD, 0x102E0, 0x1133B);
set.addRange(0x300, 0x36F).addRange(0x485, 0x486).addRange(0x64B, 0x655).addRange(0x951, 0x954).addRange(0x1AB0, 0x1ACE).addRange(0x1CD0, 0x1CD2).addRange(0x1CD4, 0x1CE0).addRange(0x1CE2, 0x1CE8).addRange(0x1CF8, 0x1CF9).addRange(0x1DC0, 0x1DFF).addRange(0x200C, 0x200D).addRange(0x20D0, 0x20F0).addRange(0x302A, 0x302D).addRange(0x3099, 0x309A).addRange(0xFE00, 0xFE0F).addRange(0xFE20, 0xFE2D).addRange(0x1CF00, 0x1CF2D).addRange(0x1CF30, 0x1CF46).addRange(0x1D167, 0x1D169).addRange(0x1D17B, 0x1D182).addRange(0x1D185, 0x1D18B).addRange(0x1D1AA, 0x1D1AD).addRange(0xE0100, 0xE01EF);
exports.characters = set;
