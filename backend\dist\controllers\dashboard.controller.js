"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCollegeProfileSummary = exports.getEventPerformanceMetrics = exports.getCollegeDashboard = void 0;
const Event_1 = __importDefault(require("../models/Event"));
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const Notification_1 = __importDefault(require("../models/Notification"));
const Collaboration_1 = __importDefault(require("../models/Collaboration"));
const College_1 = __importDefault(require("../models/College"));
// Get comprehensive dashboard data for college
const getCollegeDashboard = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        // Event Statistics
        const eventStats = {
            total: yield Event_1.default.countDocuments({ organizer: collegeId }),
            published: yield Event_1.default.countDocuments({ organizer: collegeId, status: 'published' }),
            ongoing: yield Event_1.default.countDocuments({ organizer: collegeId, status: 'ongoing' }),
            completed: yield Event_1.default.countDocuments({ organizer: collegeId, status: 'completed' }),
            draft: yield Event_1.default.countDocuments({ organizer: collegeId, status: 'draft' })
        };
        // Registration Statistics
        const registrationStats = yield EventRegistration_1.default.aggregate([
            {
                $lookup: {
                    from: 'events',
                    localField: 'event',
                    foreignField: '_id',
                    as: 'eventData'
                }
            },
            {
                $match: {
                    'eventData.organizer': collegeId
                }
            },
            {
                $group: {
                    _id: null,
                    totalRegistrations: { $sum: 1 },
                    attendedCount: {
                        $sum: { $cond: [{ $eq: ['$status', 'attended'] }, 1, 0] }
                    },
                    cancelledCount: {
                        $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
                    }
                }
            }
        ]);
        const regStats = registrationStats[0] || {
            totalRegistrations: 0,
            attendedCount: 0,
            cancelledCount: 0
        };
        // Notification Statistics
        const notificationStats = {
            total: yield Notification_1.default.countDocuments({ sender: collegeId }),
            sent: yield Notification_1.default.countDocuments({ sender: collegeId, status: 'sent' }),
            scheduled: yield Notification_1.default.countDocuments({ sender: collegeId, status: 'scheduled' })
        };
        // Collaboration Statistics
        const collaborationStats = {
            sent: yield Collaboration_1.default.countDocuments({ requester: collegeId }),
            received: yield Collaboration_1.default.countDocuments({ requestee: collegeId }),
            accepted: yield Collaboration_1.default.countDocuments({
                $or: [{ requester: collegeId }, { requestee: collegeId }],
                status: 'accepted'
            }),
            pending: yield Collaboration_1.default.countDocuments({
                requestee: collegeId,
                status: 'pending'
            })
        };
        // Recent Events
        const recentEvents = yield Event_1.default.find({ organizer: collegeId })
            .sort({ createdAt: -1 })
            .limit(5)
            .select('title startDate status currentParticipants maxParticipants');
        // Upcoming Events
        const upcomingEvents = yield Event_1.default.find({
            organizer: collegeId,
            startDate: { $gte: new Date() },
            status: 'published'
        })
            .sort({ startDate: 1 })
            .limit(5)
            .select('title startDate location.city currentParticipants maxParticipants');
        // Recent Notifications
        const recentNotifications = yield Notification_1.default.find({ sender: collegeId })
            .sort({ createdAt: -1 })
            .limit(5)
            .select('title type status sentAt clickCount');
        // Pending Collaboration Requests
        const pendingCollaborations = yield Collaboration_1.default.find({
            requestee: collegeId,
            status: 'pending'
        })
            .populate('requester', 'name email')
            .sort({ createdAt: -1 })
            .limit(5);
        // Monthly Event Analytics
        const monthlyAnalytics = yield Event_1.default.aggregate([
            {
                $match: {
                    organizer: collegeId,
                    createdAt: {
                        $gte: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1)
                    }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' }
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 }
            }
        ]);
        // Top Performing Events
        const topEvents = yield Event_1.default.find({ organizer: collegeId })
            .sort({ 'analytics.registrations': -1 })
            .limit(5)
            .select('title analytics.registrations analytics.views feedback.averageRating');
        res.json({
            success: true,
            dashboard: {
                eventStats,
                registrationStats: regStats,
                notificationStats,
                collaborationStats,
                recentEvents,
                upcomingEvents,
                recentNotifications,
                pendingCollaborations,
                monthlyAnalytics,
                topEvents
            }
        });
    }
    catch (error) {
        console.error('Get dashboard error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollegeDashboard = getCollegeDashboard;
// Get event performance metrics
const getEventPerformanceMetrics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const { eventId } = req.params;
        // Verify event ownership
        const event = yield Event_1.default.findOne({ _id: eventId, organizer: collegeId });
        if (!event) {
            return res.status(404).json({ message: 'Event not found or not authorized' });
        }
        // Registration metrics
        const registrationMetrics = yield EventRegistration_1.default.aggregate([
            { $match: { event: eventId } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Daily registration trend
        const registrationTrend = yield EventRegistration_1.default.aggregate([
            { $match: { event: eventId } },
            {
                $group: {
                    _id: {
                        $dateToString: { format: '%Y-%m-%d', date: '$registrationDate' }
                    },
                    count: { $sum: 1 }
                }
            },
            { $sort: { '_id': 1 } }
        ]);
        // Feedback summary
        const feedbackSummary = yield EventRegistration_1.default.aggregate([
            { $match: { event: eventId, 'feedback.rating': { $exists: true } } },
            {
                $group: {
                    _id: null,
                    averageRating: { $avg: '$feedback.rating' },
                    totalFeedbacks: { $sum: 1 },
                    ratingDistribution: {
                        $push: '$feedback.rating'
                    }
                }
            }
        ]);
        res.json({
            success: true,
            metrics: {
                registrationMetrics,
                registrationTrend,
                feedbackSummary: feedbackSummary[0] || null,
                eventAnalytics: event.analytics
            }
        });
    }
    catch (error) {
        console.error('Get event performance metrics error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getEventPerformanceMetrics = getEventPerformanceMetrics;
// Get college profile summary
const getCollegeProfileSummary = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const college = yield College_1.default.findById(collegeId).select('-password');
        if (!college) {
            return res.status(404).json({ message: 'College not found' });
        }
        // Calculate profile completion percentage
        const requiredFields = ['name', 'email', 'phone', 'address', 'description', 'website'];
        const completedFields = requiredFields.filter(field => college[field]);
        const profileCompletion = (completedFields.length / requiredFields.length) * 100;
        res.json({
            success: true,
            profile: Object.assign(Object.assign({}, college.toObject()), { profileCompletion: Math.round(profileCompletion) })
        });
    }
    catch (error) {
        console.error('Get college profile summary error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollegeProfileSummary = getCollegeProfileSummary;
