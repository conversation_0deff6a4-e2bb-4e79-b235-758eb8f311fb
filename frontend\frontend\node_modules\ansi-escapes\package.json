{"name": "ansi-escapes", "version": "4.3.2", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": "sindresorhus/ansi-escapes", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2"], "dependencies": {"type-fest": "^0.21.3"}, "devDependencies": {"@types/node": "^13.7.7", "ava": "^2.1.0", "tsd": "^0.14.0", "xo": "^0.25.3"}}