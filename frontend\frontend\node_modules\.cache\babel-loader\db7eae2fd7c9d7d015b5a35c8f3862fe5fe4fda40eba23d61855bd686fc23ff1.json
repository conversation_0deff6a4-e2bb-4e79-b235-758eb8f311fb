{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"home-container\",children:[/*#__PURE__*/_jsx(\"nav\",{className:\"floating-nav\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"nav-content\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"nav-logo\",children:\"College Events\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"nav-links\",children:[/*#__PURE__*/_jsx(Link,{to:\"/events\",children:\"Events\"}),/*#__PURE__*/_jsx(<PERSON>,{to:\"/login\",children:\"Login\"}),/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"nav-cta\",children:\"Register\"})]})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"hero-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"hero-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hero-content\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"hero-title\",children:[\"Discover Amazing\",/*#__PURE__*/_jsx(\"span\",{className:\"highlight\",children:\" College Events\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"hero-subtitle\",children:\"Connect with events happening at colleges near you. Join communities, learn new skills, and make lasting memories.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"hero-buttons\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/events\",className:\"btn-hero primary\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFAF\"}),\"Browse Events\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/register\",className:\"btn-hero secondary\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDE80\"}),\"Get Started\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hero-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"500+\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Active Events\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"50+\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Partner Colleges\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"10K+\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Students Connected\"})]})]})]})})}),/*#__PURE__*/_jsx(\"section\",{className:\"features-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Why Choose Our Platform?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Everything you need to discover and participate in college events\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"features-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83C\\uDFAF\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Discover Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Find events based on your interests, location, and academic field. Never miss out on opportunities that matter to you.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"feature-link\",children:/*#__PURE__*/_jsx(Link,{to:\"/events\",children:\"Explore Events \\u2192\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83E\\uDD1D\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Connect with Peers\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Meet like-minded students, build your network, and create meaningful connections that last beyond college.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"feature-link\",children:/*#__PURE__*/_jsx(Link,{to:\"/register\",children:\"Join Community \\u2192\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:\"\\uD83D\\uDCC5\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Manage Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create, organize, and promote your own events. Reach thousands of students across multiple colleges.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"feature-link\",children:/*#__PURE__*/_jsx(Link,{to:\"/register/college\",children:\"Start Organizing \\u2192\"})})]})]})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"cta-section\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"cta-content\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Ready to Get Started?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Join thousands of students already discovering amazing events\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"cta-buttons\",children:[/*#__PURE__*/_jsx(Link,{to:\"/register/student\",className:\"btn-cta primary\",children:\"Register as Student\"}),/*#__PURE__*/_jsx(Link,{to:\"/register/college\",className:\"btn-cta secondary\",children:\"Register as College\"})]})]})})})]});};export default Home;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "className", "children", "to"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home: React.FC = () => {\n  return (\n    <div className=\"home-container\">\n      {/* Floating Navigation */}\n      <nav className=\"floating-nav\">\n        <div className=\"nav-content\">\n          <Link to=\"/\" className=\"nav-logo\">\n            College Events\n          </Link>\n          <div className=\"nav-links\">\n            <Link to=\"/events\">Events</Link>\n            <Link to=\"/login\">Login</Link>\n            <Link to=\"/register\" className=\"nav-cta\">Register</Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-overlay\">\n          <div className=\"hero-content\">\n            <h1 className=\"hero-title\">\n              Discover Amazing\n              <span className=\"highlight\"> College Events</span>\n            </h1>\n            <p className=\"hero-subtitle\">\n              Connect with events happening at colleges near you. Join communities,\n              learn new skills, and make lasting memories.\n            </p>\n            <div className=\"hero-buttons\">\n              <Link to=\"/events\" className=\"btn-hero primary\">\n                <span>🎯</span>\n                Browse Events\n              </Link>\n              <Link to=\"/register\" className=\"btn-hero secondary\">\n                <span>🚀</span>\n                Get Started\n              </Link>\n            </div>\n            <div className=\"hero-stats\">\n              <div className=\"stat\">\n                <h3>500+</h3>\n                <p>Active Events</p>\n              </div>\n              <div className=\"stat\">\n                <h3>50+</h3>\n                <p>Partner Colleges</p>\n              </div>\n              <div className=\"stat\">\n                <h3>10K+</h3>\n                <p>Students Connected</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"features-section\">\n        <div className=\"container\">\n          <div className=\"section-header\">\n            <h2>Why Choose Our Platform?</h2>\n            <p>Everything you need to discover and participate in college events</p>\n          </div>\n\n          <div className=\"features-grid\">\n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🎯</div>\n              <h3>Discover Events</h3>\n              <p>Find events based on your interests, location, and academic field. Never miss out on opportunities that matter to you.</p>\n              <div className=\"feature-link\">\n                <Link to=\"/events\">Explore Events →</Link>\n              </div>\n            </div>\n\n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">🤝</div>\n              <h3>Connect with Peers</h3>\n              <p>Meet like-minded students, build your network, and create meaningful connections that last beyond college.</p>\n              <div className=\"feature-link\">\n                <Link to=\"/register\">Join Community →</Link>\n              </div>\n            </div>\n\n            <div className=\"feature-card\">\n              <div className=\"feature-icon\">📅</div>\n              <h3>Manage Events</h3>\n              <p>Create, organize, and promote your own events. Reach thousands of students across multiple colleges.</p>\n              <div className=\"feature-link\">\n                <Link to=\"/register/college\">Start Organizing →</Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"cta-section\">\n        <div className=\"container\">\n          <div className=\"cta-content\">\n            <h2>Ready to Get Started?</h2>\n            <p>Join thousands of students already discovering amazing events</p>\n            <div className=\"cta-buttons\">\n              <Link to=\"/register/student\" className=\"btn-cta primary\">\n                Register as Student\n              </Link>\n              <Link to=\"/register/college\" className=\"btn-cta secondary\">\n                Register as College\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,mBACED,KAAA,QAAKE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7BL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,gBAElC,CAAM,CAAC,cACPH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,SAAS,CAAAD,QAAA,CAAC,QAAM,CAAM,CAAC,cAChCL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,QAAQ,CAAAD,QAAA,CAAC,OAAK,CAAM,CAAC,cAC9BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACrD,CAAC,EACH,CAAC,CACH,CAAC,cAGNL,IAAA,YAASI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC/BL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BH,KAAA,OAAIE,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,kBAEzB,cAAAL,IAAA,SAAMI,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,EAChD,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oHAG7B,CAAG,CAAC,cACJH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,SAAS,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7CL,IAAA,SAAAK,QAAA,CAAM,cAAE,CAAM,CAAC,gBAEjB,EAAM,CAAC,cACPH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjDL,IAAA,SAAAK,QAAA,CAAM,cAAE,CAAM,CAAC,cAEjB,EAAM,CAAC,EACJ,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,MAAI,CAAI,CAAC,cACbL,IAAA,MAAAK,QAAA,CAAG,eAAa,CAAG,CAAC,EACjB,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,KAAG,CAAI,CAAC,cACZL,IAAA,MAAAK,QAAA,CAAG,kBAAgB,CAAG,CAAC,EACpB,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,MAAI,CAAI,CAAC,cACbL,IAAA,MAAAK,QAAA,CAAG,oBAAkB,CAAG,CAAC,EACtB,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACC,CAAC,cAGVL,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCH,KAAA,QAAKE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBH,KAAA,QAAKE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BL,IAAA,OAAAK,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCL,IAAA,MAAAK,QAAA,CAAG,mEAAiE,CAAG,CAAC,EACrE,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCL,IAAA,OAAAK,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBL,IAAA,MAAAK,QAAA,CAAG,wHAAsH,CAAG,CAAC,cAC7HL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,SAAS,CAAAD,QAAA,CAAC,uBAAgB,CAAM,CAAC,CACvC,CAAC,EACH,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCL,IAAA,OAAAK,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BL,IAAA,MAAAK,QAAA,CAAG,4GAA0G,CAAG,CAAC,cACjHL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,WAAW,CAAAD,QAAA,CAAC,uBAAgB,CAAM,CAAC,CACzC,CAAC,EACH,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtCL,IAAA,OAAAK,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBL,IAAA,MAAAK,QAAA,CAAG,sGAAoG,CAAG,CAAC,cAC3GL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,mBAAmB,CAAAD,QAAA,CAAC,yBAAkB,CAAM,CAAC,CACnD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGVL,IAAA,YAASI,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC9BL,IAAA,QAAKI,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,OAAAK,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BL,IAAA,MAAAK,QAAA,CAAG,+DAA6D,CAAG,CAAC,cACpEH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,mBAAmB,CAACF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,qBAEzD,CAAM,CAAC,cACPL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,mBAAmB,CAACF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,qBAE3D,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}