{"ast": null, "code": "/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config) \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;", "map": {"version": 3, "names": ["REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "ref", "$$typeof", "props", "exports", "Fragment", "jsx", "jsxs"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/node_modules/react/cjs/react-jsx-runtime.production.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,IAAIA,kBAAkB,GAAGC,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;EAC/DC,mBAAmB,GAAGF,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;AACpD,SAASE,OAAOA,CAACC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACvC,IAAIC,GAAG,GAAG,IAAI;EACd,KAAK,CAAC,KAAKD,QAAQ,KAAKC,GAAG,GAAG,EAAE,GAAGD,QAAQ,CAAC;EAC5C,KAAK,CAAC,KAAKD,MAAM,CAACE,GAAG,KAAKA,GAAG,GAAG,EAAE,GAAGF,MAAM,CAACE,GAAG,CAAC;EAChD,IAAI,KAAK,IAAIF,MAAM,EAAE;IACnBC,QAAQ,GAAG,CAAC,CAAC;IACb,KAAK,IAAIE,QAAQ,IAAIH,MAAM,EACzB,KAAK,KAAKG,QAAQ,KAAKF,QAAQ,CAACE,QAAQ,CAAC,GAAGH,MAAM,CAACG,QAAQ,CAAC,CAAC;EACjE,CAAC,MAAMF,QAAQ,GAAGD,MAAM;EACxBA,MAAM,GAAGC,QAAQ,CAACG,GAAG;EACrB,OAAO;IACLC,QAAQ,EAAEX,kBAAkB;IAC5BK,IAAI,EAAEA,IAAI;IACVG,GAAG,EAAEA,GAAG;IACRE,GAAG,EAAE,KAAK,CAAC,KAAKJ,MAAM,GAAGA,MAAM,GAAG,IAAI;IACtCM,KAAK,EAAEL;EACT,CAAC;AACH;AACAM,OAAO,CAACC,QAAQ,GAAGX,mBAAmB;AACtCU,OAAO,CAACE,GAAG,GAAGX,OAAO;AACrBS,OAAO,CAACG,IAAI,GAAGZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}