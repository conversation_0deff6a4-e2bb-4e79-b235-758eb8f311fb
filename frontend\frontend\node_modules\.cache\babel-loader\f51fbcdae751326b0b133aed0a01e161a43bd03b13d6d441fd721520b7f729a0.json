{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/workuuu/frontend/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{registerCollege}from'../../services/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CollegeSignup=()=>{const navigate=useNavigate();const[formData,setFormData]=useState({name:'',email:'',password:'',confirmPassword:'',address:'',documents:[]});const[error,setError]=useState('');const[loading,setLoading]=useState(false);const handleChange=e=>{const{name,value}=e.target;setFormData(_objectSpread(_objectSpread({},formData),{},{[name]:value}));};const handleFileChange=e=>{if(e.target.files){setFormData(_objectSpread(_objectSpread({},formData),{},{documents:Array.from(e.target.files)}));}};const handleSubmit=async e=>{e.preventDefault();if(formData.password!==formData.confirmPassword){setError('Passwords do not match');return;}try{setLoading(true);setError('');console.log('Submitting college registration form with data:',{name:formData.name,email:formData.email,address:formData.address,documentsCount:formData.documents.length});const response=await registerCollege({name:formData.name,email:formData.email,password:formData.password,address:formData.address,documents:formData.documents});console.log('Registration successful:',response);navigate('/verification-pending');}catch(err){var _err$response,_err$response$data;console.error('Registration error:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Registration failed. Please try again.');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"auth-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"College Registration\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Register your institution\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,encType:\"multipart/form-data\",className:\"auth-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"College Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",value:formData.name,onChange:handleChange,placeholder:\"Enter college name\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleChange,placeholder:\"Enter official email\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:formData.password,onChange:handleChange,placeholder:\"Create a password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleChange,placeholder:\"Confirm your password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Address\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"address\",value:formData.address,onChange:handleChange,placeholder:\"Enter complete college address\",rows:3,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Verification Documents\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",name:\"documents\",multiple:true,onChange:handleFileChange,accept:\".pdf,.jpg,.jpeg,.png,.doc,.docx\",required:true}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.85rem',color:'#7f8c8d',marginTop:'0.5rem'},children:\"Please upload documents that verify your college's authenticity (PDF, Images, or Documents)\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,className:\"auth-btn\",children:loading?'Registering...':'Register College'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"auth-footer\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(\"a\",{href:\"/login\",className:\"auth-link\",children:\"Login here\"})]})]})});};export default CollegeSignup;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "registerCollege", "jsx", "_jsx", "jsxs", "_jsxs", "CollegeSignup", "navigate", "formData", "setFormData", "name", "email", "password", "confirmPassword", "address", "documents", "error", "setError", "loading", "setLoading", "handleChange", "e", "value", "target", "_objectSpread", "handleFileChange", "files", "Array", "from", "handleSubmit", "preventDefault", "console", "log", "documentsCount", "length", "response", "err", "_err$response", "_err$response$data", "data", "message", "className", "children", "onSubmit", "encType", "type", "onChange", "placeholder", "required", "rows", "multiple", "accept", "style", "fontSize", "color", "marginTop", "disabled", "href"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/components/auth/CollegeSignup.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { registerCollege } from '../../services/auth';\n\nconst CollegeSignup: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    address: '',\n    documents: [] as File[]\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData({ ...formData, [name]: value });\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      setFormData({ ...formData, documents: Array.from(e.target.files) });\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError('');\n      \n      console.log('Submitting college registration form with data:', {\n        name: formData.name,\n        email: formData.email,\n        address: formData.address,\n        documentsCount: formData.documents.length\n      });\n      \n      const response = await registerCollege({\n        name: formData.name,\n        email: formData.email,\n        password: formData.password,\n        address: formData.address,\n        documents: formData.documents\n      });\n      \n      console.log('Registration successful:', response);\n      navigate('/verification-pending');\n    } catch (err: any) {\n      console.error('Registration error:', err);\n      setError(err.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-box\">\n        <div className=\"auth-header\">\n          <h2>College Registration</h2>\n          <p>Register your institution</p>\n        </div>\n\n        {error && <div className=\"error-message\">{error}</div>}\n\n        <form onSubmit={handleSubmit} encType=\"multipart/form-data\" className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>College Name</label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              placeholder=\"Enter college name\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email Address</label>\n            <input\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              placeholder=\"Enter official email\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password</label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              placeholder=\"Create a password\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Confirm Password</label>\n            <input\n              type=\"password\"\n              name=\"confirmPassword\"\n              value={formData.confirmPassword}\n              onChange={handleChange}\n              placeholder=\"Confirm your password\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Address</label>\n            <textarea\n              name=\"address\"\n              value={formData.address}\n              onChange={handleChange}\n              placeholder=\"Enter complete college address\"\n              rows={3}\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Verification Documents</label>\n            <input\n              type=\"file\"\n              name=\"documents\"\n              multiple\n              onChange={handleFileChange}\n              accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\"\n              required\n            />\n            <p style={{ fontSize: '0.85rem', color: '#7f8c8d', marginTop: '0.5rem' }}>\n              Please upload documents that verify your college's authenticity (PDF, Images, or Documents)\n            </p>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"auth-btn\"\n          >\n            {loading ? 'Registering...' : 'Register College'}\n          </button>\n        </form>\n\n        <div className=\"auth-footer\">\n          Already have an account? <a href=\"/login\" className=\"auth-link\">Login here</a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CollegeSignup;"], "mappings": "6HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,eAAe,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CACvCW,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EACb,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAqB,YAAY,CAAIC,CAA4D,EAAK,CACrF,KAAM,CAAEX,IAAI,CAAEY,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCd,WAAW,CAAAe,aAAA,CAAAA,aAAA,IAAMhB,QAAQ,MAAE,CAACE,IAAI,EAAGY,KAAK,EAAE,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAIJ,CAAsC,EAAK,CACnE,GAAIA,CAAC,CAACE,MAAM,CAACG,KAAK,CAAE,CAClBjB,WAAW,CAAAe,aAAA,CAAAA,aAAA,IAAMhB,QAAQ,MAAEO,SAAS,CAAEY,KAAK,CAACC,IAAI,CAACP,CAAC,CAACE,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAR,CAAkB,EAAK,CACjDA,CAAC,CAACS,cAAc,CAAC,CAAC,CAElB,GAAItB,QAAQ,CAACI,QAAQ,GAAKJ,QAAQ,CAACK,eAAe,CAAE,CAClDI,QAAQ,CAAC,wBAAwB,CAAC,CAClC,OACF,CAEA,GAAI,CACFE,UAAU,CAAC,IAAI,CAAC,CAChBF,QAAQ,CAAC,EAAE,CAAC,CAEZc,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAE,CAC7DtB,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBG,OAAO,CAAEN,QAAQ,CAACM,OAAO,CACzBmB,cAAc,CAAEzB,QAAQ,CAACO,SAAS,CAACmB,MACrC,CAAC,CAAC,CAEF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlC,eAAe,CAAC,CACrCS,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BE,OAAO,CAAEN,QAAQ,CAACM,OAAO,CACzBC,SAAS,CAAEP,QAAQ,CAACO,SACtB,CAAC,CAAC,CAEFgB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEG,QAAQ,CAAC,CACjD5B,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAE,MAAO6B,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBP,OAAO,CAACf,KAAK,CAAC,qBAAqB,CAAEoB,GAAG,CAAC,CACzCnB,QAAQ,CAAC,EAAAoB,aAAA,CAAAD,GAAG,CAACD,QAAQ,UAAAE,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcE,IAAI,UAAAD,kBAAA,iBAAlBA,kBAAA,CAAoBE,OAAO,GAAI,wCAAwC,CAAC,CACnF,CAAC,OAAS,CACRrB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEhB,IAAA,QAAKsC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BrC,KAAA,QAAKoC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrC,KAAA,QAAKoC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvC,IAAA,OAAAuC,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BvC,IAAA,MAAAuC,QAAA,CAAG,2BAAyB,CAAG,CAAC,EAC7B,CAAC,CAEL1B,KAAK,eAAIb,IAAA,QAAKsC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE1B,KAAK,CAAM,CAAC,cAEtDX,KAAA,SAAMsC,QAAQ,CAAEd,YAAa,CAACe,OAAO,CAAC,qBAAqB,CAACH,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC/ErC,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,cAAY,CAAO,CAAC,cAC3BvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXnC,IAAI,CAAC,MAAM,CACXY,KAAK,CAAEd,QAAQ,CAACE,IAAK,CACrBoC,QAAQ,CAAE1B,YAAa,CACvB2B,WAAW,CAAC,oBAAoB,CAChCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,eAAa,CAAO,CAAC,cAC5BvC,IAAA,UACE0C,IAAI,CAAC,OAAO,CACZnC,IAAI,CAAC,OAAO,CACZY,KAAK,CAAEd,QAAQ,CAACG,KAAM,CACtBmC,QAAQ,CAAE1B,YAAa,CACvB2B,WAAW,CAAC,sBAAsB,CAClCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,UAAQ,CAAO,CAAC,cACvBvC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfnC,IAAI,CAAC,UAAU,CACfY,KAAK,CAAEd,QAAQ,CAACI,QAAS,CACzBkC,QAAQ,CAAE1B,YAAa,CACvB2B,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,kBAAgB,CAAO,CAAC,cAC/BvC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfnC,IAAI,CAAC,iBAAiB,CACtBY,KAAK,CAAEd,QAAQ,CAACK,eAAgB,CAChCiC,QAAQ,CAAE1B,YAAa,CACvB2B,WAAW,CAAC,uBAAuB,CACnCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,SAAO,CAAO,CAAC,cACtBvC,IAAA,aACEO,IAAI,CAAC,SAAS,CACdY,KAAK,CAAEd,QAAQ,CAACM,OAAQ,CACxBgC,QAAQ,CAAE1B,YAAa,CACvB2B,WAAW,CAAC,gCAAgC,CAC5CE,IAAI,CAAE,CAAE,CACRD,QAAQ,MACT,CAAC,EACC,CAAC,cAEN3C,KAAA,QAAKoC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvC,IAAA,UAAAuC,QAAA,CAAO,wBAAsB,CAAO,CAAC,cACrCvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXnC,IAAI,CAAC,WAAW,CAChBwC,QAAQ,MACRJ,QAAQ,CAAErB,gBAAiB,CAC3B0B,MAAM,CAAC,iCAAiC,CACxCH,QAAQ,MACT,CAAC,cACF7C,IAAA,MAAGiD,KAAK,CAAE,CAAEC,QAAQ,CAAE,SAAS,CAAEC,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAb,QAAA,CAAC,6FAE1E,CAAG,CAAC,EACD,CAAC,cAENvC,IAAA,WACE0C,IAAI,CAAC,QAAQ,CACbW,QAAQ,CAAEtC,OAAQ,CAClBuB,SAAS,CAAC,UAAU,CAAAC,QAAA,CAEnBxB,OAAO,CAAG,gBAAgB,CAAG,kBAAkB,CAC1C,CAAC,EACL,CAAC,cAEPb,KAAA,QAAKoC,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,2BACF,cAAAvC,IAAA,MAAGsD,IAAI,CAAC,QAAQ,CAAChB,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,EAC3E,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}