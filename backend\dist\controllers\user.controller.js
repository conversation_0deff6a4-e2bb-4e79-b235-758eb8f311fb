"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCollegeProfile = exports.getCollegeProfile = void 0;
const College_1 = __importDefault(require("../models/College"));
// Get college profile
const getCollegeProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore - We'll add this property in the auth middleware
        const college = yield College_1.default.findById(req.college.id).select('-password');
        if (!college) {
            return res.status(404).json({ message: 'College not found' });
        }
        res.json({
            success: true,
            college
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollegeProfile = getCollegeProfile;
// Update college profile
const updateCollegeProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, address } = req.body;
        // @ts-ignore - We'll add this property in the auth middleware
        const college = yield College_1.default.findById(req.college.id);
        if (!college) {
            return res.status(404).json({ message: 'College not found' });
        }
        college.name = name || college.name;
        college.address = address || college.address;
        yield college.save();
        res.json({
            success: true,
            college: {
                id: college._id,
                name: college.name,
                email: college.email,
                address: college.address,
                isVerified: college.isVerified
            }
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.updateCollegeProfile = updateCollegeProfile;
