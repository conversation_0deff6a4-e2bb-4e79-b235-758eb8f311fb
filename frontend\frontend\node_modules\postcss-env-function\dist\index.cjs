"use strict";var e=require("postcss-value-parser"),t=require("fs"),n=require("path");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var s=a(e),i=a(t),o=a(n),c=(e,t)=>{const n=s.default(e);return n.walk((e=>{if((e=>e&&"function"===e.type&&"env"===e.value)(e)){const[n]=e.nodes;"word"===n.type&&void 0!==t[n.value]&&(e.nodes=[],e.type="word",e.value=t[n.value])}})),n.toString()};function u(e){return Object.assign({},Object(e).environmentVariables||Object(e)["environment-variables"])}function l(e){return e.map((e=>{if(e instanceof Promise)return e;if(e instanceof Function)return e();const t=e===Object(e)?e:{from:String(e)};if(t.environmentVariables||t["environment-variables"])return t;const n=String(t.from||"");return{type:(t.type||o.default.extname(n).slice(1)).toLowerCase(),from:n}})).reduce((async(e,t)=>{const{type:n,from:a}=await t;return"js"===n||"cjs"===n?Object.assign(e,await async function(e){var t;return u(await(t=o.default.resolve(e),Promise.resolve().then((function(){return r(require(t))}))))}(a)):"json"===n?Object.assign(e,await async function(e){return u(await f(o.default.resolve(e)))}(a)):Object.assign(e,u(await t))}),{})}const f=async e=>JSON.parse(await(e=>new Promise(((t,n)=>{i.default.readFile(e,"utf8",((e,a)=>{e?n(e):t(a)}))})))(e));function d(e){const t=l([].concat(Object(e).importFrom||[])),n="disableDeprecationNotice"in Object(e)&&Boolean(e.disableDeprecationNotice);let a=!1;return{postcssPlugin:"postcss-env-fn",async AtRule(e,{result:r}){let s;try{s=c(e.params,await t)}catch(t){e.warn(r,`Failed to parse params '${e.params}' as an environment value. Leaving the original value intact.`)}void 0!==s&&s!==e.params&&(e.params=s,n||a||(a=!0,e.warn(r,"postcss-env-function is deprecated and will be removed.\nCheck the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192")))},async Declaration(e,{result:r}){let s;try{s=c(e.value,await t)}catch(t){e.warn(r,`Failed to parse value '${e.value}' as an environment value. Leaving the original value intact.`)}void 0!==s&&s!==e.value&&(e.value=s,n||a||(a=!0,e.warn(r,"postcss-env-function is deprecated and will be removed.\nWe are looking for insights and anecdotes on how these features are used so that we can design the best alternative.\nPlease let us know if our proposal will work for you.\nVisit the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192")))}}}d.postcss=!0,module.exports=d;
