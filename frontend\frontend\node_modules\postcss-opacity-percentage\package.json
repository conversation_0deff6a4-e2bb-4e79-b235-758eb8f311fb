{"name": "postcss-opacity-percentage", "version": "1.1.3", "description": "PostCSS plugin to transform percentage-based opacity values to more compatible floating-point values.", "keywords": ["postcss", "css", "postcss-plugin", "opacity"], "license": "MIT", "repository": "github:mrcgrtz/postcss-opacity-percentage", "funding": [{"type": "kofi", "url": "https://ko-fi.com/mrcgrtz"}, {"type": "liberapay", "url": "https://liberapay.com/mrcgrtz"}], "author": {"name": "<PERSON>", "email": "*****************", "url": "https://marcgoertz.de/"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "index.js", "files": ["index.js"], "scripts": {"format": "xo --fix", "test": "xo && c8 ava", "coverage": "c8 report --reporter=lcov", "publish": "clean-publish"}, "peerDependencies": {"postcss": "^8.2"}, "devDependencies": {"ava": "^4.0.1", "c8": "^7.10.0", "clean-publish": "^4.0.0", "husky": "^8.0.0", "lint-staged": "^13.0.0", "postcss": "^8.4.5", "xo": "^0.52.2"}, "lint-staged": {"*.js": "xo --fix"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}}