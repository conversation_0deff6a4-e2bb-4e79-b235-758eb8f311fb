/**
 * Determines the type of the given collection, or returns false.
 *
 * @param {unknown} value The potential collection
 * @returns {TypedArrayName | false | null} 'Int8Array' | 'Uint8Array' | 'Uint8ClampedArray' | 'Int16Array' | 'Uint16Array' | 'Int32Array' | 'Uint32Array' | 'Float32Array' | 'Float64Array' | 'BigInt64Array' | 'BigUint64Array' | false | null
 */
declare function whichTypedArray(value: Int8Array): 'Int8Array';
declare function whichTypedArray(value: Uint8Array): 'Uint8Array';
declare function whichTypedArray(value: Uint8ClampedArray): 'Uint8ClampedArray';
declare function whichTypedArray(value: Int16Array): 'Int16Array';
declare function whichTypedArray(value: Uint16Array): 'Uint16Array';
declare function whichTypedArray(value: Int32Array): 'Int32Array';
declare function whichTypedArray(value: Uint32Array): 'Uint32Array';
declare function whichTypedArray(value: Float32Array): 'Float32Array';
declare function whichTypedArray(value: Float64Array): 'Float64Array';
declare function whichTypedArray(value: BigInt64Array): 'BigInt64Array';
declare function whichTypedArray(value: BigUint64Array): 'BigUint64Array';
declare function whichTypedArray(value: whichTypedArray.TypedArray): whichTypedArray.TypedArrayName;
declare function whichTypedArray(value: unknown): false | null;

declare namespace whichTypedArray {
  export type TypedArrayName =
    | 'Int8Array'
    | 'Uint8Array'
    | 'Uint8ClampedArray'
    | 'Int16Array'
    | 'Uint16Array'
    | 'Int32Array'
    | 'Uint32Array'
    | 'Float32Array'
    | 'Float64Array'
    | 'BigInt64Array'
    | 'BigUint64Array';

  export type TypedArray =
  	| Int8Array
	| Uint8Array
	| Uint8ClampedArray
	| Int16Array
	| Uint16Array
	| Int32Array
	| Uint32Array
	| Float32Array
	| Float64Array
	| BigInt64Array
	| BigUint64Array;

  export type TypedArrayConstructor =
    | Int8ArrayConstructor
    | Uint8ArrayConstructor
    | Uint8ClampedArrayConstructor
    | Int16ArrayConstructor
    | Uint16ArrayConstructor
    | Int32ArrayConstructor
    | Uint32ArrayConstructor
    | Float32ArrayConstructor
    | Float64ArrayConstructor
    | BigInt64ArrayConstructor
    | BigUint64ArrayConstructor;
}

export = whichTypedArray;
