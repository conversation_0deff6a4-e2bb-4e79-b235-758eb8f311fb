import { Request, Response } from 'express';
import College from '../models/College';

// Get college profile
export const getCollegeProfile = async (req: Request, res: Response) => {
  try {
    // @ts-ignore - We'll add this property in the auth middleware
    const college = await College.findById(req.college.id).select('-password');
    
    if (!college) {
      return res.status(404).json({ message: 'College not found' });
    }

    res.json({
      success: true,
      college
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update college profile
export const updateCollegeProfile = async (req: Request, res: Response) => {
  try {
    const { name, address } = req.body;
    
    // @ts-ignore - We'll add this property in the auth middleware
    const college = await College.findById(req.college.id);
    
    if (!college) {
      return res.status(404).json({ message: 'College not found' });
    }

    college.name = name || college.name;
    college.address = address || college.address;

    await college.save();

    res.json({
      success: true,
      college: {
        id: college._id,
        name: college.name,
        email: college.email,
        address: college.address,
        isVerified: college.isVerified
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};