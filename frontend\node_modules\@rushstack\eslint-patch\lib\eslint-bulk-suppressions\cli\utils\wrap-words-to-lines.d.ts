/**
 * Applies word wrapping and returns an array of lines.
 *
 * @param text - The text to wrap
 * @param maxLineLength - The maximum length of a line, defaults to the console width
 * @param indent - The number of spaces to indent the wrapped lines, defaults to 0
 */
export declare function wrapWordsToLines(text: string, maxLineLength?: number, indent?: number): string[];
/**
 * Applies word wrapping and returns an array of lines.
 *
 * @param text - The text to wrap
 * @param maxLineLength - The maximum length of a line, defaults to the console width
 * @param linePrefix - The string to prefix each line with, defaults to ''
 */
export declare function wrapWordsToLines(text: string, maxLineLength?: number, linePrefix?: string): string[];
/**
 * Applies word wrapping and returns an array of lines.
 *
 * @param text - The text to wrap
 * @param maxLineLength - The maximum length of a line, defaults to the console width
 * @param indentOrLinePrefix - The number of spaces to indent the wrapped lines or the string to prefix
 * each line with, defaults to no prefix
 */
export declare function wrapWordsToLines(text: string, maxLineLength?: number, indentOrLinePrefix?: number | string): string[];
//# sourceMappingURL=wrap-words-to-lines.d.ts.map