import { Request, Response } from 'express';
import Notification from '../models/Notification';
import Student from '../models/Student';
import College from '../models/College';
import Event from '../models/Event';

// Create and send notification
export const createNotification = async (req: Request, res: Response) => {
  try {
    const {
      title,
      message,
      type,
      recipientType,
      recipientIds,
      relatedEvent,
      scheduledFor
    } = req.body;

    // @ts-ignore
    const senderId = req.college.id;

    const notification = new Notification({
      title,
      message,
      type,
      recipients: {
        students: recipientType === 'students' ? recipientIds : [],
        colleges: recipientType === 'colleges' ? recipientIds : []
      },
      sender: senderId,
      relatedEvent,
      status: scheduledFor ? 'scheduled' : 'sent',
      scheduledFor,
      sentAt: scheduledFor ? undefined : new Date()
    });

    await notification.save();

    // TODO: Implement actual push notification service (Firebase, etc.)
    // For now, we'll just save to database

    res.status(201).json({
      success: true,
      message: 'Notification created successfully',
      notification
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get notifications sent by college
export const getCollegeNotifications = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    const notifications = await Notification.find({ sender: collegeId })
      .populate('relatedEvent', 'title startDate')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      notifications
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Send notification to event participants
export const notifyEventParticipants = async (req: Request, res: Response) => {
  try {
    const { eventId } = req.params;
    const { title, message, type } = req.body;

    // @ts-ignore
    const senderId = req.college.id;

    // Get event and verify ownership
    const event = await Event.findById(eventId);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    if (event.organizer.toString() !== senderId) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    // Get all students registered for this event
    // TODO: Implement EventRegistration model query
    const registeredStudents: string[] = []; // Placeholder

    const notification = new Notification({
      title,
      message,
      type: type || 'event_update',
      recipients: {
        students: registeredStudents,
        colleges: []
      },
      sender: senderId,
      relatedEvent: eventId,
      status: 'sent',
      sentAt: new Date()
    });

    await notification.save();

    res.json({
      success: true,
      message: 'Notification sent to event participants',
      notification
    });
  } catch (error) {
    console.error('Notify event participants error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get notification analytics
export const getNotificationAnalytics = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    const analytics = await Notification.aggregate([
      { $match: { sender: collegeId } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalClicks: { $sum: '$clickCount' },
          avgClicks: { $avg: '$clickCount' }
        }
      }
    ]);

    const totalNotifications = await Notification.countDocuments({ sender: collegeId });
    const totalClicks = await Notification.aggregate([
      { $match: { sender: collegeId } },
      { $group: { _id: null, total: { $sum: '$clickCount' } } }
    ]);

    res.json({
      success: true,
      analytics: {
        totalNotifications,
        totalClicks: totalClicks[0]?.total || 0,
        byType: analytics
      }
    });
  } catch (error) {
    console.error('Get notification analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Mark notification as read
export const markNotificationAsRead = async (req: Request, res: Response) => {
  try {
    const { notificationId } = req.params;
    // @ts-ignore
    const userId = req.user?.id || req.college?.id;

    await Notification.findByIdAndUpdate(
      notificationId,
      {
        $addToSet: {
          readBy: {
            user: userId,
            readAt: new Date()
          }
        }
      }
    );

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Track notification click
export const trackNotificationClick = async (req: Request, res: Response) => {
  try {
    const { notificationId } = req.params;

    await Notification.findByIdAndUpdate(
      notificationId,
      { $inc: { clickCount: 1 } }
    );

    res.json({
      success: true,
      message: 'Click tracked'
    });
  } catch (error) {
    console.error('Track notification click error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
