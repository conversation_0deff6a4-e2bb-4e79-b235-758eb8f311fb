{"version": 3, "file": "static/css/main.5a796bc5.css", "mappings": "0JAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,mIAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CCTA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAGE,aAAc,CAFd,6EAAyF,CACzF,eAAgB,CAEhB,iBACF,CAGA,KACE,sBACF,CAGA,eACE,YAAa,CACb,qBAAsB,CACtB,gBACF,CAEA,QAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,kDAA6D,CAG7D,+BAAyC,CAFzC,UAAY,CACZ,cAAe,CAEf,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YAEF,CAEA,gBAME,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,MAEE,UAAY,CADZ,oBAAqB,CAErB,uBACF,CAEA,YACE,qBACF,CAEA,WAME,6BAAoC,CAFpC,8CAAoD,CACpD,4BAA6B,CAE7B,oBAAqB,CANrB,8BAAkC,CAClC,gBAAiB,CACjB,eAKF,CAEA,eAIE,kBAAmB,CAHnB,YAAa,CAEb,QAAS,CADT,eAGF,CAEA,oBAKE,kBAAmB,CAJnB,UAAY,CAEZ,eAAgB,CAKhB,eAAgB,CAJhB,kBAAoB,CAGpB,iBAAkB,CALlB,oBAAqB,CAIrB,uBAGF,CAEA,2BAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIF,CAEA,iCACE,SACF,CAEA,0BACE,oBAAoC,CACpC,0BACF,CAEA,MACE,QAAO,CAEP,8BAA+B,CAD/B,YAEF,CAEA,WACE,QAAO,CAEP,gBAAiB,CADjB,SAEF,CAEA,MAIE,wBAAyB,CACzB,iBAAkB,CAHlB,kBAAmB,CACnB,iBAAkB,CAFlB,iBAKF,CAEA,SAGE,aAAc,CAFd,cAAe,CACf,kBAEF,CAEA,QAGE,aAAc,CAFd,gBAAiB,CACjB,kBAEF,CAEA,SACE,YAAa,CAEb,QAAS,CADT,sBAEF,CAEA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAGf,oBAAqB,CAFrB,eAAiB,CAJjB,qBAAuB,CAKvB,oBAAqB,CAErB,+BACF,CAEA,SACE,wBAAyB,CACzB,UACF,CAEA,WACE,wBAAyB,CACzB,UACF,CAEA,UACE,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAEA,SAGE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAJxC,QAAO,CACP,cAAe,CAIf,iBACF,CAEA,YAEE,aAAc,CADd,kBAEF,CAEA,QACE,kDAA6D,CAC7D,UAAY,CAGZ,eAAgB,CADhB,iBAAkB,CAElB,iBAAkB,CAHlB,iBAIF,CAEA,eAOE,uDAAsF,CANtF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,UACE,eAAiB,CAEjB,eAAgB,CADhB,UAEF,CAGA,WAIE,qBAAuB,CACvB,iBAAkB,CAClB,+BAAyC,CAJzC,gBAAiB,CADjB,eAAgB,CAEhB,YAIF,CAEA,gBACE,eACF,CAEA,YACE,kBACF,CAEA,kBAIE,aAAc,CAHd,aAAc,CAEd,eAAiB,CADjB,mBAGF,CAEA,qCAIE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,cAAgB,CADhB,UAKF,CAEA,iDAGE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,aACE,wBAAyB,CAIzB,WAAY,CACZ,iBAAkB,CAJlB,UAAY,CAOZ,cAAe,CAFf,cAAe,CACf,eAAiB,CAJjB,cAAgB,CAMhB,+BAAiC,CAPjC,UAQF,CAEA,mBACE,wBACF,CAGA,oBAGE,kBAAmB,CAGnB,kDAA6D,CAL7D,YAAa,CACb,sBAAuB,CAEvB,eAAgB,CAChB,YAEF,CAEA,cAQE,8BAAgC,CAPhC,eAAiB,CACjB,kBAAmB,CAEnB,gCAA0C,CAC1C,eAAgB,CAFhB,YAAa,CAIb,iBAAkB,CADlB,UAGF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,oBACE,aAAc,CACd,gBAAiB,CAEjB,eAAgB,CADhB,mBAEF,CAEA,mBACE,aAAc,CACd,gBAAiB,CACjB,oBACF,CAEA,kBACE,YAAa,CACb,qBAAsB,CACtB,UAAW,CACX,kBACF,CAEA,iBAME,sBAA6B,CAH7B,kBAAmB,CAFnB,aAAc,CAOd,eAAgB,CANhB,YAAa,CAKb,iBAAkB,CAHlB,oBAAqB,CACrB,uBAIF,CAEA,wBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIF,CAEA,8BACE,SACF,CAEA,gBACE,kDAAqD,CACrD,UACF,CAEA,sBAGE,oBAAqB,CADrB,gCAA+C,CAD/C,0BAGF,CAEA,gBACE,kDAAqD,CACrD,UACF,CAEA,sBAGE,oBAAqB,CADrB,gCAA8C,CAD9C,0BAGF,CAEA,aAGE,aAAc,CAFd,cAAe,CACf,kBAEF,CAEA,oBACE,gBAAiB,CAEjB,eAAgB,CADhB,mBAEF,CAEA,mBACE,gBAAkB,CAElB,eAAgB,CADhB,UAEF,CAEA,iBAIE,4BAA6B,CAH7B,aAAc,CACd,cAAe,CACf,kBAEF,CAEA,YACE,aAAc,CAEd,eAAgB,CADhB,oBAAqB,CAErB,yBACF,CAEA,kBACE,aAAc,CACd,yBACF,CAGA,gBAGE,kBAAmB,CAGnB,kDAA6D,CAL7D,YAAa,CACb,sBAAuB,CAEvB,eAAgB,CAChB,YAEF,CAEA,UAOE,8BAAgC,CANhC,eAAiB,CACjB,kBAAmB,CAEnB,gCAA0C,CAC1C,eAAgB,CAFhB,YAAa,CAGb,UAEF,CAEA,aAEE,oBAAqB,CADrB,iBAEF,CAEA,gBAEE,gBAGF,CAEA,gCANE,aAAc,CAGd,eAAgB,CADhB,mBASF,CALA,gBAEE,cAGF,CAEA,eACE,aAAc,CACd,gBACF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,uBACE,eACF,CAEA,6BAIE,aAAc,CAHd,aAAc,CAId,gBAAkB,CAFlB,eAAgB,CADhB,mBAIF,CAEA,2FASE,wBAAyB,CAJzB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CAHf,YAAa,CAIb,uBAAyB,CALzB,UAOF,CAEA,6GAKE,qBAAuB,CADvB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAEA,UACE,kDAAqD,CAErD,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CAFf,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CANhB,iBAAkB,CAKlB,uBAEF,CAEA,gBAEE,gCAA+C,CAD/C,0BAEF,CAEA,mBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,aAIE,4BAA6B,CAC7B,aAAc,CAHd,eAAgB,CAChB,kBAAmB,CAFnB,iBAKF,CAEA,WACE,aAAc,CAEd,eAAgB,CADhB,oBAAqB,CAErB,yBACF,CAEA,iBACE,aAAc,CACd,yBACF,CAEA,eACE,wBAAyB,CAKzB,6BAA8B,CAJ9B,aAMF,CAEA,gCANE,kBAAmB,CAGnB,eAAgB,CAFhB,oBAAqB,CAFrB,YAeF,CARA,iBACE,wBAAyB,CAKzB,6BAA8B,CAJ9B,aAMF,CAGA,yBACE,oCAGE,eAAgB,CADhB,YAEF,CAEA,wBAGE,WAAY,CADZ,YAEF,CAEA,oCAEE,gBACF,CAEA,iBACE,cACF,CAEA,aACE,gBACF,CAEA,2FAGE,aACF,CACF,CAIA,gBAEE,iBAAkB,CADlB,UAEF,CAGA,cAOE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,6BAAiD,CALjD,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAON,uBAAyB,CAJzB,YAKF,CAEA,aAME,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,iBAIF,CAEA,UAQE,6BAAoC,CAFpC,iDAAoD,CACpD,4BAA6B,CAE7B,oBAAqB,CALrB,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,oBAKF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,aAKE,kBAAmB,CAJnB,aAAc,CAEd,eAAgB,CAChB,kBAAoB,CAFpB,oBAAqB,CAIrB,uBACF,CAEA,mBACE,oBAAoC,CACpC,aACF,CAEA,SACE,2DAA+D,CAC/D,oBAAuB,CAEvB,yBAA2B,CAD3B,+BAEF,CAEA,eACE,2DAA+D,CAE/D,+BAA+C,CAD/C,0BAEF,CAGA,cAIE,kBAAmB,CAInB,yCAA0C,CAF1C,8DAA0E,CAC1E,yBAA0B,CAJ1B,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAOjB,eAAgB,CARhB,iBASF,CAEA,qBAOE,qLAKF,CAEA,yCARE,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SAYF,CATA,oBAOE,slBAEF,CAEA,yBACE,GAAK,yBAA6B,CAClC,IAAM,4BAA+B,CACrC,GAAO,yBAA6B,CACtC,CAEA,cAKE,aAAc,CADd,gBAAiB,CAEjB,cAAe,CALf,iBAAkB,CAElB,UAAW,CADX,SAKF,CAEA,cAGE,8BAA+B,CAD/B,UAAY,CADZ,iBAGF,CAEA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,YACE,8BAAkC,CAClC,kCAAqC,CACrC,eAAgB,CAChB,eAAgB,CAChB,oBAAqB,CACrB,gCACF,CAEA,WAGE,6BAAoC,CAFpC,8CAAoD,CACpD,4BAA6B,CAE7B,oBAAqB,CACrB,iBACF,CAEA,eACE,kCAAqC,CACrC,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CAEnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CAGhB,WAAa,CACb,4BACF,CAEA,cACE,YAAa,CAIb,cAAe,CAHf,UAAW,CACX,sBAAuB,CACvB,kBAEF,CAEA,UAEE,kBAAmB,CAGnB,kBAAmB,CAJnB,mBAAoB,CAOpB,gBAAiB,CADjB,eAAgB,CAJhB,SAAW,CAUX,sBAAuB,CADvB,eAAgB,CADhB,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CAJlB,oBAAqB,CAGrB,uBAKF,CAEA,iBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIF,CAEA,uBACE,SACF,CAEA,kBACE,iDAAoD,CAEpD,+BAA+C,CAD/C,UAEF,CAEA,wBAEE,gCAAgD,CADhD,0BAEF,CAEA,oBAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,gBAAoC,CAEpC,0BAA0C,CAD1C,UAGF,CAEA,0BACE,oBAAoC,CAEpC,4BAAgD,CADhD,0BAEF,CAGA,YACE,YAAa,CAIb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,eAEF,CAEA,MAEE,uCAAyC,CADzC,iBAEF,CAEA,SAOE,6BAAoC,CAFpC,8CAAoD,CACpD,4BAA6B,CAE7B,oBAAqB,CAPrB,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAChB,mBAKF,CAEA,QACE,eAAiB,CAIjB,eAAgB,CADhB,kBAAmB,CAFnB,UAAY,CACZ,wBAGF,CAGA,kBAEE,+CAA6D,CAD7D,cAAe,CAEf,iBACF,CAEA,yBAOE,kDAAkF,CANlF,UAAW,CAKX,YAAa,CAFb,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,cACF,CAEA,gBAEE,kBAAmB,CADnB,iBAEF,CAEA,mBAIE,aAAc,CAHd,8BAAkC,CAClC,8BAAiC,CACjC,eAAgB,CAEhB,kBAAmB,CACnB,iBACF,CAEA,yBAQE,iDAAoD,CACpD,iBAAkB,CANlB,YAAa,CAFb,UAAW,CAMX,UAAW,CAHX,QAAS,CAFT,iBAAkB,CAGlB,0BAA2B,CAC3B,UAIF,CAEA,kBAEE,aAAc,CADd,gBAAiB,CAIjB,eAAgB,CADhB,aAAc,CADd,eAGF,CAEA,eAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,eACF,CAEA,cACE,eAAiB,CAQjB,0BAA0C,CAN1C,kBAAmB,CACnB,gCAA2C,CAI3C,eAAgB,CANhB,iBAAkB,CAKlB,iBAAkB,CAFlB,iBAAkB,CAClB,uBAIF,CAEA,qBAOE,iDAAoD,CANpD,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAKN,mBAAoB,CACpB,6BACF,CAEA,2BACE,mBACF,CAEA,oBAEE,gCAA2C,CAD3C,2BAEF,CAEA,cAGE,aAAc,CACd,oBAAsB,CAHtB,cAAe,CACf,oBAAqB,CAGrB,uBACF,CAEA,kCAEE,mBAAoB,CADpB,oBAEF,CAEA,iBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,kBACF,CAEA,gBACE,aAAc,CAGd,cAAe,CAFf,eAAgB,CAChB,oBAEF,CAEA,cACE,eACF,CAEA,gBACE,aAAc,CAGd,gBAAkB,CADlB,eAAgB,CAGhB,iBAAkB,CAJlB,oBAAqB,CAGrB,uBAEF,CAEA,sBAOE,iDAAoD,CAJpD,WAAY,CAFZ,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAMlB,yBAA2B,CAH3B,OAIF,CAEA,4BACE,UACF,CAEA,sBACE,aACF,CAGA,aAEE,kDAA6D,CAE7D,eAAgB,CAHhB,cAAe,CAEf,iBAEF,CAEA,oBAOE,4HAEiF,CAHjF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAON,SACF,CAEA,aAGE,UAAW,CAEX,UAAY,CAJZ,iBAAkB,CAGlB,iBAAkB,CAFlB,SAIF,CAEA,gBACE,UAAW,CACX,8BAAkC,CAClC,8BAAiC,CACjC,eAAgB,CAChB,kBAAmB,CACnB,gCACF,CAEA,eACE,UAAW,CACX,gBAAiB,CACjB,kBAAmB,CAGnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CADhB,UAIF,CAEA,aACE,YAAa,CAGb,cAAe,CAFf,UAGF,CAEA,sBAJE,sBAiBF,CAbA,SAEE,kBAAmB,CAEnB,kBAAmB,CAHnB,mBAAoB,CAMpB,gBAAiB,CADjB,eAAgB,CAKhB,eAAgB,CADhB,eAAgB,CAPhB,mBAAoB,CAMpB,iBAAkB,CAJlB,oBAAqB,CAGrB,uBAKF,CAEA,gBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIF,CAEA,sBACE,SACF,CAEA,iBACE,iDAAoD,CAEpD,+BAA8C,CAD9C,UAEF,CAEA,uBAEE,gCAA+C,CAD/C,0BAEF,CAEA,mBAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,iDAAoD,CAEpD,0BAA0C,CAD1C,UAGF,CAEA,yBACE,oBAAoC,CAEpC,gCAAgD,CADhD,0BAEF,CAGA,yBACE,cACE,oBACF,CAEA,aACE,mBACF,CAEA,UACE,gBACF,CAEA,WACE,QACF,CAEA,aAEE,eAAiB,CADjB,mBAEF,CAEA,cACE,eAAgB,CAChB,cACF,CAEA,cACE,cACF,CAEA,YACE,gBAAiB,CACjB,kBACF,CAEA,eACE,gBAAiB,CACjB,kBACF,CAEA,cAEE,kBAAmB,CADnB,qBAAsB,CAEtB,QAAS,CACT,kBACF,CAEA,UAEE,eAAgB,CADhB,UAEF,CAEA,YACE,QACF,CAEA,SACE,cACF,CAEA,kBACE,cACF,CAEA,eAEE,QAAS,CADT,yBAEF,CAEA,cACE,mBACF,CAEA,aACE,cACF,CAEA,aAEE,kBAAmB,CADnB,qBAAsB,CAEtB,QACF,CAEA,SAEE,eAAgB,CADhB,UAEF,CACF,CAEA,yBACE,YACE,cACF,CAEA,eACE,cACF,CAEA,mBACE,gBACF,CAEA,cACE,cACF,CAEA,WACE,cACF,CACF,CAIA,eAGE,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CAHnB,kBAAmB,CACnB,cAIF,CAEA,kBACE,aAAc,CAEd,8BAAkC,CAClC,eAAgB,CAFhB,kBAGF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CAGb,cAAe,CAFf,QAGF,CAEA,aAQE,qBAAuB,CAJvB,wBAAyB,CACzB,kBAAmB,CAJnB,QAAO,CAKP,cAAe,CAJf,eAAgB,CAChB,cAAgB,CAIhB,uBAEF,CAEA,mBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,qBAEE,eAAW,CADX,YAAa,CACb,UACF,CAEA,oBACE,eAAiB,CAGjB,wBAAyB,CADzB,kBAAmB,CAEnB,+BAA0C,CAH1C,cAAe,CAIf,uBACF,CAEA,0BAEE,+BAAyC,CADzC,0BAEF,CAEA,uBACE,aAAc,CAEd,8BAAkC,CAElC,gBAAiB,CADjB,eAAgB,CAFhB,kBAIF,CAEA,sBAEE,aAAc,CACd,eAAgB,CAFhB,mBAGF,CAEA,2BACE,aAAc,CACd,eACF,CAGA,yBACE,YAEE,mBAAoB,CADpB,qBAEF,CAEA,aACE,cACF,CAEA,UAEE,WAAY,CADZ,wBAEF,CACF,CAIA,mBAIE,kBAAmB,CADnB,aAAc,CADd,gBAAiB,CAGjB,gBAAiB,CAJjB,YAKF,CAEA,kBACE,kBAAmB,CACnB,iBACF,CAEA,qBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,oBAEE,aAAc,CADd,gBAEF,CAGA,YAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,kBACF,CAEA,WAME,kBAAmB,CALnB,eAAiB,CAQjB,qBAAsB,CANtB,kBAAmB,CACnB,+BAA0C,CAC1C,YAAa,CAEb,UAAW,CALX,YAAa,CAMb,uBAEF,CAEA,iBAEE,+BAA0C,CAD1C,0BAEF,CAEA,kBACE,yBACF,CAEA,yBACE,yBACF,CAEA,yBACE,yBACF,CAEA,0BACE,yBACF,CAEA,WACE,gBAAiB,CACjB,UACF,CAEA,iBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,gBAEE,aAAc,CADd,cAAe,CAEf,eACF,CAEA,aAEE,aAAc,CADd,eAEF,CAGA,eACE,kBACF,CAEA,kBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,oBACF,CAEA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,YAEE,kBAAmB,CAGnB,eAAiB,CAOjB,2BAAkC,CANlC,kBAAmB,CAInB,+BAA0C,CAF1C,aAAc,CAPd,YAAa,CAQb,eAAgB,CANhB,QAAS,CACT,cAAe,CAGf,oBAAqB,CAIrB,uBAEF,CAEA,kBAEE,+BAA0C,CAC1C,aAAc,CAFd,0BAGF,CAEA,yBACE,yBACF,CAEA,8BACE,yBACF,CAEA,kCACE,yBACF,CAEA,2BACE,yBACF,CAEA,iBACE,gBACF,CAGA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,mBACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAA0C,CAD1C,YAEF,CAEA,gBAGE,kBAAmB,CAGnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,oBAAqB,CACrB,mBAEF,CAEA,mBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,UACE,aAAc,CAGd,eAAiB,CADjB,eAAgB,CADhB,oBAAqB,CAGrB,yBACF,CAEA,gBACE,aACF,CAGA,sDAGE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mDAKE,kBAAmB,CAEnB,kBAAmB,CACnB,kBAAmB,CALnB,YAAa,CACb,6BAA8B,CAE9B,YAAa,CAGb,uBACF,CAEA,qEAGE,kBAAmB,CACnB,yBACF,CAEA,4DAKE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,yDAIE,aAAc,CADd,eAAiB,CAEjB,QACF,CAEA,UAEE,aAAc,CADd,eAEF,CAEA,iCAIE,oBAAqB,CAFrB,YAAa,CACb,qBAAsB,CAEtB,UACF,CAEA,QAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAgB,CAHhB,qBAAwB,CAIxB,wBACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,aACE,kBAAmB,CACnB,aACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAEA,sBAGE,aAAc,CADd,eAEF,CAGA,uBACE,YAAa,CACb,SACF,CAEA,wBAGE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAJhB,kBAAoB,CAMpB,uBACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBACF,CAGA,SAEE,aAAc,CACd,iBAAkB,CAClB,YAAa,CAHb,iBAIF,CAGA,oCAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAAgB,CAChB,iBACF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,yBACE,mBACE,YACF,CAUA,gDACE,yBACF,CAEA,mDAIE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,iCAEE,sBAAuB,CACvB,kBAAmB,CACnB,QACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}", "/* Import Google Fonts */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');\n\n/* Basic Reset */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #2c3e50;\n  overflow-x: hidden;\n}\n\n/* Smooth scrolling */\nhtml {\n  scroll-behavior: smooth;\n}\n\n/* Layout */\n.app-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.header {\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 1rem 0;\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  backdrop-filter: blur(10px);\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.logo {\n  text-decoration: none;\n  color: white;\n  transition: all 0.3s ease;\n}\n\n.logo:hover {\n  transform: scale(1.05);\n}\n\n.header h1 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: linear-gradient(45deg, #ffffff, #ecf0f1);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.header nav ul {\n  display: flex;\n  list-style: none;\n  gap: 2rem;\n  align-items: center;\n}\n\n.header nav ul li a {\n  color: white;\n  text-decoration: none;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 25px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.header nav ul li a::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.header nav ul li a:hover::before {\n  left: 100%;\n}\n\n.header nav ul li a:hover {\n  background: rgba(255, 255, 255, 0.1);\n  transform: translateY(-2px);\n}\n\n.main {\n  flex: 1;\n  padding: 2rem;\n  min-height: calc(100vh - 140px);\n}\n\n.main-home {\n  flex: 1;\n  padding: 0;\n  min-height: 100vh;\n}\n\n.hero {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding: 3rem 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n\n.hero h2 {\n  font-size: 2rem;\n  margin-bottom: 1rem;\n  color: #2c3e50;\n}\n\n.hero p {\n  font-size: 1.2rem;\n  margin-bottom: 2rem;\n  color: #7f8c8d;\n}\n\n.buttons {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n  text-decoration: none;\n  display: inline-block;\n  transition: background-color 0.3s;\n}\n\n.primary {\n  background-color: #3498db;\n  color: white;\n}\n\n.secondary {\n  background-color: #95a5a6;\n  color: white;\n}\n\n.features {\n  display: flex;\n  justify-content: space-between;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.feature {\n  flex: 1;\n  padding: 1.5rem;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.feature h3 {\n  margin-bottom: 1rem;\n  color: #2c3e50;\n}\n\n.footer {\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  text-align: center;\n  padding: 2rem 1rem;\n  margin-top: auto;\n  position: relative;\n}\n\n.footer::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n}\n\n.footer p {\n  font-size: 0.9rem;\n  opacity: 0.8;\n  font-weight: 400;\n}\n\n/* Form Styles */\n.container {\n  max-width: 500px;\n  margin: 2rem auto;\n  padding: 2rem;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.form-container {\n  margin-top: 1rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #3498db;\n  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);\n}\n\n.btn-primary {\n  background-color: #3498db;\n  color: white;\n  width: 100%;\n  padding: 0.75rem;\n  border: none;\n  border-radius: 4px;\n  font-size: 1rem;\n  font-weight: bold;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.btn-primary:hover {\n  background-color: #2980b9;\n}\n\n/* Registration Page Styles */\n.register-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 80vh;\n  padding: 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.register-box {\n  background: white;\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n  text-align: center;\n  animation: slideUp 0.6s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.register-header h2 {\n  color: #2c3e50;\n  font-size: 2.2rem;\n  margin-bottom: 0.5rem;\n  font-weight: 700;\n}\n\n.register-header p {\n  color: #7f8c8d;\n  font-size: 1.1rem;\n  margin-bottom: 2.5rem;\n}\n\n.register-options {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.register-option {\n  display: block;\n  padding: 2rem;\n  border-radius: 15px;\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  position: relative;\n  overflow: hidden;\n}\n\n.register-option::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.register-option:hover::before {\n  left: 100%;\n}\n\n.student-option {\n  background: linear-gradient(135deg, #3498db, #2980b9);\n  color: white;\n}\n\n.student-option:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 30px rgba(52, 152, 219, 0.4);\n  border-color: #2980b9;\n}\n\n.college-option {\n  background: linear-gradient(135deg, #27ae60, #229954);\n  color: white;\n}\n\n.college-option:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 15px 30px rgba(39, 174, 96, 0.4);\n  border-color: #229954;\n}\n\n.option-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  display: block;\n}\n\n.register-option h3 {\n  font-size: 1.4rem;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.register-option p {\n  font-size: 0.95rem;\n  opacity: 0.9;\n  line-height: 1.4;\n}\n\n.register-footer {\n  color: #7f8c8d;\n  font-size: 1rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #ecf0f1;\n}\n\n.login-link {\n  color: #3498db;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.login-link:hover {\n  color: #2980b9;\n  text-decoration: underline;\n}\n\n/* Auth Container Styles (Login, Signup forms) */\n.auth-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 80vh;\n  padding: 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.auth-box {\n  background: white;\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 450px;\n  width: 100%;\n  animation: slideUp 0.6s ease-out;\n}\n\n.auth-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n}\n\n.auth-header h1 {\n  color: #2c3e50;\n  font-size: 2.2rem;\n  margin-bottom: 0.5rem;\n  font-weight: 700;\n}\n\n.auth-header h2 {\n  color: #2c3e50;\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n  font-weight: 700;\n}\n\n.auth-header p {\n  color: #7f8c8d;\n  font-size: 1.1rem;\n}\n\n.auth-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.auth-form .form-group {\n  margin-bottom: 0;\n}\n\n.auth-form .form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n}\n\n.auth-form .form-group input,\n.auth-form .form-group select,\n.auth-form .form-group textarea {\n  width: 100%;\n  padding: 1rem;\n  border: 2px solid #ecf0f1;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background-color: #f8f9fa;\n}\n\n.auth-form .form-group input:focus,\n.auth-form .form-group select:focus,\n.auth-form .form-group textarea:focus {\n  outline: none;\n  border-color: #3498db;\n  background-color: white;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);\n}\n\n.auth-btn {\n  background: linear-gradient(135deg, #3498db, #2980b9);\n  color: white;\n  border: none;\n  padding: 1rem 2rem;\n  border-radius: 10px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 1rem;\n}\n\n.auth-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);\n}\n\n.auth-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.auth-footer {\n  text-align: center;\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #ecf0f1;\n  color: #7f8c8d;\n}\n\n.auth-link {\n  color: #3498db;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.auth-link:hover {\n  color: #2980b9;\n  text-decoration: underline;\n}\n\n.error-message {\n  background-color: #ffebee;\n  color: #c62828;\n  padding: 1rem;\n  border-radius: 10px;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #c62828;\n  font-weight: 500;\n}\n\n.success-message {\n  background-color: #e8f5e8;\n  color: #2e7d32;\n  padding: 1rem;\n  border-radius: 10px;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #2e7d32;\n  font-weight: 500;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .register-container,\n  .auth-container {\n    padding: 1rem;\n    min-height: 70vh;\n  }\n\n  .register-box,\n  .auth-box {\n    padding: 2rem;\n    margin: 1rem;\n  }\n\n  .register-header h2,\n  .auth-header h1 {\n    font-size: 1.8rem;\n  }\n\n  .register-option {\n    padding: 1.5rem;\n  }\n\n  .option-icon {\n    font-size: 2.5rem;\n  }\n\n  .auth-form .form-group input,\n  .auth-form .form-group select,\n  .auth-form .form-group textarea {\n    padding: 0.8rem;\n  }\n}\n\n/* ===== HOME PAGE STYLES ===== */\n\n.home-container {\n  width: 100%;\n  overflow-x: hidden;\n}\n\n/* Floating Navigation */\n.floating-nav {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  transition: all 0.3s ease;\n}\n\n.nav-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 1rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.nav-logo {\n  font-family: 'Poppins', sans-serif;\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #2c3e50;\n  text-decoration: none;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.nav-links {\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n}\n\n.nav-links a {\n  color: #2c3e50;\n  text-decoration: none;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 25px;\n  transition: all 0.3s ease;\n}\n\n.nav-links a:hover {\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n}\n\n.nav-cta {\n  background: linear-gradient(45deg, #667eea, #764ba2) !important;\n  color: white !important;\n  padding: 0.75rem 1.5rem !important;\n  font-weight: 600 !important;\n}\n\n.nav-cta:hover {\n  background: linear-gradient(45deg, #764ba2, #667eea) !important;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n\n/* Hero Section */\n.hero-section {\n  position: relative;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\n  background-size: 400% 400%;\n  animation: gradientShift 15s ease infinite;\n  overflow: hidden;\n}\n\n.hero-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);\n  z-index: 1;\n}\n\n.hero-section::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"10\" cy=\"60\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"90\" cy=\"40\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  z-index: 1;\n}\n\n@keyframes gradientShift {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n.hero-overlay {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.hero-content {\n  text-align: center;\n  color: white;\n  animation: fadeInUp 1s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.hero-title {\n  font-family: 'Poppins', sans-serif;\n  font-size: clamp(2.5rem, 5vw, 4.5rem);\n  font-weight: 800;\n  line-height: 1.1;\n  margin-bottom: 1.5rem;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.highlight {\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  position: relative;\n}\n\n.hero-subtitle {\n  font-size: clamp(1.1rem, 2vw, 1.4rem);\n  font-weight: 400;\n  line-height: 1.6;\n  margin-bottom: 3rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  opacity: 0.95;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n\n.hero-buttons {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  margin-bottom: 4rem;\n  flex-wrap: wrap;\n}\n\n.btn-hero {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 2rem;\n  border-radius: 50px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1.1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  min-width: 180px;\n  justify-content: center;\n}\n\n.btn-hero::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.btn-hero:hover::before {\n  left: 100%;\n}\n\n.btn-hero.primary {\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n  color: white;\n  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);\n}\n\n.btn-hero.primary:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);\n}\n\n.btn-hero.secondary {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.btn-hero.secondary:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.2);\n}\n\n/* Hero Stats */\n.hero-stats {\n  display: flex;\n  justify-content: center;\n  gap: 3rem;\n  margin-top: 2rem;\n  flex-wrap: wrap;\n}\n\n.stat {\n  text-align: center;\n  animation: fadeInUp 1s ease-out 0.5s both;\n}\n\n.stat h3 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.stat p {\n  font-size: 0.9rem;\n  opacity: 0.8;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  font-weight: 500;\n}\n\n/* Features Section */\n.features-section {\n  padding: 6rem 0;\n  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);\n  position: relative;\n}\n\n.features-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100px;\n  background: linear-gradient(180deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 4rem;\n}\n\n.section-header h2 {\n  font-family: 'Poppins', sans-serif;\n  font-size: clamp(2rem, 4vw, 3rem);\n  font-weight: 700;\n  color: #2c3e50;\n  margin-bottom: 1rem;\n  position: relative;\n}\n\n.section-header h2::after {\n  content: '';\n  position: absolute;\n  bottom: -10px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60px;\n  height: 4px;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  border-radius: 2px;\n}\n\n.section-header p {\n  font-size: 1.2rem;\n  color: #7f8c8d;\n  max-width: 500px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 2.5rem;\n  margin-top: 3rem;\n}\n\n.feature-card {\n  background: white;\n  padding: 3rem 2rem;\n  border-radius: 20px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);\n  text-align: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  border: 1px solid rgba(102, 126, 234, 0.1);\n}\n\n.feature-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  transform: scaleX(0);\n  transition: transform 0.3s ease;\n}\n\n.feature-card:hover::before {\n  transform: scaleX(1);\n}\n\n.feature-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n}\n\n.feature-icon {\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n  display: block;\n  filter: grayscale(0.2);\n  transition: all 0.3s ease;\n}\n\n.feature-card:hover .feature-icon {\n  transform: scale(1.1);\n  filter: grayscale(0);\n}\n\n.feature-card h3 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 1rem;\n}\n\n.feature-card p {\n  color: #7f8c8d;\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n  font-size: 1rem;\n}\n\n.feature-link {\n  margin-top: auto;\n}\n\n.feature-link a {\n  color: #667eea;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.feature-link a::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background: linear-gradient(45deg, #667eea, #764ba2);\n  transition: width 0.3s ease;\n}\n\n.feature-link a:hover::after {\n  width: 100%;\n}\n\n.feature-link a:hover {\n  color: #764ba2;\n}\n\n/* CTA Section */\n.cta-section {\n  padding: 6rem 0;\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.cta-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.2) 0%, transparent 50%),\n    radial-gradient(circle at 80% 80%, rgba(155, 89, 182, 0.2) 0%, transparent 50%);\n  z-index: 1;\n}\n\n.cta-content {\n  position: relative;\n  z-index: 2;\n  color:black;\n  text-align: center;\n  color: white;\n}\n\n.cta-content h2 {\n  color:black;\n  font-family: 'Poppins', sans-serif;\n  font-size: clamp(2rem, 4vw, 3rem);\n  font-weight: 700;\n  margin-bottom: 1rem;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.cta-content p {\n  color:black;\n  font-size: 1.2rem;\n  margin-bottom: 3rem;\n  opacity: 0.9;\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.cta-buttons {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.btn-cta {\n  display: inline-flex;\n  align-items: center;\n  padding: 1rem 2.5rem;\n  border-radius: 50px;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1.1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  min-width: 200px;\n  justify-content: center;\n}\n\n.btn-cta::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.btn-cta:hover::before {\n  left: 100%;\n}\n\n.btn-cta.primary {\n  background: linear-gradient(45deg, #3498db, #2980b9);\n  color: white;\n  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);\n}\n\n.btn-cta.primary:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(52, 152, 219, 0.6);\n}\n\n.btn-cta.secondary {\n  background: linear-gradient(45deg, #3498db, #2980b9);\n  color: white;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.btn-cta.secondary:hover {\n  background: rgba(255, 255, 255, 0.1);\n  transform: translateY(-3px);\n  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.1);\n}\n\n/* Responsive Design for Home Page */\n@media (max-width: 768px) {\n  .floating-nav {\n    background: rgba(255, 255, 255, 0.98);\n  }\n\n  .nav-content {\n    padding: 0.75rem 1rem;\n  }\n\n  .nav-logo {\n    font-size: 1.2rem;\n  }\n\n  .nav-links {\n    gap: 1rem;\n  }\n\n  .nav-links a {\n    padding: 0.4rem 0.8rem;\n    font-size: 0.9rem;\n  }\n\n  .hero-section {\n    min-height: 80vh;\n    padding: 2rem 0;\n  }\n\n  .hero-content {\n    padding: 0 1rem;\n  }\n\n  .hero-title {\n    font-size: 2.5rem;\n    margin-bottom: 1rem;\n  }\n\n  .hero-subtitle {\n    font-size: 1.1rem;\n    margin-bottom: 2rem;\n  }\n\n  .hero-buttons {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  .btn-hero {\n    width: 100%;\n    max-width: 280px;\n  }\n\n  .hero-stats {\n    gap: 2rem;\n  }\n\n  .stat h3 {\n    font-size: 2rem;\n  }\n\n  .features-section {\n    padding: 4rem 0;\n  }\n\n  .features-grid {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .feature-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .cta-section {\n    padding: 4rem 0;\n  }\n\n  .cta-buttons {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n\n  .btn-cta {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero-title {\n    font-size: 2rem;\n  }\n\n  .hero-subtitle {\n    font-size: 1rem;\n  }\n\n  .section-header h2 {\n    font-size: 1.8rem;\n  }\n\n  .feature-icon {\n    font-size: 3rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n/* ===== ADMIN PAGE STYLES ===== */\n\n.admin-section {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 15px;\n  border: 1px solid #e9ecef;\n}\n\n.admin-section h3 {\n  color: #2c3e50;\n  margin-bottom: 1rem;\n  font-family: 'Poppins', sans-serif;\n  font-weight: 600;\n}\n\n.admin-form {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.admin-input {\n  flex: 1;\n  min-width: 250px;\n  padding: 0.75rem;\n  border: 2px solid #ecf0f1;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background-color: white;\n}\n\n.admin-input:focus {\n  outline: none;\n  border-color: #3498db;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);\n}\n\n.admin-colleges-list {\n  display: grid;\n  gap: 1.5rem;\n}\n\n.admin-college-card {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 15px;\n  border: 1px solid #e9ecef;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.admin-college-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.admin-college-card h4 {\n  color: #2c3e50;\n  margin-bottom: 1rem;\n  font-family: 'Poppins', sans-serif;\n  font-weight: 600;\n  font-size: 1.2rem;\n}\n\n.admin-college-card p {\n  margin-bottom: 0.5rem;\n  color: #7f8c8d;\n  line-height: 1.5;\n}\n\n.admin-college-card strong {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n/* Responsive Admin Styles */\n@media (max-width: 768px) {\n  .admin-form {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .admin-input {\n    min-width: auto;\n  }\n\n  .auth-box {\n    max-width: 100% !important;\n    margin: 1rem;\n  }\n}\n\n/* ===== COLLEGE DASHBOARD STYLES ===== */\n\n.college-dashboard {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.dashboard-header {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n\n.dashboard-header h1 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n}\n\n.dashboard-header p {\n  font-size: 1.1rem;\n  color: #7f8c8d;\n}\n\n/* Stats Grid */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n\n.stat-card {\n  background: white;\n  padding: 2rem;\n  border-radius: 15px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  transition: all 0.3s ease;\n  border-left: 4px solid;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\n}\n\n.stat-card.events {\n  border-left-color: #3498db;\n}\n\n.stat-card.registrations {\n  border-left-color: #2ecc71;\n}\n\n.stat-card.notifications {\n  border-left-color: #f39c12;\n}\n\n.stat-card.collaborations {\n  border-left-color: #9b59b6;\n}\n\n.stat-icon {\n  font-size: 2.5rem;\n  opacity: 0.8;\n}\n\n.stat-content h3 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.stat-content p {\n  font-size: 1rem;\n  color: #7f8c8d;\n  margin: 0.25rem 0;\n}\n\n.stat-detail {\n  font-size: 0.9rem;\n  color: #95a5a6;\n}\n\n/* Quick Actions */\n.quick-actions {\n  margin-bottom: 3rem;\n}\n\n.quick-actions h2 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 1.8rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 1.5rem;\n}\n\n.action-buttons {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 12px;\n  text-decoration: none;\n  color: #2c3e50;\n  font-weight: 600;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n}\n\n.action-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\n  color: #2c3e50;\n}\n\n.action-btn.create-event {\n  border-left-color: #3498db;\n}\n\n.action-btn.send-notification {\n  border-left-color: #f39c12;\n}\n\n.action-btn.manage-collaborations {\n  border-left-color: #9b59b6;\n}\n\n.action-btn.view-analytics {\n  border-left-color: #2ecc71;\n}\n\n.action-btn span {\n  font-size: 1.5rem;\n}\n\n/* Dashboard Sections */\n.dashboard-sections {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 2rem;\n}\n\n.dashboard-section {\n  background: white;\n  border-radius: 15px;\n  padding: 2rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #ecf0f1;\n}\n\n.section-header h3 {\n  font-family: 'Poppins', sans-serif;\n  font-size: 1.3rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.view-all {\n  color: #3498db;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: color 0.3s ease;\n}\n\n.view-all:hover {\n  color: #2980b9;\n}\n\n/* Event Items */\n.events-list,\n.collaborations-list,\n.notifications-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.event-item,\n.collaboration-item,\n.notification-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 10px;\n  transition: all 0.3s ease;\n}\n\n.event-item:hover,\n.collaboration-item:hover,\n.notification-item:hover {\n  background: #ecf0f1;\n  transform: translateX(5px);\n}\n\n.event-info h4,\n.collaboration-info h4,\n.notification-info h4 {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin: 0 0 0.25rem 0;\n}\n\n.event-info p,\n.collaboration-info p,\n.notification-info p {\n  font-size: 0.9rem;\n  color: #7f8c8d;\n  margin: 0;\n}\n\n.location {\n  font-size: 0.8rem;\n  color: #95a5a6;\n}\n\n.event-stats,\n.notification-stats {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.25rem;\n}\n\n.status {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.status.published {\n  background: #d5f4e6;\n  color: #27ae60;\n}\n\n.status.draft {\n  background: #fef9e7;\n  color: #f39c12;\n}\n\n.status.ongoing {\n  background: #ebf3fd;\n  color: #3498db;\n}\n\n.status.completed {\n  background: #f4f4f4;\n  color: #7f8c8d;\n}\n\n.status.sent {\n  background: #d5f4e6;\n  color: #27ae60;\n}\n\n.status.scheduled {\n  background: #fef9e7;\n  color: #f39c12;\n}\n\n.participants,\n.clicks {\n  font-size: 0.8rem;\n  color: #95a5a6;\n}\n\n/* Collaboration Actions */\n.collaboration-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-accept,\n.btn-reject {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-accept {\n  background: #2ecc71;\n  color: white;\n}\n\n.btn-accept:hover {\n  background: #27ae60;\n}\n\n.btn-reject {\n  background: #e74c3c;\n  color: white;\n}\n\n.btn-reject:hover {\n  background: #c0392b;\n}\n\n/* No Data State */\n.no-data {\n  text-align: center;\n  color: #95a5a6;\n  font-style: italic;\n  padding: 2rem;\n}\n\n/* Loading and Error States */\n.dashboard-loading,\n.dashboard-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 60vh;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid #ecf0f1;\n  border-top: 4px solid #3498db;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive Dashboard */\n@media (max-width: 768px) {\n  .college-dashboard {\n    padding: 1rem;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .action-buttons {\n    grid-template-columns: 1fr;\n  }\n\n  .dashboard-sections {\n    grid-template-columns: 1fr;\n  }\n\n  .event-item,\n  .collaboration-item,\n  .notification-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n\n  .event-stats,\n  .notification-stats {\n    align-items: flex-start;\n    flex-direction: row;\n    gap: 1rem;\n  }\n}"], "names": [], "sourceRoot": ""}