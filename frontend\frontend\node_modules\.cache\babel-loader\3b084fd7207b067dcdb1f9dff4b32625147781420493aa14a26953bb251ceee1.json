{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/workuuu/frontend/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{registerCollege}from'../../services/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CollegeSignup=()=>{const navigate=useNavigate();const[formData,setFormData]=useState({name:'',email:'',password:'',confirmPassword:'',address:'',documents:[]});const[error,setError]=useState('');const[loading,setLoading]=useState(false);const handleChange=e=>{const{name,value}=e.target;setFormData(_objectSpread(_objectSpread({},formData),{},{[name]:value}));};const handleFileChange=e=>{if(e.target.files){setFormData(_objectSpread(_objectSpread({},formData),{},{documents:Array.from(e.target.files)}));}};const handleSubmit=async e=>{e.preventDefault();if(formData.password!==formData.confirmPassword){setError('Passwords do not match');return;}try{setLoading(true);setError('');console.log('Submitting college registration form with data:',{name:formData.name,email:formData.email,address:formData.address,documentsCount:formData.documents.length});const response=await registerCollege({name:formData.name,email:formData.email,password:formData.password,address:formData.address,documents:formData.documents});console.log('Registration successful:',response);navigate('/verification-pending');}catch(err){var _err$response,_err$response$data;console.error('Registration error:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Registration failed. Please try again.');}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-6\",children:\"College Registration\"}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-100 text-red-700 p-3 rounded mb-4\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,encType:\"multipart/form-data\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"College Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",value:formData.name,onChange:handleChange,className:\"w-full p-2 border rounded\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleChange,className:\"w-full p-2 border rounded\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:formData.password,onChange:handleChange,className:\"w-full p-2 border rounded\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleChange,className:\"w-full p-2 border rounded\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"Address\"}),/*#__PURE__*/_jsx(\"textarea\",{name:\"address\",value:formData.address,onChange:handleChange,className:\"w-full p-2 border rounded\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-gray-700 mb-2\",children:\"Verification Documents\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",name:\"documents\",multiple:true,onChange:handleFileChange,className:\"w-full p-2 border rounded\",required:true}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 mt-1\",children:\"Please upload documents that verify your college's authenticity\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,className:\"w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600\",children:loading?'Registering...':'Register'})]})]});};export default CollegeSignup;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "registerCollege", "jsx", "_jsx", "jsxs", "_jsxs", "CollegeSignup", "navigate", "formData", "setFormData", "name", "email", "password", "confirmPassword", "address", "documents", "error", "setError", "loading", "setLoading", "handleChange", "e", "value", "target", "_objectSpread", "handleFileChange", "files", "Array", "from", "handleSubmit", "preventDefault", "console", "log", "documentsCount", "length", "response", "err", "_err$response", "_err$response$data", "data", "message", "className", "children", "onSubmit", "encType", "type", "onChange", "required", "multiple", "disabled"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/components/auth/CollegeSignup.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { registerCollege } from '../../services/auth';\n\nconst CollegeSignup: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    address: '',\n    documents: [] as File[]\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData({ ...formData, [name]: value });\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      setFormData({ ...formData, documents: Array.from(e.target.files) });\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError('');\n      \n      console.log('Submitting college registration form with data:', {\n        name: formData.name,\n        email: formData.email,\n        address: formData.address,\n        documentsCount: formData.documents.length\n      });\n      \n      const response = await registerCollege({\n        name: formData.name,\n        email: formData.email,\n        password: formData.password,\n        address: formData.address,\n        documents: formData.documents\n      });\n      \n      console.log('Registration successful:', response);\n      navigate('/verification-pending');\n    } catch (err: any) {\n      console.error('Registration error:', err);\n      setError(err.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold mb-6\">College Registration</h2>\n      \n      {error && <div className=\"bg-red-100 text-red-700 p-3 rounded mb-4\">{error}</div>}\n      \n      <form onSubmit={handleSubmit} encType=\"multipart/form-data\">\n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">College Name</label>\n          <input\n            type=\"text\"\n            name=\"name\"\n            value={formData.name}\n            onChange={handleChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n        </div>\n        \n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">Email</label>\n          <input\n            type=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n        </div>\n        \n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">Password</label>\n          <input\n            type=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n        </div>\n        \n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">Confirm Password</label>\n          <input\n            type=\"password\"\n            name=\"confirmPassword\"\n            value={formData.confirmPassword}\n            onChange={handleChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n        </div>\n        \n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">Address</label>\n          <textarea\n            name=\"address\"\n            value={formData.address}\n            onChange={handleChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n        </div>\n        \n        <div className=\"mb-4\">\n          <label className=\"block text-gray-700 mb-2\">Verification Documents</label>\n          <input\n            type=\"file\"\n            name=\"documents\"\n            multiple\n            onChange={handleFileChange}\n            className=\"w-full p-2 border rounded\"\n            required\n          />\n          <p className=\"text-sm text-gray-500 mt-1\">\n            Please upload documents that verify your college's authenticity\n          </p>\n        </div>\n        \n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600\"\n        >\n          {loading ? 'Registering...' : 'Register'}\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default CollegeSignup;"], "mappings": "6HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,eAAe,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CACvCW,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EACb,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAqB,YAAY,CAAIC,CAA4D,EAAK,CACrF,KAAM,CAAEX,IAAI,CAAEY,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCd,WAAW,CAAAe,aAAA,CAAAA,aAAA,IAAMhB,QAAQ,MAAE,CAACE,IAAI,EAAGY,KAAK,EAAE,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAIJ,CAAsC,EAAK,CACnE,GAAIA,CAAC,CAACE,MAAM,CAACG,KAAK,CAAE,CAClBjB,WAAW,CAAAe,aAAA,CAAAA,aAAA,IAAMhB,QAAQ,MAAEO,SAAS,CAAEY,KAAK,CAACC,IAAI,CAACP,CAAC,CAACE,MAAM,CAACG,KAAK,CAAC,EAAE,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAR,CAAkB,EAAK,CACjDA,CAAC,CAACS,cAAc,CAAC,CAAC,CAElB,GAAItB,QAAQ,CAACI,QAAQ,GAAKJ,QAAQ,CAACK,eAAe,CAAE,CAClDI,QAAQ,CAAC,wBAAwB,CAAC,CAClC,OACF,CAEA,GAAI,CACFE,UAAU,CAAC,IAAI,CAAC,CAChBF,QAAQ,CAAC,EAAE,CAAC,CAEZc,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAE,CAC7DtB,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBG,OAAO,CAAEN,QAAQ,CAACM,OAAO,CACzBmB,cAAc,CAAEzB,QAAQ,CAACO,SAAS,CAACmB,MACrC,CAAC,CAAC,CAEF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlC,eAAe,CAAC,CACrCS,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BE,OAAO,CAAEN,QAAQ,CAACM,OAAO,CACzBC,SAAS,CAAEP,QAAQ,CAACO,SACtB,CAAC,CAAC,CAEFgB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEG,QAAQ,CAAC,CACjD5B,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAE,MAAO6B,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBP,OAAO,CAACf,KAAK,CAAC,qBAAqB,CAAEoB,GAAG,CAAC,CACzCnB,QAAQ,CAAC,EAAAoB,aAAA,CAAAD,GAAG,CAACD,QAAQ,UAAAE,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcE,IAAI,UAAAD,kBAAA,iBAAlBA,kBAAA,CAAoBE,OAAO,GAAI,wCAAwC,CAAC,CACnF,CAAC,OAAS,CACRrB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEd,KAAA,QAAKoC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEvC,IAAA,OAAIsC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,CAEhE1B,KAAK,eAAIb,IAAA,QAAKsC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAE1B,KAAK,CAAM,CAAC,cAEjFX,KAAA,SAAMsC,QAAQ,CAAEd,YAAa,CAACe,OAAO,CAAC,qBAAqB,CAAAF,QAAA,eACzDrC,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cAChEvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXnC,IAAI,CAAC,MAAM,CACXY,KAAK,CAAEd,QAAQ,CAACE,IAAK,CACrBoC,QAAQ,CAAE1B,YAAa,CACvBqB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACzDvC,IAAA,UACE0C,IAAI,CAAC,OAAO,CACZnC,IAAI,CAAC,OAAO,CACZY,KAAK,CAAEd,QAAQ,CAACG,KAAM,CACtBmC,QAAQ,CAAE1B,YAAa,CACvBqB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC5DvC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfnC,IAAI,CAAC,UAAU,CACfY,KAAK,CAAEd,QAAQ,CAACI,QAAS,CACzBkC,QAAQ,CAAE1B,YAAa,CACvBqB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACpEvC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfnC,IAAI,CAAC,iBAAiB,CACtBY,KAAK,CAAEd,QAAQ,CAACK,eAAgB,CAChCiC,QAAQ,CAAE1B,YAAa,CACvBqB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC3DvC,IAAA,aACEO,IAAI,CAAC,SAAS,CACdY,KAAK,CAAEd,QAAQ,CAACM,OAAQ,CACxBgC,QAAQ,CAAE1B,YAAa,CACvBqB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,EACC,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,IAAA,UAAOsC,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,wBAAsB,CAAO,CAAC,cAC1EvC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXnC,IAAI,CAAC,WAAW,CAChBsC,QAAQ,MACRF,QAAQ,CAAErB,gBAAiB,CAC3BgB,SAAS,CAAC,2BAA2B,CACrCM,QAAQ,MACT,CAAC,cACF5C,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iEAE1C,CAAG,CAAC,EACD,CAAC,cAENvC,IAAA,WACE0C,IAAI,CAAC,QAAQ,CACbI,QAAQ,CAAE/B,OAAQ,CAClBuB,SAAS,CAAC,8DAA8D,CAAAC,QAAA,CAEvExB,OAAO,CAAG,gBAAgB,CAAG,UAAU,CAClC,CAAC,EACL,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}