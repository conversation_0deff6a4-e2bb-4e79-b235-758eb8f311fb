{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student'\n  });\n  const [error, setError] = useState('');\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Login failed. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleChange,\n              placeholder: \"Enter your email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              placeholder: \"Enter your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"userType\",\n              children: \"Account Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"userType\",\n              name: \"userType\",\n              value: formData.userType,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"college\",\n                children: \"College\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"auth-btn\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-footer\",\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/register\",\n            className: \"auth-link\",\n            children: \"Register here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"PbvND6DChikgLkMFxrNMRMgRKlU=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "login", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "formData", "setFormData", "email", "password", "userType", "error", "setError", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "err", "_err$response", "_err$response$data", "response", "data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\n\nconst Login: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student' as 'student' | 'college'\n  });\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-box\">\n        <div className=\"auth-header\">\n          <h1>Welcome Back</h1>\n          <p>Sign in to your account</p>\n        </div>\n\n        <div className=\"form-container\">\n          {error && <div className=\"error-message\">{error}</div>}\n\n          <form onSubmit={handleSubmit} className=\"auth-form\">\n            <div className=\"form-group\">\n              <label htmlFor=\"email\">Email Address</label>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"password\">Password</label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                placeholder=\"Enter your password\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"userType\">Account Type</label>\n              <select\n                id=\"userType\"\n                name=\"userType\"\n                value={formData.userType}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"student\">Student</option>\n                <option value=\"college\">College</option>\n              </select>\n            </div>\n\n            <button type=\"submit\" className=\"auth-btn\">\n              Sign In\n            </button>\n          </form>\n\n          <div className=\"auth-footer\">\n            Don't have an account? <a href=\"/register\" className=\"auth-link\">Register here</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,YAAY,GAAIC,CAA0D,IAAK;IACnFP,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACQ,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAkB,IAAK;IACjDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBP,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMZ,KAAK,CAACM,QAAQ,CAAC;MACrBD,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBV,QAAQ,CAAC,EAAAS,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBG,OAAO,KAAI,iCAAiC,CAAC;IAC5E;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BzB,OAAA;MAAKwB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBzB,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA;UAAAyB,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB7B,OAAA;UAAAyB,QAAA,EAAG;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEN7B,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC5BhB,KAAK,iBAAIT,OAAA;UAAKwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEhB;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtD7B,OAAA;UAAM8B,QAAQ,EAAEd,YAAa;UAACQ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjDzB,OAAA;YAAKwB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzB,OAAA;cAAO+B,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C7B,OAAA;cACEgC,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVnB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEX,QAAQ,CAACE,KAAM;cACtB4B,QAAQ,EAAEvB,YAAa;cACvBwB,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzB,OAAA;cAAO+B,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C7B,OAAA;cACEgC,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,UAAU;cACbnB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEX,QAAQ,CAACG,QAAS;cACzB2B,QAAQ,EAAEvB,YAAa;cACvBwB,WAAW,EAAC,qBAAqB;cACjCC,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzB,OAAA;cAAO+B,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9C7B,OAAA;cACEiC,EAAE,EAAC,UAAU;cACbnB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEX,QAAQ,CAACI,QAAS;cACzB0B,QAAQ,EAAEvB,YAAa;cACvByB,QAAQ;cAAAX,QAAA,gBAERzB,OAAA;gBAAQe,KAAK,EAAC,SAAS;gBAAAU,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC7B,OAAA;gBAAQe,KAAK,EAAC,SAAS;gBAAAU,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7B,OAAA;YAAQgC,IAAI,EAAC,QAAQ;YAACR,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP7B,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAC,yBACJ,eAAAzB,OAAA;YAAGqC,IAAI,EAAC,WAAW;YAACb,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA5FID,KAAe;EAAA,QACFJ,WAAW;AAAA;AAAAyC,EAAA,GADxBrC,KAAe;AA8FrB,eAAeA,KAAK;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}