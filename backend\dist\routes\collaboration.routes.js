"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const collaboration_controller_1 = require("../controllers/collaboration.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = express_1.default.Router();
// Collaboration routes (protected)
router.post('/request', auth_middleware_1.authMiddleware, collaboration_controller_1.sendCollaborationRequest);
router.get('/requests', auth_middleware_1.authMiddleware, collaboration_controller_1.getCollaborationRequests);
router.put('/requests/:collaborationId/respond', auth_middleware_1.authMiddleware, collaboration_controller_1.respondToCollaborationRequest);
router.get('/analytics', auth_middleware_1.authMiddleware, collaboration_controller_1.getCollaborationAnalytics);
router.get('/partners', auth_middleware_1.authMiddleware, collaboration_controller_1.getPotentialPartners);
exports.default = router;
