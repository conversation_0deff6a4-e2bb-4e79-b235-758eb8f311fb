"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const EventSchema = new mongoose_1.Schema({
    title: { type: String, required: true },
    description: { type: String, required: true },
    eventType: { type: String, required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    registrationDeadline: { type: Date, required: true },
    location: {
        address: { type: String, required: true },
        coordinates: { type: [Number], index: '2dsphere' },
        venue: { type: String, required: true },
        city: { type: String, required: true },
        state: { type: String, required: true },
        pincode: { type: String, required: true }
    },
    organizer: { type: mongoose_1.Schema.Types.ObjectId, ref: 'College', required: true },
    collaborators: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'College' }],
    price: { type: Number, default: 0 },
    isFree: { type: Boolean, default: true },
    maxParticipants: { type: Number },
    currentParticipants: { type: Number, default: 0 },
    registrationLink: { type: String },
    images: [{ type: String }],
    tags: [{ type: String }],
    requirements: [{ type: String }],
    agenda: [{
            time: { type: String },
            activity: { type: String },
            speaker: { type: String }
        }],
    speakers: [{
            name: { type: String },
            designation: { type: String },
            company: { type: String },
            bio: { type: String },
            image: { type: String }
        }],
    sponsors: [{
            name: { type: String },
            logo: { type: String },
            website: { type: String },
            tier: { type: String, enum: ['platinum', 'gold', 'silver', 'bronze'] }
        }],
    status: {
        type: String,
        enum: ['draft', 'published', 'ongoing', 'completed', 'cancelled'],
        default: 'draft'
    },
    isOnline: { type: Boolean, default: false },
    meetingLink: { type: String },
    recordingLink: { type: String },
    certificateTemplate: { type: String },
    feedback: {
        averageRating: { type: Number, default: 0 },
        totalReviews: { type: Number, default: 0 }
    },
    analytics: {
        views: { type: Number, default: 0 },
        registrations: { type: Number, default: 0 },
        attendance: { type: Number, default: 0 },
        completionRate: { type: Number, default: 0 }
    },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});
// Update the updatedAt field before saving
EventSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
// Index for location-based queries
EventSchema.index({ 'location.coordinates': '2dsphere' });
EventSchema.index({ eventType: 1, startDate: 1 });
EventSchema.index({ organizer: 1, status: 1 });
exports.default = mongoose_1.default.model('Event', EventSchema);
