# @babel/plugin-transform-member-expression-literals

> Ensure that reserved words are quoted in property accesses

See our website [@babel/plugin-transform-member-expression-literals](https://babeljs.io/docs/babel-plugin-transform-member-expression-literals) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-member-expression-literals
```

or using yarn:

```sh
yarn add @babel/plugin-transform-member-expression-literals --dev
```
