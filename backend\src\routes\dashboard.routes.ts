import express from 'express';
import {
  getCollegeDashboard,
  getEventPerformanceMetrics,
  getCollegeProfileSummary
} from '../controllers/dashboard.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Dashboard routes (protected)
router.get('/', authMiddleware, getCollegeDashboard);
router.get('/event/:eventId/metrics', authMiddleware, getEventPerformanceMetrics);
router.get('/profile-summary', authMiddleware, getCollegeProfileSummary);

export default router;
