{"name": "stringify-object", "version": "3.3.0", "description": "Stringify an object/array like JSON.stringify just without all the double-quotes", "license": "BSD-2-<PERSON><PERSON>", "repository": "yeoman/stringify-object", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["object", "stringify", "pretty", "print", "dump", "format", "type", "json"], "dependencies": {"get-own-enumerable-property-symbols": "^3.0.0", "is-obj": "^1.0.1", "is-regexp": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}