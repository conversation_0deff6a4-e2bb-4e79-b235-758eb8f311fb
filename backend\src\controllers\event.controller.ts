import { Request, Response } from 'express';
import Event from '../models/Event';
import EventRegistration from '../models/EventRegistration';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configure multer for event image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.cwd(), 'uploads', 'events');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

export const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Create a new event
export const createEvent = async (req: Request, res: Response) => {
  try {
    const {
      title,
      description,
      eventType,
      startDate,
      endDate,
      registrationDeadline,
      location,
      price,
      maxParticipants,
      registrationLink,
      tags,
      requirements,
      agenda,
      speakers,
      sponsors,
      isOnline,
      meetingLink
    } = req.body;

    // @ts-ignore - We'll add this property in the auth middleware
    const organizer = req.college.id;

    // Handle uploaded images
    const images = req.files ?
      (req.files as Express.Multer.File[]).map(file => file.path) :
      [];

    const event = new Event({
      title,
      description,
      eventType,
      startDate,
      endDate,
      registrationDeadline,
      location,
      organizer,
      price: price || 0,
      isFree: !price || price === 0,
      maxParticipants,
      registrationLink,
      images,
      tags: tags ? JSON.parse(tags) : [],
      requirements: requirements ? JSON.parse(requirements) : [],
      agenda: agenda ? JSON.parse(agenda) : [],
      speakers: speakers ? JSON.parse(speakers) : [],
      sponsors: sponsors ? JSON.parse(sponsors) : [],
      isOnline: isOnline === 'true',
      meetingLink,
      status: 'published'
    });

    await event.save();

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      event
    });
  } catch (error) {
    console.error('Create event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all events with filtering and pagination
export const getAllEvents = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 10,
      eventType,
      city,
      startDate,
      endDate,
      isFree,
      search,
      sortBy = 'startDate',
      sortOrder = 'asc'
    } = req.query;

    // Build filter object
    const filter: any = { status: 'published' };

    if (eventType) filter.eventType = eventType;
    if (city) filter['location.city'] = new RegExp(city as string, 'i');
    if (isFree !== undefined) filter.isFree = isFree === 'true';

    if (startDate || endDate) {
      filter.startDate = {};
      if (startDate) filter.startDate.$gte = new Date(startDate as string);
      if (endDate) filter.startDate.$lte = new Date(endDate as string);
    }

    if (search) {
      filter.$or = [
        { title: new RegExp(search as string, 'i') },
        { description: new RegExp(search as string, 'i') },
        { tags: { $in: [new RegExp(search as string, 'i')] } }
      ];
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const skip = (Number(page) - 1) * Number(limit);

    const events = await Event.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(Number(limit))
      .populate('organizer', 'name email')
      .populate('collaborators', 'name');

    const total = await Event.countDocuments(filter);

    res.json({
      success: true,
      events,
      pagination: {
        current: Number(page),
        pages: Math.ceil(total / Number(limit)),
        total,
        limit: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get events error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get event by ID with analytics
export const getEventById = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('organizer', 'name email phone website')
      .populate('collaborators', 'name email');

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Increment view count
    await Event.findByIdAndUpdate(req.params.id, {
      $inc: { 'analytics.views': 1 }
    });

    // Get registration count
    const registrationCount = await EventRegistration.countDocuments({
      event: req.params.id,
      status: { $ne: 'cancelled' }
    });

    // Update current participants
    await Event.findByIdAndUpdate(req.params.id, {
      currentParticipants: registrationCount
    });

    res.json({
      success: true,
      event: {
        ...event.toObject(),
        currentParticipants: registrationCount
      }
    });
  } catch (error) {
    console.error('Get event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update event
export const updateEvent = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // @ts-ignore - We'll add this property in the auth middleware
    if (event.organizer.toString() !== req.college.id) {
      return res.status(401).json({ message: 'Not authorized to update this event' });
    }

    const updatedEvent = await Event.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );

    res.json({
      success: true,
      event: updatedEvent
    });
  } catch (error) {
    console.error('Update event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete event
export const deleteEvent = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // @ts-ignore - We'll add this property in the auth middleware
    if (event.organizer.toString() !== req.college.id) {
      return res.status(401).json({ message: 'Not authorized to delete this event' });
    }

    // Check if event has registrations
    const registrationCount = await EventRegistration.countDocuments({
      event: req.params.id,
      status: { $ne: 'cancelled' }
    });

    if (registrationCount > 0) {
      return res.status(400).json({
        message: 'Cannot delete event with active registrations. Cancel the event instead.'
      });
    }

    await event.deleteOne();

    res.json({
      success: true,
      message: 'Event removed'
    });
  } catch (error) {
    console.error('Delete event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get events organized by college
export const getCollegeEvents = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;
    const { status, page = 1, limit = 10 } = req.query;

    const filter: any = { organizer: collegeId };
    if (status) filter.status = status;

    const skip = (Number(page) - 1) * Number(limit);

    const events = await Event.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit))
      .populate('collaborators', 'name');

    const total = await Event.countDocuments(filter);

    res.json({
      success: true,
      events,
      pagination: {
        current: Number(page),
        pages: Math.ceil(total / Number(limit)),
        total,
        limit: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get college events error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get event analytics for college
export const getEventAnalytics = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    const totalEvents = await Event.countDocuments({ organizer: collegeId });
    const publishedEvents = await Event.countDocuments({
      organizer: collegeId,
      status: 'published'
    });
    const completedEvents = await Event.countDocuments({
      organizer: collegeId,
      status: 'completed'
    });

    // Get total registrations across all events
    const registrationStats = await EventRegistration.aggregate([
      {
        $lookup: {
          from: 'events',
          localField: 'event',
          foreignField: '_id',
          as: 'eventData'
        }
      },
      {
        $match: {
          'eventData.organizer': collegeId
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get events by type
    const eventsByType = await Event.aggregate([
      { $match: { organizer: collegeId } },
      {
        $group: {
          _id: '$eventType',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      analytics: {
        totalEvents,
        publishedEvents,
        completedEvents,
        registrationStats,
        eventsByType
      }
    });
  } catch (error) {
    console.error('Get event analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};