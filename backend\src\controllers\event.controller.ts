import { Request, Response } from 'express';
import Event from '../models/Event';

// Create a new event
export const createEvent = async (req: Request, res: Response) => {
  try {
    const { 
      title, 
      description, 
      eventType, 
      startDate, 
      endDate, 
      location, 
      price, 
      registrationLink,
      images 
    } = req.body;

    // @ts-ignore - We'll add this property in the auth middleware
    const organizer = req.college.id;

    const event = new Event({
      title,
      description,
      eventType,
      startDate,
      endDate,
      location,
      organizer,
      price: price || 0,
      isFree: !price || price === 0,
      registrationLink,
      images: images || []
    });

    await event.save();

    res.status(201).json({
      success: true,
      event
    });
  } catch (error) {
    console.error('Create event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all events
export const getAllEvents = async (req: Request, res: Response) => {
  try {
    const events = await Event.find()
      .sort({ createdAt: -1 })
      .populate('organizer', 'name');

    res.json({
      success: true,
      count: events.length,
      events
    });
  } catch (error) {
    console.error('Get events error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get event by ID
export const getEventById = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('organizer', 'name email')
      .populate('collaborators', 'name email');

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    res.json({
      success: true,
      event
    });
  } catch (error) {
    console.error('Get event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update event
export const updateEvent = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // @ts-ignore - We'll add this property in the auth middleware
    if (event.organizer.toString() !== req.college.id) {
      return res.status(401).json({ message: 'Not authorized to update this event' });
    }

    const updatedEvent = await Event.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );

    res.json({
      success: true,
      event: updatedEvent
    });
  } catch (error) {
    console.error('Update event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete event
export const deleteEvent = async (req: Request, res: Response) => {
  try {
    const event = await Event.findById(req.params.id);

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // @ts-ignore - We'll add this property in the auth middleware
    if (event.organizer.toString() !== req.college.id) {
      return res.status(401).json({ message: 'Not authorized to delete this event' });
    }

    await event.deleteOne();

    res.json({
      success: true,
      message: 'Event removed'
    });
  } catch (error) {
    console.error('Delete event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};