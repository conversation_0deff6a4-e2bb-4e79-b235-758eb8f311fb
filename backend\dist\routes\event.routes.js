"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const event_controller_1 = require("../controllers/event.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = express_1.default.Router();
// Event routes
router.post('/', auth_middleware_1.authMiddleware, event_controller_1.createEvent);
router.get('/', event_controller_1.getAllEvents);
router.get('/:id', event_controller_1.getEventById);
router.put('/:id', auth_middleware_1.authMiddleware, event_controller_1.updateEvent);
router.delete('/:id', auth_middleware_1.authMiddleware, event_controller_1.deleteEvent);
exports.default = router;
