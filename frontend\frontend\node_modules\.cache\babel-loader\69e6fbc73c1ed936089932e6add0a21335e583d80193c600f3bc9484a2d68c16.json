{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"section\",{className:\"hero\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Find and Join College Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Connect with events happening at colleges near you\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"buttons\",children:[/*#__PURE__*/_jsx(Link,{to:\"/events\",className:\"btn primary\",children:\"Browse Events\"}),/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"btn secondary\",children:\"Create Account\"})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Discover Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Find events based on your interests and location\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Connect with Peers\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Meet students with similar interests\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Manage Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create and manage your own events\"})]})]})]});};export default Home;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "children", "className", "to"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home: React.FC = () => {\n  return (\n    <div>\n      <section className=\"hero\">\n        <h2>Find and Join College Events</h2>\n        <p>Connect with events happening at colleges near you</p>\n        <div className=\"buttons\">\n          <Link to=\"/events\" className=\"btn primary\">Browse Events</Link>\n          <Link to=\"/register\" className=\"btn secondary\">Create Account</Link>\n        </div>\n      </section>\n      \n      <section className=\"features\">\n        <div className=\"feature\">\n          <h3>Discover Events</h3>\n          <p>Find events based on your interests and location</p>\n        </div>\n        <div className=\"feature\">\n          <h3>Connect with <PERSON><PERSON></h3>\n          <p>Meet students with similar interests</p>\n        </div>\n        <div className=\"feature\">\n          <h3>Manage Events</h3>\n          <p>Create and manage your own events</p>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,IAAc,CAAGA,CAAA,GAAM,CAC3B,mBACED,KAAA,QAAAE,QAAA,eACEF,KAAA,YAASG,SAAS,CAAC,MAAM,CAAAD,QAAA,eACvBJ,IAAA,OAAAI,QAAA,CAAI,8BAA4B,CAAI,CAAC,cACrCJ,IAAA,MAAAI,QAAA,CAAG,oDAAkD,CAAG,CAAC,cACzDF,KAAA,QAAKG,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBJ,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,SAAS,CAACD,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,cAC/DJ,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,WAAW,CAACD,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,EACjE,CAAC,EACC,CAAC,cAEVF,KAAA,YAASG,SAAS,CAAC,UAAU,CAAAD,QAAA,eAC3BF,KAAA,QAAKG,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBJ,IAAA,OAAAI,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBJ,IAAA,MAAAI,QAAA,CAAG,kDAAgD,CAAG,CAAC,EACpD,CAAC,cACNF,KAAA,QAAKG,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBJ,IAAA,OAAAI,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BJ,IAAA,MAAAI,QAAA,CAAG,sCAAoC,CAAG,CAAC,EACxC,CAAC,cACNF,KAAA,QAAKG,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBJ,IAAA,OAAAI,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBJ,IAAA,MAAAI,QAAA,CAAG,mCAAiC,CAAG,CAAC,EACrC,CAAC,EACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}