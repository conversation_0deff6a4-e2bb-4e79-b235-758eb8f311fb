{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:5000/api/auth';\n// Create axios instance with error handling\nconst api = axios.create({\n  baseURL: API_URL\n});\n\n// Add request interceptor for debugging\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log('API Request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url, config.data);\n  return config;\n}, error => {\n  console.error('Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Add response interceptor for debugging\napi.interceptors.response.use(response => {\n  console.log('API Response:', response.status, response.data);\n  return response;\n}, error => {\n  var _error$response, _error$response2;\n  console.error('Response Error:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n  return Promise.reject(error);\n});\nexport const login = async data => {\n  try {\n    const response = await api.post('/login', data);\n    if (response.data.token) {\n      localStorage.setItem('token', response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data.user));\n    }\n    return response.data;\n  } catch (error) {\n    console.error('Login error:', error);\n    throw error;\n  }\n};\nexport const registerStudent = async data => {\n  try {\n    const response = await api.post('/register/student', data);\n    return response.data;\n  } catch (error) {\n    console.error('Student registration error:', error);\n    throw error;\n  }\n};\nexport const registerCollege = async data => {\n  try {\n    const formData = new FormData();\n\n    // Add text fields\n    formData.append('name', data.name);\n    formData.append('email', data.email);\n    formData.append('password', data.password);\n    formData.append('address', data.address);\n\n    // Add documents if any\n    if (data.documents && data.documents.length > 0) {\n      for (let i = 0; i < data.documents.length; i++) {\n        formData.append('documents', data.documents[i]);\n      }\n    }\n\n    // Log form data for debugging\n    console.log('Form data entries:');\n    for (const pair of formData.entries()) {\n      console.log(pair[0], pair[1]);\n    }\n    const response = await api.post('/register/college', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('College registration error:', error);\n    throw error;\n  }\n};\nexport const logout = () => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\nexport const getCurrentUser = () => {\n  const userStr = localStorage.getItem('user');\n  if (userStr) return JSON.parse(userStr);\n  return null;\n};", "map": {"version": 3, "names": ["axios", "API_URL", "api", "create", "baseURL", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "data", "error", "Promise", "reject", "response", "status", "_error$response", "_error$response2", "message", "login", "post", "token", "localStorage", "setItem", "JSON", "stringify", "user", "registerStudent", "registerCollege", "formData", "FormData", "append", "name", "email", "password", "address", "documents", "length", "i", "pair", "entries", "headers", "logout", "removeItem", "getCurrentUser", "userStr", "getItem", "parse"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/services/auth.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:5000/api/auth';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  userType: 'student' | 'college';\n}\n\ninterface RegisterStudentData {\n  name: string;\n  email: string;\n  password: string;\n  age: number;\n  collegeName: string;\n  class: string;\n  adharId: string;\n  studentCardId: string;\n}\n\ninterface RegisterCollegeData {\n  name: string;\n  email: string;\n  password: string;\n  address: string;\n  documents?: File[];\n}\n\n// Create axios instance with error handling\nconst api = axios.create({\n  baseURL: API_URL,\n});\n\n// Add request interceptor for debugging\napi.interceptors.request.use(\n  (config) => {\n    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);\n    return config;\n  },\n  (error) => {\n    console.error('Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for debugging\napi.interceptors.response.use(\n  (response) => {\n    console.log('API Response:', response.status, response.data);\n    return response;\n  },\n  (error) => {\n    console.error('Response Error:', error.response?.status, error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\nexport const login = async (data: LoginData) => {\n  try {\n    const response = await api.post('/login', data);\n    \n    if (response.data.token) {\n      localStorage.setItem('token', response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data.user));\n    }\n    \n    return response.data;\n  } catch (error) {\n    console.error('Login error:', error);\n    throw error;\n  }\n};\n\nexport const registerStudent = async (data: RegisterStudentData) => {\n  try {\n    const response = await api.post('/register/student', data);\n    return response.data;\n  } catch (error) {\n    console.error('Student registration error:', error);\n    throw error;\n  }\n};\n\nexport const registerCollege = async (data: RegisterCollegeData) => {\n  try {\n    const formData = new FormData();\n    \n    // Add text fields\n    formData.append('name', data.name);\n    formData.append('email', data.email);\n    formData.append('password', data.password);\n    formData.append('address', data.address);\n    \n    // Add documents if any\n    if (data.documents && data.documents.length > 0) {\n      for (let i = 0; i < data.documents.length; i++) {\n        formData.append('documents', data.documents[i]);\n      }\n    }\n    \n    // Log form data for debugging\n    console.log('Form data entries:');\n    for (const pair of formData.entries()) {\n      console.log(pair[0], pair[1]);\n    }\n    \n    const response = await api.post('/register/college', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    \n    return response.data;\n  } catch (error) {\n    console.error('College registration error:', error);\n    throw error;\n  }\n};\n\nexport const logout = () => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\n\nexport const getCurrentUser = () => {\n  const userStr = localStorage.getItem('user');\n  if (userStr) return JSON.parse(userStr);\n  return null;\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,gCAAgC;AA2BhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH;AACX,CAAC,CAAC;;AAEF;AACAC,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,cAAc,GAAAF,cAAA,GAAED,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,EAAEN,MAAM,CAACO,IAAI,CAAC;EAClF,OAAOP,MAAM;AACf,CAAC,EACAQ,KAAK,IAAK;EACTN,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,GAAG,CAACG,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC1BY,QAAQ,IAAK;EACZT,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEQ,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACJ,IAAI,CAAC;EAC5D,OAAOI,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,gBAAA;EACTZ,OAAO,CAACM,KAAK,CAAC,iBAAiB,GAAAK,eAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,EAAE,EAAAE,gBAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBP,IAAI,KAAIC,KAAK,CAACO,OAAO,CAAC;EAC/F,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMQ,KAAK,GAAG,MAAOT,IAAe,IAAK;EAC9C,IAAI;IACF,MAAMI,QAAQ,GAAG,MAAMjB,GAAG,CAACuB,IAAI,CAAC,QAAQ,EAAEV,IAAI,CAAC;IAE/C,IAAII,QAAQ,CAACJ,IAAI,CAACW,KAAK,EAAE;MACvBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAET,QAAQ,CAACJ,IAAI,CAACW,KAAK,CAAC;MAClDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAACJ,IAAI,CAACgB,IAAI,CAAC,CAAC;IAClE;IAEA,OAAOZ,QAAQ,CAACJ,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgB,eAAe,GAAG,MAAOjB,IAAyB,IAAK;EAClE,IAAI;IACF,MAAMI,QAAQ,GAAG,MAAMjB,GAAG,CAACuB,IAAI,CAAC,mBAAmB,EAAEV,IAAI,CAAC;IAC1D,OAAOI,QAAQ,CAACJ,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMiB,eAAe,GAAG,MAAOlB,IAAyB,IAAK;EAClE,IAAI;IACF,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACAD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErB,IAAI,CAACsB,IAAI,CAAC;IAClCH,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAErB,IAAI,CAACuB,KAAK,CAAC;IACpCJ,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAErB,IAAI,CAACwB,QAAQ,CAAC;IAC1CL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAErB,IAAI,CAACyB,OAAO,CAAC;;IAExC;IACA,IAAIzB,IAAI,CAAC0B,SAAS,IAAI1B,IAAI,CAAC0B,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,IAAI,CAAC0B,SAAS,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC9CT,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAErB,IAAI,CAAC0B,SAAS,CAACE,CAAC,CAAC,CAAC;MACjD;IACF;;IAEA;IACAjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,KAAK,MAAMiC,IAAI,IAAIV,QAAQ,CAACW,OAAO,CAAC,CAAC,EAAE;MACrCnC,OAAO,CAACC,GAAG,CAACiC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/B;IAEA,MAAMzB,QAAQ,GAAG,MAAMjB,GAAG,CAACuB,IAAI,CAAC,mBAAmB,EAAES,QAAQ,EAAE;MAC7DY,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,OAAO3B,QAAQ,CAACJ,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM+B,MAAM,GAAGA,CAAA,KAAM;EAC1BpB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC;EAChCrB,YAAY,CAACqB,UAAU,CAAC,MAAM,CAAC;AACjC,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAMC,OAAO,GAAGvB,YAAY,CAACwB,OAAO,CAAC,MAAM,CAAC;EAC5C,IAAID,OAAO,EAAE,OAAOrB,IAAI,CAACuB,KAAK,CAACF,OAAO,CAAC;EACvC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}