{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\Home.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Find and Join College Events\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Connect with events happening at colleges near you\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/events\",\n          className: \"btn primary\",\n          children: \"Browse Events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"btn secondary\",\n          children: \"Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"features\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Discover Events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Find events based on your interests and location\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Connect with Peers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Meet students with similar interests\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Manage Events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Create and manage your own events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Home", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home: React.FC = () => {\n  return (\n    <div>\n      <section className=\"hero\">\n        <h2>Find and Join College Events</h2>\n        <p>Connect with events happening at colleges near you</p>\n        <div className=\"buttons\">\n          <Link to=\"/events\" className=\"btn primary\">Browse Events</Link>\n          <Link to=\"/register\" className=\"btn secondary\">Create Account</Link>\n        </div>\n      </section>\n      \n      <section className=\"features\">\n        <div className=\"feature\">\n          <h3>Discover Events</h3>\n          <p>Find events based on your interests and location</p>\n        </div>\n        <div className=\"feature\">\n          <h3>Connect with <PERSON><PERSON></h3>\n          <p>Meet students with similar interests</p>\n        </div>\n        <div className=\"feature\">\n          <h3>Manage Events</h3>\n          <p>Create and manage your own events</p>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA;MAASG,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACvBF,OAAA;QAAAE,QAAA,EAAI;MAA4B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCP,OAAA;QAAAE,QAAA,EAAG;MAAkD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzDP,OAAA;QAAKG,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtBF,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,SAAS;UAACL,SAAS,EAAC,aAAa;UAAAD,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/DP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,WAAW;UAACL,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVP,OAAA;MAASG,SAAS,EAAC,UAAU;MAAAD,QAAA,gBAC3BF,OAAA;QAAKG,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtBF,OAAA;UAAAE,QAAA,EAAI;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBP,OAAA;UAAAE,QAAA,EAAG;QAAgD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACNP,OAAA;QAAKG,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtBF,OAAA;UAAAE,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BP,OAAA;UAAAE,QAAA,EAAG;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACNP,OAAA;QAAKG,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtBF,OAAA;UAAAE,QAAA,EAAI;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBP,OAAA;UAAAE,QAAA,EAAG;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACE,EAAA,GA5BIR,IAAc;AA8BpB,eAAeA,IAAI;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}