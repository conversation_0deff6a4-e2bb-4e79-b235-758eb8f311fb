import React from 'react';
import { Link } from 'react-router-dom';

const VerificationPending: React.FC = () => {
  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      <h2 className="text-2xl font-bold mb-4">Registration Successful!</h2>
      <div className="mb-6">
        <p className="mb-4">
          Thank you for registering your college. Your account is pending verification.
        </p>
        <p className="mb-4">
          Our team will review your documents and verify your college account. 
          This process usually takes 1-2 business days.
        </p>
        <p>
          You will receive an email notification once your account is verified.
        </p>
      </div>
      <Link to="/login" className="inline-block bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
        Go to Login
      </Link>
    </div>
  );
};

export default VerificationPending;
