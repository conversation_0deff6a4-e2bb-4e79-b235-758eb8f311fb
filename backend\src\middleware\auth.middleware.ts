import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import College from '../models/College';

// Extend the Request interface to include college property
declare global {
  namespace Express {
    interface Request {
      college?: any;
    }
  }
}

export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get token from header
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret') as { id: string };
    
    // Find college by id
    const college = await College.findById(decoded.id).select('-password');
    
    if (!college) {
      return res.status(401).json({ message: 'Token is not valid' });
    }

    // Add college to request object
    req.college = college;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ message: 'Token is not valid' });
  }
};