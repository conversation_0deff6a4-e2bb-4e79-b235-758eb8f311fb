import mongoose, { Document, Schema } from 'mongoose';

export interface ICollege extends Document {
  name: string;
  email: string;
  password: string;
  address: string;
  verificationDocuments: string[];
  isVerified: boolean;
  collegeId: string;
  phone: string;
  website?: string;
  description?: string;
  logo?: string;
  establishedYear?: number;
  totalStudents?: number;
  departments: string[];
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  notificationSettings: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    eventReminders: boolean;
  };
  collaborationRequests: {
    sent: mongoose.Types.ObjectId[];
    received: mongoose.Types.ObjectId[];
  };
  createdAt: Date;
  updatedAt: Date;
}

const CollegeSchema: Schema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  address: { type: String, required: true },
  verificationDocuments: [{ type: String }],
  isVerified: { type: Boolean, default: false },
  collegeId: { type: String, required: true, unique: true },
  phone: { type: String, required: true },
  website: { type: String },
  description: { type: String },
  logo: { type: String },
  establishedYear: { type: Number },
  totalStudents: { type: Number },
  departments: [{ type: String }],
  socialMedia: {
    facebook: { type: String },
    twitter: { type: String },
    instagram: { type: String },
    linkedin: { type: String }
  },
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  notificationSettings: {
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
    eventReminders: { type: Boolean, default: true }
  },
  collaborationRequests: {
    sent: [{ type: Schema.Types.ObjectId, ref: 'College' }],
    received: [{ type: Schema.Types.ObjectId, ref: 'College' }]
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field before saving
CollegeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.model<ICollege>('College', CollegeSchema);
