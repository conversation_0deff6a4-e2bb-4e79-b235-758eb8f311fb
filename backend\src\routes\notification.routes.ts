import express from 'express';
import {
  createNotification,
  getCollegeNotifications,
  notifyEventParticipants,
  getNotificationAnalytics,
  markNotificationAsRead,
  trackNotificationClick
} from '../controllers/notification.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Notification routes (protected)
router.post('/', authMiddleware, createNotification);
router.get('/', authMiddleware, getCollegeNotifications);
router.post('/event/:eventId', authMiddleware, notifyEventParticipants);
router.get('/analytics', authMiddleware, getNotificationAnalytics);
router.put('/:notificationId/read', markNotificationAsRead);
router.put('/:notificationId/click', trackNotificationClick);

export default router;
