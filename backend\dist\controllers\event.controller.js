"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteEvent = exports.updateEvent = exports.getEventById = exports.getAllEvents = exports.createEvent = void 0;
const Event_1 = __importDefault(require("../models/Event"));
// Create a new event
const createEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { title, description, eventType, startDate, endDate, location, price, registrationLink, images } = req.body;
        // @ts-ignore - We'll add this property in the auth middleware
        const organizer = req.college.id;
        const event = new Event_1.default({
            title,
            description,
            eventType,
            startDate,
            endDate,
            location,
            organizer,
            price: price || 0,
            isFree: !price || price === 0,
            registrationLink,
            images: images || []
        });
        yield event.save();
        res.status(201).json({
            success: true,
            event
        });
    }
    catch (error) {
        console.error('Create event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.createEvent = createEvent;
// Get all events
const getAllEvents = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const events = yield Event_1.default.find()
            .sort({ createdAt: -1 })
            .populate('organizer', 'name');
        res.json({
            success: true,
            count: events.length,
            events
        });
    }
    catch (error) {
        console.error('Get events error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getAllEvents = getAllEvents;
// Get event by ID
const getEventById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id)
            .populate('organizer', 'name email')
            .populate('collaborators', 'name email');
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        res.json({
            success: true,
            event
        });
    }
    catch (error) {
        console.error('Get event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getEventById = getEventById;
// Update event
const updateEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id);
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        // @ts-ignore - We'll add this property in the auth middleware
        if (event.organizer.toString() !== req.college.id) {
            return res.status(401).json({ message: 'Not authorized to update this event' });
        }
        const updatedEvent = yield Event_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        res.json({
            success: true,
            event: updatedEvent
        });
    }
    catch (error) {
        console.error('Update event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.updateEvent = updateEvent;
// Delete event
const deleteEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id);
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        // @ts-ignore - We'll add this property in the auth middleware
        if (event.organizer.toString() !== req.college.id) {
            return res.status(401).json({ message: 'Not authorized to delete this event' });
        }
        yield event.deleteOne();
        res.json({
            success: true,
            message: 'Event removed'
        });
    }
    catch (error) {
        console.error('Delete event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.deleteEvent = deleteEvent;
