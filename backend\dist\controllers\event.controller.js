"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEventAnalytics = exports.getCollegeEvents = exports.deleteEvent = exports.updateEvent = exports.getEventById = exports.getAllEvents = exports.createEvent = exports.upload = void 0;
const Event_1 = __importDefault(require("../models/Event"));
const EventRegistration_1 = __importDefault(require("../models/EventRegistration"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Configure multer for event image uploads
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadPath = path_1.default.join(process.cwd(), 'uploads', 'events');
        if (!fs_1.default.existsSync(uploadPath)) {
            fs_1.default.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        cb(null, `${Date.now()}-${file.originalname}`);
    }
});
exports.upload = (0, multer_1.default)({
    storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        }
        else {
            cb(new Error('Only image files are allowed'));
        }
    },
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});
// Create a new event
const createEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { title, description, eventType, startDate, endDate, registrationDeadline, location, price, maxParticipants, registrationLink, tags, requirements, agenda, speakers, sponsors, isOnline, meetingLink } = req.body;
        // @ts-ignore - We'll add this property in the auth middleware
        const organizer = req.college.id;
        // Handle uploaded images
        const images = req.files ?
            req.files.map(file => file.path) :
            [];
        const event = new Event_1.default({
            title,
            description,
            eventType,
            startDate,
            endDate,
            registrationDeadline,
            location,
            organizer,
            price: price || 0,
            isFree: !price || price === 0,
            maxParticipants,
            registrationLink,
            images,
            tags: tags ? JSON.parse(tags) : [],
            requirements: requirements ? JSON.parse(requirements) : [],
            agenda: agenda ? JSON.parse(agenda) : [],
            speakers: speakers ? JSON.parse(speakers) : [],
            sponsors: sponsors ? JSON.parse(sponsors) : [],
            isOnline: isOnline === 'true',
            meetingLink,
            status: 'published'
        });
        yield event.save();
        res.status(201).json({
            success: true,
            message: 'Event created successfully',
            event
        });
    }
    catch (error) {
        console.error('Create event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.createEvent = createEvent;
// Get all events with filtering and pagination
const getAllEvents = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, eventType, city, startDate, endDate, isFree, search, sortBy = 'startDate', sortOrder = 'asc' } = req.query;
        // Build filter object
        const filter = { status: 'published' };
        if (eventType)
            filter.eventType = eventType;
        if (city)
            filter['location.city'] = new RegExp(city, 'i');
        if (isFree !== undefined)
            filter.isFree = isFree === 'true';
        if (startDate || endDate) {
            filter.startDate = {};
            if (startDate)
                filter.startDate.$gte = new Date(startDate);
            if (endDate)
                filter.startDate.$lte = new Date(endDate);
        }
        if (search) {
            filter.$or = [
                { title: new RegExp(search, 'i') },
                { description: new RegExp(search, 'i') },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const skip = (Number(page) - 1) * Number(limit);
        const events = yield Event_1.default.find(filter)
            .sort(sort)
            .skip(skip)
            .limit(Number(limit))
            .populate('organizer', 'name email')
            .populate('collaborators', 'name');
        const total = yield Event_1.default.countDocuments(filter);
        res.json({
            success: true,
            events,
            pagination: {
                current: Number(page),
                pages: Math.ceil(total / Number(limit)),
                total,
                limit: Number(limit)
            }
        });
    }
    catch (error) {
        console.error('Get events error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getAllEvents = getAllEvents;
// Get event by ID with analytics
const getEventById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id)
            .populate('organizer', 'name email phone website')
            .populate('collaborators', 'name email');
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        // Increment view count
        yield Event_1.default.findByIdAndUpdate(req.params.id, {
            $inc: { 'analytics.views': 1 }
        });
        // Get registration count
        const registrationCount = yield EventRegistration_1.default.countDocuments({
            event: req.params.id,
            status: { $ne: 'cancelled' }
        });
        // Update current participants
        yield Event_1.default.findByIdAndUpdate(req.params.id, {
            currentParticipants: registrationCount
        });
        res.json({
            success: true,
            event: Object.assign(Object.assign({}, event.toObject()), { currentParticipants: registrationCount })
        });
    }
    catch (error) {
        console.error('Get event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getEventById = getEventById;
// Update event
const updateEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id);
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        // @ts-ignore - We'll add this property in the auth middleware
        if (event.organizer.toString() !== req.college.id) {
            return res.status(401).json({ message: 'Not authorized to update this event' });
        }
        const updatedEvent = yield Event_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        res.json({
            success: true,
            event: updatedEvent
        });
    }
    catch (error) {
        console.error('Update event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.updateEvent = updateEvent;
// Delete event
const deleteEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const event = yield Event_1.default.findById(req.params.id);
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        // @ts-ignore - We'll add this property in the auth middleware
        if (event.organizer.toString() !== req.college.id) {
            return res.status(401).json({ message: 'Not authorized to delete this event' });
        }
        // Check if event has registrations
        const registrationCount = yield EventRegistration_1.default.countDocuments({
            event: req.params.id,
            status: { $ne: 'cancelled' }
        });
        if (registrationCount > 0) {
            return res.status(400).json({
                message: 'Cannot delete event with active registrations. Cancel the event instead.'
            });
        }
        yield event.deleteOne();
        res.json({
            success: true,
            message: 'Event removed'
        });
    }
    catch (error) {
        console.error('Delete event error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.deleteEvent = deleteEvent;
// Get events organized by college
const getCollegeEvents = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const { status, page = 1, limit = 10 } = req.query;
        const filter = { organizer: collegeId };
        if (status)
            filter.status = status;
        const skip = (Number(page) - 1) * Number(limit);
        const events = yield Event_1.default.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(Number(limit))
            .populate('collaborators', 'name');
        const total = yield Event_1.default.countDocuments(filter);
        res.json({
            success: true,
            events,
            pagination: {
                current: Number(page),
                pages: Math.ceil(total / Number(limit)),
                total,
                limit: Number(limit)
            }
        });
    }
    catch (error) {
        console.error('Get college events error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollegeEvents = getCollegeEvents;
// Get event analytics for college
const getEventAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const totalEvents = yield Event_1.default.countDocuments({ organizer: collegeId });
        const publishedEvents = yield Event_1.default.countDocuments({
            organizer: collegeId,
            status: 'published'
        });
        const completedEvents = yield Event_1.default.countDocuments({
            organizer: collegeId,
            status: 'completed'
        });
        // Get total registrations across all events
        const registrationStats = yield EventRegistration_1.default.aggregate([
            {
                $lookup: {
                    from: 'events',
                    localField: 'event',
                    foreignField: '_id',
                    as: 'eventData'
                }
            },
            {
                $match: {
                    'eventData.organizer': collegeId
                }
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get events by type
        const eventsByType = yield Event_1.default.aggregate([
            { $match: { organizer: collegeId } },
            {
                $group: {
                    _id: '$eventType',
                    count: { $sum: 1 }
                }
            }
        ]);
        res.json({
            success: true,
            analytics: {
                totalEvents,
                publishedEvents,
                completedEvents,
                registrationStats,
                eventsByType
            }
        });
    }
    catch (error) {
        console.error('Get event analytics error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getEventAnalytics = getEventAnalytics;
