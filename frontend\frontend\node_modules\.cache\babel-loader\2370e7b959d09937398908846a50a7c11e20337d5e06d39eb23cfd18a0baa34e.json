{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/workuuu/frontend/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{registerStudent}from'../../services/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StudentSignup=()=>{const navigate=useNavigate();const[formData,setFormData]=useState({name:'',email:'',password:'',confirmPassword:'',age:'',collegeName:'',class:'',adharId:'',studentCardId:''});const[error,setError]=useState('');const[loading,setLoading]=useState(false);const handleChange=e=>{const{name,value}=e.target;setFormData(_objectSpread(_objectSpread({},formData),{},{[name]:value}));};const handleSubmit=async e=>{e.preventDefault();if(formData.password!==formData.confirmPassword){setError('Passwords do not match');return;}try{setLoading(true);setError('');await registerStudent({name:formData.name,email:formData.email,password:formData.password,age:parseInt(formData.age),collegeName:formData.collegeName,class:formData.class,adharId:formData.adharId,studentCardId:formData.studentCardId});navigate('/login');}catch(err){var _err$response,_err$response$data;console.error('Registration error:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Registration failed. Please try again.');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"auth-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Student Registration\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create your student account\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"auth-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"name\",value:formData.name,onChange:handleChange,placeholder:\"Enter your full name\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleChange,placeholder:\"Enter your email\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:formData.password,onChange:handleChange,placeholder:\"Create a password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleChange,placeholder:\"Confirm your password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Age\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",name:\"age\",value:formData.age,onChange:handleChange,placeholder:\"Enter your age\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"College Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"collegeName\",value:formData.collegeName,onChange:handleChange,placeholder:\"Enter your college name\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Class/Year\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"class\",value:formData.class,onChange:handleChange,placeholder:\"e.g., 2nd Year, Final Year\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Aadhar ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"adharId\",value:formData.adharId,onChange:handleChange,placeholder:\"Enter your Aadhar number\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Student Card ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"studentCardId\",value:formData.studentCardId,onChange:handleChange,placeholder:\"Enter your student ID\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:loading,className:\"auth-btn\",children:loading?'Registering...':'Register as Student'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"auth-footer\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(\"a\",{href:\"/login\",className:\"auth-link\",children:\"Login here\"})]})]})});};export default StudentSignup;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "registerStudent", "jsx", "_jsx", "jsxs", "_jsxs", "StudentSignup", "navigate", "formData", "setFormData", "name", "email", "password", "confirmPassword", "age", "collegeName", "class", "<PERSON>har<PERSON>d", "studentCardId", "error", "setError", "loading", "setLoading", "handleChange", "e", "value", "target", "_objectSpread", "handleSubmit", "preventDefault", "parseInt", "err", "_err$response", "_err$response$data", "console", "response", "data", "message", "className", "children", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "href"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/components/auth/StudentSignup.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { registerStudent } from '../../services/auth';\n\nconst StudentSignup: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    age: '',\n    collegeName: '',\n    class: '',\n    adharId: '',\n    studentCardId: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData({ ...formData, [name]: value });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError('');\n      \n      await registerStudent({\n        name: formData.name,\n        email: formData.email,\n        password: formData.password,\n        age: parseInt(formData.age),\n        collegeName: formData.collegeName,\n        class: formData.class,\n        adharId: formData.adharId,\n        studentCardId: formData.studentCardId\n      });\n      \n      navigate('/login');\n    } catch (err: any) {\n      console.error('Registration error:', err);\n      setError(err.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-box\">\n        <div className=\"auth-header\">\n          <h2>Student Registration</h2>\n          <p>Create your student account</p>\n        </div>\n\n        {error && <div className=\"error-message\">{error}</div>}\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label>Full Name</label>\n            <input\n              type=\"text\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              placeholder=\"Enter your full name\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Email Address</label>\n            <input\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              placeholder=\"Enter your email\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Password</label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              placeholder=\"Create a password\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Confirm Password</label>\n            <input\n              type=\"password\"\n              name=\"confirmPassword\"\n              value={formData.confirmPassword}\n              onChange={handleChange}\n              placeholder=\"Confirm your password\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Age</label>\n            <input\n              type=\"number\"\n              name=\"age\"\n              value={formData.age}\n              onChange={handleChange}\n              placeholder=\"Enter your age\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>College Name</label>\n            <input\n              type=\"text\"\n              name=\"collegeName\"\n              value={formData.collegeName}\n              onChange={handleChange}\n              placeholder=\"Enter your college name\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Class/Year</label>\n            <input\n              type=\"text\"\n              name=\"class\"\n              value={formData.class}\n              onChange={handleChange}\n              placeholder=\"e.g., 2nd Year, Final Year\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Aadhar ID</label>\n            <input\n              type=\"text\"\n              name=\"adharId\"\n              value={formData.adharId}\n              onChange={handleChange}\n              placeholder=\"Enter your Aadhar number\"\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label>Student Card ID</label>\n            <input\n              type=\"text\"\n              name=\"studentCardId\"\n              value={formData.studentCardId}\n              onChange={handleChange}\n              placeholder=\"Enter your student ID\"\n              required\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"auth-btn\"\n          >\n            {loading ? 'Registering...' : 'Register as Student'}\n          </button>\n        </form>\n\n        <div className=\"auth-footer\">\n          Already have an account? <a href=\"/login\" className=\"auth-link\">Login here</a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentSignup;"], "mappings": "6HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,eAAe,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CACvCW,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,GAAG,CAAE,EAAE,CACPC,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,aAAa,CAAE,EACjB,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAwB,YAAY,CAAIC,CAAsC,EAAK,CAC/D,KAAM,CAAEd,IAAI,CAAEe,KAAM,CAAC,CAAGD,CAAC,CAACE,MAAM,CAChCjB,WAAW,CAAAkB,aAAA,CAAAA,aAAA,IAAMnB,QAAQ,MAAE,CAACE,IAAI,EAAGe,KAAK,EAAE,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAJ,CAAkB,EAAK,CACjDA,CAAC,CAACK,cAAc,CAAC,CAAC,CAElB,GAAIrB,QAAQ,CAACI,QAAQ,GAAKJ,QAAQ,CAACK,eAAe,CAAE,CAClDO,QAAQ,CAAC,wBAAwB,CAAC,CAClC,OACF,CAEA,GAAI,CACFE,UAAU,CAAC,IAAI,CAAC,CAChBF,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAnB,eAAe,CAAC,CACpBS,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BE,GAAG,CAAEgB,QAAQ,CAACtB,QAAQ,CAACM,GAAG,CAAC,CAC3BC,WAAW,CAAEP,QAAQ,CAACO,WAAW,CACjCC,KAAK,CAAER,QAAQ,CAACQ,KAAK,CACrBC,OAAO,CAAET,QAAQ,CAACS,OAAO,CACzBC,aAAa,CAAEV,QAAQ,CAACU,aAC1B,CAAC,CAAC,CAEFX,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAE,MAAOwB,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBC,OAAO,CAACf,KAAK,CAAC,qBAAqB,CAAEY,GAAG,CAAC,CACzCX,QAAQ,CAAC,EAAAY,aAAA,CAAAD,GAAG,CAACI,QAAQ,UAAAH,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcI,IAAI,UAAAH,kBAAA,iBAAlBA,kBAAA,CAAoBI,OAAO,GAAI,wCAAwC,CAAC,CACnF,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEnB,IAAA,QAAKmC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BlC,KAAA,QAAKiC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,OAAAoC,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BpC,IAAA,MAAAoC,QAAA,CAAG,6BAA2B,CAAG,CAAC,EAC/B,CAAC,CAELpB,KAAK,eAAIhB,IAAA,QAAKmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEpB,KAAK,CAAM,CAAC,cAEtDd,KAAA,SAAMmC,QAAQ,CAAEZ,YAAa,CAACU,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDlC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,WAAS,CAAO,CAAC,cACxBpC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,IAAI,CAAC,MAAM,CACXe,KAAK,CAAEjB,QAAQ,CAACE,IAAK,CACrBgC,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,sBAAsB,CAClCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,eAAa,CAAO,CAAC,cAC5BpC,IAAA,UACEsC,IAAI,CAAC,OAAO,CACZ/B,IAAI,CAAC,OAAO,CACZe,KAAK,CAAEjB,QAAQ,CAACG,KAAM,CACtB+B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,UAAQ,CAAO,CAAC,cACvBpC,IAAA,UACEsC,IAAI,CAAC,UAAU,CACf/B,IAAI,CAAC,UAAU,CACfe,KAAK,CAAEjB,QAAQ,CAACI,QAAS,CACzB8B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,kBAAgB,CAAO,CAAC,cAC/BpC,IAAA,UACEsC,IAAI,CAAC,UAAU,CACf/B,IAAI,CAAC,iBAAiB,CACtBe,KAAK,CAAEjB,QAAQ,CAACK,eAAgB,CAChC6B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,uBAAuB,CACnCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,KAAG,CAAO,CAAC,cAClBpC,IAAA,UACEsC,IAAI,CAAC,QAAQ,CACb/B,IAAI,CAAC,KAAK,CACVe,KAAK,CAAEjB,QAAQ,CAACM,GAAI,CACpB4B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,gBAAgB,CAC5BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,cAAY,CAAO,CAAC,cAC3BpC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,IAAI,CAAC,aAAa,CAClBe,KAAK,CAAEjB,QAAQ,CAACO,WAAY,CAC5B2B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,yBAAyB,CACrCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,YAAU,CAAO,CAAC,cACzBpC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,IAAI,CAAC,OAAO,CACZe,KAAK,CAAEjB,QAAQ,CAACQ,KAAM,CACtB0B,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,4BAA4B,CACxCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,WAAS,CAAO,CAAC,cACxBpC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,IAAI,CAAC,SAAS,CACde,KAAK,CAAEjB,QAAQ,CAACS,OAAQ,CACxByB,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,0BAA0B,CACtCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENvC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,UAAAoC,QAAA,CAAO,iBAAe,CAAO,CAAC,cAC9BpC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,IAAI,CAAC,eAAe,CACpBe,KAAK,CAAEjB,QAAQ,CAACU,aAAc,CAC9BwB,QAAQ,CAAEnB,YAAa,CACvBoB,WAAW,CAAC,uBAAuB,CACnCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENzC,IAAA,WACEsC,IAAI,CAAC,QAAQ,CACbI,QAAQ,CAAExB,OAAQ,CAClBiB,SAAS,CAAC,UAAU,CAAAC,QAAA,CAEnBlB,OAAO,CAAG,gBAAgB,CAAG,qBAAqB,CAC7C,CAAC,EACL,CAAC,cAEPhB,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,2BACF,cAAApC,IAAA,MAAG2C,IAAI,CAAC,QAAQ,CAACR,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,EAC3E,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}