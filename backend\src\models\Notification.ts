import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  title: string;
  message: string;
  type: 'event_reminder' | 'event_update' | 'collaboration_request' | 'general';
  recipients: {
    students: mongoose.Types.ObjectId[];
    colleges: mongoose.Types.ObjectId[];
  };
  sender: mongoose.Types.ObjectId;
  relatedEvent?: mongoose.Types.ObjectId;
  status: 'draft' | 'sent' | 'scheduled';
  scheduledFor?: Date;
  sentAt?: Date;
  readBy: {
    user: mongoose.Types.ObjectId;
    readAt: Date;
  }[];
  clickCount: number;
  createdAt: Date;
}

const NotificationSchema: Schema = new Schema({
  title: { type: String, required: true },
  message: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['event_reminder', 'event_update', 'collaboration_request', 'general'],
    required: true 
  },
  recipients: {
    students: [{ type: Schema.Types.ObjectId, ref: 'Student' }],
    colleges: [{ type: Schema.Types.ObjectId, ref: 'College' }]
  },
  sender: { type: Schema.Types.ObjectId, ref: 'College', required: true },
  relatedEvent: { type: Schema.Types.ObjectId, ref: 'Event' },
  status: { 
    type: String, 
    enum: ['draft', 'sent', 'scheduled'],
    default: 'draft'
  },
  scheduledFor: { type: Date },
  sentAt: { type: Date },
  readBy: [{
    user: { type: Schema.Types.ObjectId, refPath: 'recipients' },
    readAt: { type: Date, default: Date.now }
  }],
  clickCount: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now }
});

export default mongoose.model<INotification>('Notification', NotificationSchema);
