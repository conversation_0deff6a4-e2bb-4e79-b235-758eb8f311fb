{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["../../src/create-program/shared.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAwB;AAExB,+CAAiC;AAUjC;;GAEG;AACH,MAAM,qBAAqB,GAAuB;IAChD,MAAM,EAAE,IAAI;IAEZ;;OAEG;IACH,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;CACzB,CAAC;AAsHA,sDAAqB;AApHvB;;GAEG;AACH,MAAM,wBAAwB,mCACzB,qBAAqB,KACxB,oBAAoB,EAAE,IAAI,EAC1B,OAAO,EAAE,IAAI,EACb,OAAO,EAAE,IAAI,GACd,CAAC;AAEF,SAAS,qCAAqC,CAC5C,aAA4B;IAE5B,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;QAC9C,uCACK,wBAAwB,KAC3B,mBAAmB,EAAE,IAAI,IACzB;KACH;IAED,OAAO,wBAAwB,CAAC;AAClC,CAAC;AAkGC,sFAAqC;AA7FvC,8EAA8E;AAC9E,MAAM,yBAAyB,GAC7B,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC;AACjE,MAAM,iBAAiB,GAAG,yBAAyB;IACjD,CAAC,CAAC,CAAC,QAAgB,EAAU,EAAE,CAAC,QAAQ;IACxC,CAAC,CAAC,CAAC,QAAgB,EAAU,EAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AAEzD,SAAS,oBAAoB,CAAC,QAAgB;IAC5C,IAAI,UAAU,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC1C,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAI,CAAC,GAAG,CAAC,EAAE;QACjC,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACtC;IACD,OAAO,iBAAiB,CAAC,UAAU,CAAkB,CAAC;AACxD,CAAC;AAmFC,oDAAoB;AAjFtB,SAAS,kBAAkB,CAAC,CAAS,EAAE,eAAuB;IAC5D,OAAO,cAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,eAAe,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACrD,CAAC;AA4EC,gDAAkB;AA1EpB,SAAS,gBAAgB,CAAC,CAAgB;IACxC,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAkB,CAAC;AAC1C,CAAC;AAoEC,4CAAgB;AAlElB,MAAM,qBAAqB,GAAG;IAC5B,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,IAAI;IACjB,EAAE,CAAC,SAAS,CAAC,IAAI;CACT,CAAC;AACX,SAAS,YAAY,CAAC,QAA4B;;IAChD,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CACL,MAAA,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CACzC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CACjC,mCAAI,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,cAAuB,EACvB,aAA4B;IAE5B,MAAM,GAAG,GAAG,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEjE,oFAAoF;IACpF,MAAM,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACzD,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,QAAQ,CAAC,CAAC;IAChD,IAAI,WAAW,KAAK,WAAW,EAAE;QAC/B,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;AACjD,CAAC;AAyCC,8CAAiB;AAvCnB,SAAS,iBAAiB,CAAC,kBAA0B;IACnD,IAAI,cAA8B,CAAC;IAEnC,IAAI;QACF,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAmB,CAAC;KAChE;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,UAAU,GAAG;YACjB,2DAA2D;YAC3D,2FAA2F;SAC5F,CAAC;QAEF,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KACxC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAyBC,8CAAiB;AAvBnB;;;;GAIG;AACH,SAAS,UAAU,CAAC,OAAe;;IACjC,qCAAqC;IACrC,IAAI,MAAA,EAAE,CAAC,GAAG,0CAAE,UAAU,EAAE;QACtB,OAAO,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KACnC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAQC,gCAAU"}