{"ast": null, "code": "import axios from'axios';const API_URL='http://localhost:5000/api/auth';// Create axios instance with error handling\nconst api=axios.create({baseURL:API_URL});// Add request interceptor for debugging\napi.interceptors.request.use(config=>{var _config$method;console.log('API Request:',(_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),config.url,config.data);return config;},error=>{console.error('Request Error:',error);return Promise.reject(error);});// Add response interceptor for debugging\napi.interceptors.response.use(response=>{console.log('API Response:',response.status,response.data);return response;},error=>{var _error$response,_error$response2;console.error('Response Error:',(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data)||error.message);return Promise.reject(error);});export const login=async data=>{try{const response=await api.post('/login',data);if(response.data.token){localStorage.setItem('token',response.data.token);localStorage.setItem('user',JSON.stringify(response.data.user));}return response.data;}catch(error){console.error('Login error:',error);throw error;}};export const registerStudent=async data=>{try{const response=await api.post('/register/student',data);return response.data;}catch(error){console.error('Student registration error:',error);throw error;}};export const registerCollege=async data=>{try{const formData=new FormData();// Add text fields\nformData.append('name',data.name);formData.append('email',data.email);formData.append('password',data.password);formData.append('address',data.address);// Add documents if any\nif(data.documents&&data.documents.length>0){for(let i=0;i<data.documents.length;i++){formData.append('documents',data.documents[i]);}}// Log form data for debugging\nconsole.log('Form data entries:');for(const pair of formData.entries()){console.log(pair[0],pair[1]);}const response=await api.post('/register/college',formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){console.error('College registration error:',error);throw error;}};export const logout=()=>{localStorage.removeItem('token');localStorage.removeItem('user');};export const getCurrentUser=()=>{const userStr=localStorage.getItem('user');if(userStr)return JSON.parse(userStr);return null;};", "map": {"version": 3, "names": ["axios", "API_URL", "api", "create", "baseURL", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "data", "error", "Promise", "reject", "response", "status", "_error$response", "_error$response2", "message", "login", "post", "token", "localStorage", "setItem", "JSON", "stringify", "user", "registerStudent", "registerCollege", "formData", "FormData", "append", "name", "email", "password", "address", "documents", "length", "i", "pair", "entries", "headers", "logout", "removeItem", "getCurrentUser", "userStr", "getItem", "parse"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/services/auth.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:5000/api/auth';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  userType: 'student' | 'college';\n}\n\ninterface RegisterStudentData {\n  name: string;\n  email: string;\n  password: string;\n  age: number;\n  collegeName: string;\n  class: string;\n  adharId: string;\n  studentCardId: string;\n}\n\ninterface RegisterCollegeData {\n  name: string;\n  email: string;\n  password: string;\n  address: string;\n  documents?: File[];\n}\n\n// Create axios instance with error handling\nconst api = axios.create({\n  baseURL: API_URL,\n});\n\n// Add request interceptor for debugging\napi.interceptors.request.use(\n  (config) => {\n    console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);\n    return config;\n  },\n  (error) => {\n    console.error('Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for debugging\napi.interceptors.response.use(\n  (response) => {\n    console.log('API Response:', response.status, response.data);\n    return response;\n  },\n  (error) => {\n    console.error('Response Error:', error.response?.status, error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\nexport const login = async (data: LoginData) => {\n  try {\n    const response = await api.post('/login', data);\n    \n    if (response.data.token) {\n      localStorage.setItem('token', response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data.user));\n    }\n    \n    return response.data;\n  } catch (error) {\n    console.error('Login error:', error);\n    throw error;\n  }\n};\n\nexport const registerStudent = async (data: RegisterStudentData) => {\n  try {\n    const response = await api.post('/register/student', data);\n    return response.data;\n  } catch (error) {\n    console.error('Student registration error:', error);\n    throw error;\n  }\n};\n\nexport const registerCollege = async (data: RegisterCollegeData) => {\n  try {\n    const formData = new FormData();\n    \n    // Add text fields\n    formData.append('name', data.name);\n    formData.append('email', data.email);\n    formData.append('password', data.password);\n    formData.append('address', data.address);\n    \n    // Add documents if any\n    if (data.documents && data.documents.length > 0) {\n      for (let i = 0; i < data.documents.length; i++) {\n        formData.append('documents', data.documents[i]);\n      }\n    }\n    \n    // Log form data for debugging\n    console.log('Form data entries:');\n    for (const pair of formData.entries()) {\n      console.log(pair[0], pair[1]);\n    }\n    \n    const response = await api.post('/register/college', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    \n    return response.data;\n  } catch (error) {\n    console.error('College registration error:', error);\n    throw error;\n  }\n};\n\nexport const logout = () => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\n\nexport const getCurrentUser = () => {\n  const userStr = localStorage.getItem('user');\n  if (userStr) return JSON.parse(userStr);\n  return null;\n};"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,OAAO,CAAG,gCAAgC,CA2BhD;AACA,KAAM,CAAAC,GAAG,CAAGF,KAAK,CAACG,MAAM,CAAC,CACvBC,OAAO,CAAEH,OACX,CAAC,CAAC,CAEF;AACAC,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,KAAAC,cAAA,CACVC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAAF,cAAA,CAAED,MAAM,CAACI,MAAM,UAAAH,cAAA,iBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAAEL,MAAM,CAACM,GAAG,CAAEN,MAAM,CAACO,IAAI,CAAC,CAClF,MAAO,CAAAP,MAAM,CACf,CAAC,CACAQ,KAAK,EAAK,CACTN,OAAO,CAACM,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtC,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAd,GAAG,CAACG,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC1BY,QAAQ,EAAK,CACZT,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEQ,QAAQ,CAACC,MAAM,CAAED,QAAQ,CAACJ,IAAI,CAAC,CAC5D,MAAO,CAAAI,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,KAAAK,eAAA,CAAAC,gBAAA,CACTZ,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAAK,eAAA,CAAEL,KAAK,CAACG,QAAQ,UAAAE,eAAA,iBAAdA,eAAA,CAAgBD,MAAM,CAAE,EAAAE,gBAAA,CAAAN,KAAK,CAACG,QAAQ,UAAAG,gBAAA,iBAAdA,gBAAA,CAAgBP,IAAI,GAAIC,KAAK,CAACO,OAAO,CAAC,CAC/F,MAAO,CAAAN,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,MAAO,MAAM,CAAAQ,KAAK,CAAG,KAAO,CAAAT,IAAe,EAAK,CAC9C,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAjB,GAAG,CAACuB,IAAI,CAAC,QAAQ,CAAEV,IAAI,CAAC,CAE/C,GAAII,QAAQ,CAACJ,IAAI,CAACW,KAAK,CAAE,CACvBC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAET,QAAQ,CAACJ,IAAI,CAACW,KAAK,CAAC,CAClDC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAACJ,IAAI,CAACgB,IAAI,CAAC,CAAC,CAClE,CAEA,MAAO,CAAAZ,QAAQ,CAACJ,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAgB,eAAe,CAAG,KAAO,CAAAjB,IAAyB,EAAK,CAClE,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAjB,GAAG,CAACuB,IAAI,CAAC,mBAAmB,CAAEV,IAAI,CAAC,CAC1D,MAAO,CAAAI,QAAQ,CAACJ,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAiB,eAAe,CAAG,KAAO,CAAAlB,IAAyB,EAAK,CAClE,GAAI,CACF,KAAM,CAAAmB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B;AACAD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAErB,IAAI,CAACsB,IAAI,CAAC,CAClCH,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAErB,IAAI,CAACuB,KAAK,CAAC,CACpCJ,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAErB,IAAI,CAACwB,QAAQ,CAAC,CAC1CL,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAErB,IAAI,CAACyB,OAAO,CAAC,CAExC;AACA,GAAIzB,IAAI,CAAC0B,SAAS,EAAI1B,IAAI,CAAC0B,SAAS,CAACC,MAAM,CAAG,CAAC,CAAE,CAC/C,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG5B,IAAI,CAAC0B,SAAS,CAACC,MAAM,CAAEC,CAAC,EAAE,CAAE,CAC9CT,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAErB,IAAI,CAAC0B,SAAS,CAACE,CAAC,CAAC,CAAC,CACjD,CACF,CAEA;AACAjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC,IAAK,KAAM,CAAAiC,IAAI,GAAI,CAAAV,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAE,CACrCnC,OAAO,CAACC,GAAG,CAACiC,IAAI,CAAC,CAAC,CAAC,CAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAC/B,CAEA,KAAM,CAAAzB,QAAQ,CAAG,KAAM,CAAAjB,GAAG,CAACuB,IAAI,CAAC,mBAAmB,CAAES,QAAQ,CAAE,CAC7DY,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CAEF,MAAO,CAAA3B,QAAQ,CAACJ,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAA+B,MAAM,CAAGA,CAAA,GAAM,CAC1BpB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC,CAChCrB,YAAY,CAACqB,UAAU,CAAC,MAAM,CAAC,CACjC,CAAC,CAED,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,OAAO,CAAGvB,YAAY,CAACwB,OAAO,CAAC,MAAM,CAAC,CAC5C,GAAID,OAAO,CAAE,MAAO,CAAArB,IAAI,CAACuB,KAAK,CAACF,OAAO,CAAC,CACvC,MAAO,KAAI,CACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}