{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/workuuu/frontend/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{login}from'../services/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const navigate=useNavigate();const[formData,setFormData]=useState({email:'',password:'',userType:'student'});const[error,setError]=useState('');const handleChange=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const handleSubmit=async e=>{e.preventDefault();setError('');try{await login(formData);navigate('/dashboard');}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Login failed. Please try again.');}};return/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Login\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-container\",children:[error&&/*#__PURE__*/_jsx(\"div\",{style:{color:'red',marginBottom:'15px'},children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"userType\",children:\"I am a\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"userType\",name:\"userType\",value:formData.userType,onChange:handleChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"student\",children:\"Student\"}),/*#__PURE__*/_jsx(\"option\",{value:\"college\",children:\"College\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",children:\"Login\"})]})]})]});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "login", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "navigate", "formData", "setFormData", "email", "password", "userType", "error", "setError", "handleChange", "e", "_objectSpread", "target", "name", "value", "handleSubmit", "preventDefault", "err", "_err$response", "_err$response$data", "response", "data", "message", "className", "children", "style", "color", "marginBottom", "onSubmit", "htmlFor", "type", "id", "onChange", "required"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\n\nconst Login: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student' as 'student' | 'college'\n  });\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>Login</h1>\n      <div className=\"form-container\">\n        {error && <div style={{ color: 'red', marginBottom: '15px' }}>{error}</div>}\n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"userType\">I am a</label>\n            <select\n              id=\"userType\"\n              name=\"userType\"\n              value={formData.userType}\n              onChange={handleChange}\n              required\n            >\n              <option value=\"student\">Student</option>\n              <option value=\"college\">College</option>\n            </select>\n          </div>\n          <button type=\"submit\" className=\"btn btn-primary\">Login</button>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": "6HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,KAAK,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CACvCW,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAAgB,YAAY,CAAIC,CAA0D,EAAK,CACnFP,WAAW,CAAAQ,aAAA,CAAAA,aAAA,IACNT,QAAQ,MACX,CAACQ,CAAC,CAACE,MAAM,CAACC,IAAI,EAAGH,CAAC,CAACE,MAAM,CAACE,KAAK,EAChC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAL,CAAkB,EAAK,CACjDA,CAAC,CAACM,cAAc,CAAC,CAAC,CAClBR,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAb,KAAK,CAACO,QAAQ,CAAC,CACrBD,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOgB,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBX,QAAQ,CAAC,EAAAU,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcG,IAAI,UAAAF,kBAAA,iBAAlBA,kBAAA,CAAoBG,OAAO,GAAI,iCAAiC,CAAC,CAC5E,CACF,CAAC,CAED,mBACEvB,KAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,IAAA,OAAA2B,QAAA,CAAI,OAAK,CAAI,CAAC,cACdzB,KAAA,QAAKwB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5BjB,KAAK,eAAIV,IAAA,QAAK4B,KAAK,CAAE,CAAEC,KAAK,CAAE,KAAK,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAEjB,KAAK,CAAM,CAAC,cAC3ER,KAAA,SAAM6B,QAAQ,CAAEb,YAAa,CAAAS,QAAA,eAC3BzB,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAOgC,OAAO,CAAC,OAAO,CAAAL,QAAA,CAAC,OAAK,CAAO,CAAC,cACpC3B,IAAA,UACEiC,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACVlB,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEZ,QAAQ,CAACE,KAAM,CACtB4B,QAAQ,CAAEvB,YAAa,CACvBwB,QAAQ,MACT,CAAC,EACC,CAAC,cACNlC,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAOgC,OAAO,CAAC,UAAU,CAAAL,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1C3B,IAAA,UACEiC,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACblB,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEZ,QAAQ,CAACG,QAAS,CACzB2B,QAAQ,CAAEvB,YAAa,CACvBwB,QAAQ,MACT,CAAC,EACC,CAAC,cACNlC,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAOgC,OAAO,CAAC,UAAU,CAAAL,QAAA,CAAC,QAAM,CAAO,CAAC,cACxCzB,KAAA,WACEgC,EAAE,CAAC,UAAU,CACblB,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEZ,QAAQ,CAACI,QAAS,CACzB0B,QAAQ,CAAEvB,YAAa,CACvBwB,QAAQ,MAAAT,QAAA,eAER3B,IAAA,WAAQiB,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC3B,IAAA,WAAQiB,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,EACN,CAAC,cACN3B,IAAA,WAAQiC,IAAI,CAAC,QAAQ,CAACP,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC5D,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}