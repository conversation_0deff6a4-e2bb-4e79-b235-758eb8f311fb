{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Admin=()=>{const[colleges,setColleges]=useState([]);const[loading,setLoading]=useState(false);const[message,setMessage]=useState('');const[emailToVerify,setEmailToVerify]=useState('');const fetchUnverifiedColleges=async()=>{try{setLoading(true);const response=await axios.get('http://localhost:5000/api/auth/unverified-colleges');setColleges(response.data.colleges);}catch(error){console.error('Error fetching colleges:',error);setMessage('Error fetching unverified colleges');}finally{setLoading(false);}};const verifyCollegeById=async id=>{try{await axios.put(\"http://localhost:5000/api/auth/verify/\".concat(id));setMessage('College verified successfully!');fetchUnverifiedColleges();// Refresh the list\n}catch(error){console.error('Error verifying college:',error);setMessage('Error verifying college');}};const verifyCollegeByEmail=async()=>{try{await axios.post('http://localhost:5000/api/auth/verify-by-email',{email:emailToVerify});setMessage('College verified successfully by email!');setEmailToVerify('');fetchUnverifiedColleges();// Refresh the list\n}catch(error){console.error('Error verifying college:',error);setMessage('Error verifying college by email');}};useEffect(()=>{fetchUnverifiedColleges();},[]);return/*#__PURE__*/_jsx(\"div\",{className:\"auth-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-box\",style:{maxWidth:'800px'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Admin Panel\"}),/*#__PURE__*/_jsx(\"p\",{children:\"College Verification Management\"})]}),message&&/*#__PURE__*/_jsx(\"div\",{className:message.includes('Error')?'error-message':'success-message',children:message}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Quick Verify by Email\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-form\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"email\",placeholder:\"Enter college email\",value:emailToVerify,onChange:e=>setEmailToVerify(e.target.value),className:\"admin-input\"}),/*#__PURE__*/_jsx(\"button\",{onClick:verifyCollegeByEmail,disabled:!emailToVerify,className:\"auth-btn\",style:{maxWidth:'120px'},children:\"Verify\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"admin-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Unverified Colleges\"}),/*#__PURE__*/_jsx(\"button\",{onClick:fetchUnverifiedColleges,className:\"auth-btn\",style:{marginBottom:'20px',maxWidth:'150px'},children:\"Refresh List\"}),loading?/*#__PURE__*/_jsx(\"p\",{children:\"Loading...\"}):colleges.length===0?/*#__PURE__*/_jsx(\"p\",{children:\"No unverified colleges found.\"}):/*#__PURE__*/_jsx(\"div\",{className:\"admin-colleges-list\",children:colleges.map(college=>/*#__PURE__*/_jsxs(\"div\",{className:\"admin-college-card\",children:[/*#__PURE__*/_jsx(\"h4\",{children:college.name}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" \",college.email]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Address:\"}),\" \",college.address]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Documents:\"}),\" \",college.verificationDocuments.length,\" files uploaded\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Registered:\"}),\" \",new Date(college.createdAt).toLocaleDateString()]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>verifyCollegeById(college._id),className:\"auth-btn\",style:{maxWidth:'150px',marginTop:'10px'},children:\"Verify College\"})]},college._id))})]})]})});};export default Admin;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Admin", "colleges", "setColleges", "loading", "setLoading", "message", "setMessage", "emailToVerify", "setEmailToVerify", "fetchUnverifiedColleges", "response", "get", "data", "error", "console", "verifyCollegeById", "id", "put", "concat", "verifyCollegeByEmail", "post", "email", "className", "children", "style", "max<PERSON><PERSON><PERSON>", "includes", "type", "placeholder", "value", "onChange", "e", "target", "onClick", "disabled", "marginBottom", "length", "map", "college", "name", "address", "verificationDocuments", "Date", "createdAt", "toLocaleDateString", "_id", "marginTop"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Admin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\ninterface College {\n  _id: string;\n  name: string;\n  email: string;\n  address: string;\n  verificationDocuments: string[];\n  isVerified: boolean;\n  createdAt: string;\n}\n\nconst Admin: React.FC = () => {\n  const [colleges, setColleges] = useState<College[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [emailToVerify, setEmailToVerify] = useState('');\n\n  const fetchUnverifiedColleges = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/auth/unverified-colleges');\n      setColleges(response.data.colleges);\n    } catch (error) {\n      console.error('Error fetching colleges:', error);\n      setMessage('Error fetching unverified colleges');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const verifyCollegeById = async (id: string) => {\n    try {\n      await axios.put(`http://localhost:5000/api/auth/verify/${id}`);\n      setMessage('College verified successfully!');\n      fetchUnverifiedColleges(); // Refresh the list\n    } catch (error) {\n      console.error('Error verifying college:', error);\n      setMessage('Error verifying college');\n    }\n  };\n\n  const verifyCollegeByEmail = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/verify-by-email', {\n        email: emailToVerify\n      });\n      setMessage('College verified successfully by email!');\n      setEmailToVerify('');\n      fetchUnverifiedColleges(); // Refresh the list\n    } catch (error) {\n      console.error('Error verifying college:', error);\n      setMessage('Error verifying college by email');\n    }\n  };\n\n  useEffect(() => {\n    fetchUnverifiedColleges();\n  }, []);\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-box\" style={{ maxWidth: '800px' }}>\n        <div className=\"auth-header\">\n          <h1>Admin Panel</h1>\n          <p>College Verification Management</p>\n        </div>\n\n        {message && (\n          <div className={message.includes('Error') ? 'error-message' : 'success-message'}>\n            {message}\n          </div>\n        )}\n\n        {/* Quick verification by email */}\n        <div className=\"admin-section\">\n          <h3>Quick Verify by Email</h3>\n          <div className=\"admin-form\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter college email\"\n              value={emailToVerify}\n              onChange={(e) => setEmailToVerify(e.target.value)}\n              className=\"admin-input\"\n            />\n            <button\n              onClick={verifyCollegeByEmail}\n              disabled={!emailToVerify}\n              className=\"auth-btn\"\n              style={{ maxWidth: '120px' }}\n            >\n              Verify\n            </button>\n          </div>\n        </div>\n\n        {/* List of unverified colleges */}\n        <div className=\"admin-section\">\n          <h3>Unverified Colleges</h3>\n          <button\n            onClick={fetchUnverifiedColleges}\n            className=\"auth-btn\"\n            style={{ marginBottom: '20px', maxWidth: '150px' }}\n          >\n            Refresh List\n          </button>\n\n          {loading ? (\n            <p>Loading...</p>\n          ) : colleges.length === 0 ? (\n            <p>No unverified colleges found.</p>\n          ) : (\n            <div className=\"admin-colleges-list\">\n              {colleges.map((college) => (\n                <div key={college._id} className=\"admin-college-card\">\n                  <h4>{college.name}</h4>\n                  <p><strong>Email:</strong> {college.email}</p>\n                  <p><strong>Address:</strong> {college.address}</p>\n                  <p><strong>Documents:</strong> {college.verificationDocuments.length} files uploaded</p>\n                  <p><strong>Registered:</strong> {new Date(college.createdAt).toLocaleDateString()}</p>\n\n                  <button\n                    onClick={() => verifyCollegeById(college._id)}\n                    className=\"auth-btn\"\n                    style={{ maxWidth: '150px', marginTop: '10px' }}\n                  >\n                    Verify College\n                  </button>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Admin;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAY1B,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGT,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACc,aAAa,CAAEC,gBAAgB,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAAgB,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CACFL,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAf,KAAK,CAACgB,GAAG,CAAC,oDAAoD,CAAC,CACtFT,WAAW,CAACQ,QAAQ,CAACE,IAAI,CAACX,QAAQ,CAAC,CACrC,CAAE,MAAOY,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDP,UAAU,CAAC,oCAAoC,CAAC,CAClD,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAW,iBAAiB,CAAG,KAAO,CAAAC,EAAU,EAAK,CAC9C,GAAI,CACF,KAAM,CAAArB,KAAK,CAACsB,GAAG,0CAAAC,MAAA,CAA0CF,EAAE,CAAE,CAAC,CAC9DV,UAAU,CAAC,gCAAgC,CAAC,CAC5CG,uBAAuB,CAAC,CAAC,CAAE;AAC7B,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDP,UAAU,CAAC,yBAAyB,CAAC,CACvC,CACF,CAAC,CAED,KAAM,CAAAa,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAAxB,KAAK,CAACyB,IAAI,CAAC,gDAAgD,CAAE,CACjEC,KAAK,CAAEd,aACT,CAAC,CAAC,CACFD,UAAU,CAAC,yCAAyC,CAAC,CACrDE,gBAAgB,CAAC,EAAE,CAAC,CACpBC,uBAAuB,CAAC,CAAC,CAAE;AAC7B,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDP,UAAU,CAAC,kCAAkC,CAAC,CAChD,CACF,CAAC,CAEDZ,SAAS,CAAC,IAAM,CACde,uBAAuB,CAAC,CAAC,CAC3B,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEZ,IAAA,QAAKyB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BxB,KAAA,QAAKuB,SAAS,CAAC,UAAU,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAF,QAAA,eACrDxB,KAAA,QAAKuB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B1B,IAAA,OAAA0B,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB1B,IAAA,MAAA0B,QAAA,CAAG,iCAA+B,CAAG,CAAC,EACnC,CAAC,CAELlB,OAAO,eACNR,IAAA,QAAKyB,SAAS,CAAEjB,OAAO,CAACqB,QAAQ,CAAC,OAAO,CAAC,CAAG,eAAe,CAAG,iBAAkB,CAAAH,QAAA,CAC7ElB,OAAO,CACL,CACN,cAGDN,KAAA,QAAKuB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B1B,IAAA,OAAA0B,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BxB,KAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1B,IAAA,UACE8B,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,qBAAqB,CACjCC,KAAK,CAAEtB,aAAc,CACrBuB,QAAQ,CAAGC,CAAC,EAAKvB,gBAAgB,CAACuB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAClDP,SAAS,CAAC,aAAa,CACxB,CAAC,cACFzB,IAAA,WACEoC,OAAO,CAAEd,oBAAqB,CAC9Be,QAAQ,CAAE,CAAC3B,aAAc,CACzBe,SAAS,CAAC,UAAU,CACpBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAF,QAAA,CAC9B,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNxB,KAAA,QAAKuB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B1B,IAAA,OAAA0B,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5B1B,IAAA,WACEoC,OAAO,CAAExB,uBAAwB,CACjCa,SAAS,CAAC,UAAU,CACpBE,KAAK,CAAE,CAAEW,YAAY,CAAE,MAAM,CAAEV,QAAQ,CAAE,OAAQ,CAAE,CAAAF,QAAA,CACpD,cAED,CAAQ,CAAC,CAERpB,OAAO,cACNN,IAAA,MAAA0B,QAAA,CAAG,YAAU,CAAG,CAAC,CACftB,QAAQ,CAACmC,MAAM,GAAK,CAAC,cACvBvC,IAAA,MAAA0B,QAAA,CAAG,+BAA6B,CAAG,CAAC,cAEpC1B,IAAA,QAAKyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCtB,QAAQ,CAACoC,GAAG,CAAEC,OAAO,eACpBvC,KAAA,QAAuBuB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnD1B,IAAA,OAAA0B,QAAA,CAAKe,OAAO,CAACC,IAAI,CAAK,CAAC,cACvBxC,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACe,OAAO,CAACjB,KAAK,EAAI,CAAC,cAC9CtB,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACe,OAAO,CAACE,OAAO,EAAI,CAAC,cAClDzC,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACe,OAAO,CAACG,qBAAqB,CAACL,MAAM,CAAC,iBAAe,EAAG,CAAC,cACxFrC,KAAA,MAAAwB,QAAA,eAAG1B,IAAA,WAAA0B,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAAmB,IAAI,CAACJ,OAAO,CAACK,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAI,CAAC,cAEtF/C,IAAA,WACEoC,OAAO,CAAEA,CAAA,GAAMlB,iBAAiB,CAACuB,OAAO,CAACO,GAAG,CAAE,CAC9CvB,SAAS,CAAC,UAAU,CACpBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEqB,SAAS,CAAE,MAAO,CAAE,CAAAvB,QAAA,CACjD,gBAED,CAAQ,CAAC,GAbDe,OAAO,CAACO,GAcb,CACN,CAAC,CACC,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}