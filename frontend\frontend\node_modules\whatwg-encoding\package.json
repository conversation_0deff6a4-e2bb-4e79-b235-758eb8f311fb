{"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.5", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/whatwg-encoding", "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.24"}, "devDependencies": {"eslint": "^5.3.0", "got": "^9.0.0", "mocha": "^5.2.0"}}