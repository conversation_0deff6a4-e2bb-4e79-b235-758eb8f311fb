"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_controller_1 = require("../controllers/auth.controller");
const multer_1 = __importDefault(require("multer"));
// Configure multer for file uploads
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        cb(null, `${Date.now()}-${file.originalname}`);
    }
});
const upload = (0, multer_1.default)({ storage });
const router = express_1.default.Router();
// Auth routes
router.post('/register/college', upload.array('documents'), auth_controller_1.registerCollege);
router.post('/register/student', auth_controller_1.registerStudent);
router.post('/login', auth_controller_1.login);
router.put('/verify/:id', auth_controller_1.verifyCollege);
exports.default = router;
