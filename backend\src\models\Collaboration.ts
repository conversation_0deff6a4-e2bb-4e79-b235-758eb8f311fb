import mongoose, { Document, Schema } from 'mongoose';

export interface ICollaboration extends Document {
  requester: mongoose.Types.ObjectId;
  requestee: mongoose.Types.ObjectId;
  eventId?: mongoose.Types.ObjectId;
  proposedEvent?: {
    title: string;
    description: string;
    eventType: string;
    proposedDate: Date;
    location: string;
  };
  status: 'pending' | 'accepted' | 'rejected' | 'completed';
  message: string;
  response?: string;
  terms?: {
    costSharing: string;
    responsibilities: string;
    resourceSharing: string;
  };
  createdAt: Date;
  respondedAt?: Date;
  completedAt?: Date;
}

const CollaborationSchema: Schema = new Schema({
  requester: { type: Schema.Types.ObjectId, ref: 'College', required: true },
  requestee: { type: Schema.Types.ObjectId, ref: 'College', required: true },
  eventId: { type: Schema.Types.ObjectId, ref: 'Event' },
  proposedEvent: {
    title: { type: String },
    description: { type: String },
    eventType: { type: String },
    proposedDate: { type: Date },
    location: { type: String }
  },
  status: { 
    type: String, 
    enum: ['pending', 'accepted', 'rejected', 'completed'],
    default: 'pending'
  },
  message: { type: String, required: true },
  response: { type: String },
  terms: {
    costSharing: { type: String },
    responsibilities: { type: String },
    resourceSharing: { type: String }
  },
  createdAt: { type: Date, default: Date.now },
  respondedAt: { type: Date },
  completedAt: { type: Date }
});

export default mongoose.model<ICollaboration>('Collaboration', CollaborationSchema);
