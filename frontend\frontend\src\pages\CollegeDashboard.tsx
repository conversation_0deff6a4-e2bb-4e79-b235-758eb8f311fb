import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

interface DashboardData {
  eventStats: {
    total: number;
    published: number;
    ongoing: number;
    completed: number;
    draft: number;
  };
  registrationStats: {
    totalRegistrations: number;
    attendedCount: number;
    cancelledCount: number;
  };
  notificationStats: {
    total: number;
    sent: number;
    scheduled: number;
  };
  collaborationStats: {
    sent: number;
    received: number;
    accepted: number;
    pending: number;
  };
  recentEvents: any[];
  upcomingEvents: any[];
  recentNotifications: any[];
  pendingCollaborations: any[];
}

const CollegeDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDashboardData(data.dashboard);
      } else {
        setError('Failed to fetch dashboard data');
      }
    } catch (error) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-error">
        <h2>Error Loading Dashboard</h2>
        <p>{error}</p>
        <button onClick={fetchDashboardData} className="btn-primary">
          Retry
        </button>
      </div>
    );
  }

  if (!dashboardData) return null;

  return (
    <div className="college-dashboard">
      <div className="dashboard-header">
        <h1>College Dashboard</h1>
        <p>Welcome back! Here's what's happening with your events.</p>
      </div>

      {/* Quick Stats */}
      <div className="stats-grid">
        <div className="stat-card events">
          <div className="stat-icon">🎯</div>
          <div className="stat-content">
            <h3>{dashboardData.eventStats.total}</h3>
            <p>Total Events</p>
            <span className="stat-detail">
              {dashboardData.eventStats.published} Published
            </span>
          </div>
        </div>

        <div className="stat-card registrations">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{dashboardData.registrationStats.totalRegistrations}</h3>
            <p>Total Registrations</p>
            <span className="stat-detail">
              {dashboardData.registrationStats.attendedCount} Attended
            </span>
          </div>
        </div>

        <div className="stat-card notifications">
          <div className="stat-icon">📢</div>
          <div className="stat-content">
            <h3>{dashboardData.notificationStats.total}</h3>
            <p>Notifications Sent</p>
            <span className="stat-detail">
              {dashboardData.notificationStats.scheduled} Scheduled
            </span>
          </div>
        </div>

        <div className="stat-card collaborations">
          <div className="stat-icon">🤝</div>
          <div className="stat-content">
            <h3>{dashboardData.collaborationStats.accepted}</h3>
            <p>Active Collaborations</p>
            <span className="stat-detail">
              {dashboardData.collaborationStats.pending} Pending
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/events/create" className="action-btn create-event">
            <span>➕</span>
            Create New Event
          </Link>
          <Link to="/notifications/create" className="action-btn send-notification">
            <span>📢</span>
            Send Notification
          </Link>
          <Link to="/collaborations" className="action-btn manage-collaborations">
            <span>🤝</span>
            Manage Collaborations
          </Link>
          <Link to="/analytics" className="action-btn view-analytics">
            <span>📊</span>
            View Analytics
          </Link>
        </div>
      </div>

      {/* Dashboard Sections */}
      <div className="dashboard-sections">
        {/* Recent Events */}
        <div className="dashboard-section">
          <div className="section-header">
            <h3>Recent Events</h3>
            <Link to="/events/manage" className="view-all">View All</Link>
          </div>
          <div className="events-list">
            {dashboardData.recentEvents.length > 0 ? (
              dashboardData.recentEvents.map((event) => (
                <div key={event._id} className="event-item">
                  <div className="event-info">
                    <h4>{event.title}</h4>
                    <p>{new Date(event.startDate).toLocaleDateString()}</p>
                  </div>
                  <div className="event-stats">
                    <span className={`status ${event.status}`}>{event.status}</span>
                    <span className="participants">
                      {event.currentParticipants}/{event.maxParticipants || '∞'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="no-data">No recent events</p>
            )}
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="dashboard-section">
          <div className="section-header">
            <h3>Upcoming Events</h3>
            <Link to="/events/upcoming" className="view-all">View All</Link>
          </div>
          <div className="events-list">
            {dashboardData.upcomingEvents.length > 0 ? (
              dashboardData.upcomingEvents.map((event) => (
                <div key={event._id} className="event-item">
                  <div className="event-info">
                    <h4>{event.title}</h4>
                    <p>{new Date(event.startDate).toLocaleDateString()}</p>
                    <span className="location">{event.location.city}</span>
                  </div>
                  <div className="event-stats">
                    <span className="participants">
                      {event.currentParticipants}/{event.maxParticipants || '∞'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="no-data">No upcoming events</p>
            )}
          </div>
        </div>

        {/* Pending Collaborations */}
        <div className="dashboard-section">
          <div className="section-header">
            <h3>Pending Collaboration Requests</h3>
            <Link to="/collaborations" className="view-all">View All</Link>
          </div>
          <div className="collaborations-list">
            {dashboardData.pendingCollaborations.length > 0 ? (
              dashboardData.pendingCollaborations.map((collab) => (
                <div key={collab._id} className="collaboration-item">
                  <div className="collaboration-info">
                    <h4>{collab.requester.name}</h4>
                    <p>{collab.message}</p>
                  </div>
                  <div className="collaboration-actions">
                    <button className="btn-accept">Accept</button>
                    <button className="btn-reject">Reject</button>
                  </div>
                </div>
              ))
            ) : (
              <p className="no-data">No pending collaboration requests</p>
            )}
          </div>
        </div>

        {/* Recent Notifications */}
        <div className="dashboard-section">
          <div className="section-header">
            <h3>Recent Notifications</h3>
            <Link to="/notifications" className="view-all">View All</Link>
          </div>
          <div className="notifications-list">
            {dashboardData.recentNotifications.length > 0 ? (
              dashboardData.recentNotifications.map((notification) => (
                <div key={notification._id} className="notification-item">
                  <div className="notification-info">
                    <h4>{notification.title}</h4>
                    <p>{notification.type}</p>
                  </div>
                  <div className="notification-stats">
                    <span className={`status ${notification.status}`}>
                      {notification.status}
                    </span>
                    <span className="clicks">{notification.clickCount} clicks</span>
                  </div>
                </div>
              ))
            ) : (
              <p className="no-data">No recent notifications</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollegeDashboard;
