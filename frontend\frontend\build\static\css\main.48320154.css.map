{"version": 3, "file": "static/css/main.48320154.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,mIAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CCZA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KACE,4BAA8B,CAC9B,eACF,CAGA,eACE,YAAa,CACb,qBAAsB,CACtB,gBACF,CAEA,QAME,kBAAmB,CALnB,wBAAyB,CACzB,UAAY,CAEZ,YAAa,CACb,6BAA8B,CAF9B,iBAIF,CAEA,WACE,gBACF,CAEA,eACE,YAAa,CACb,eACF,CAEA,kBACE,kBACF,CAEA,oBACE,UAAY,CACZ,oBACF,CAEA,0BACE,yBACF,CAEA,MACE,QAAO,CACP,YACF,CAEA,MAIE,wBAAyB,CACzB,iBAAkB,CAHlB,kBAAmB,CACnB,iBAAkB,CAFlB,iBAKF,CAEA,SAGE,aAAc,CAFd,cAAe,CACf,kBAEF,CAEA,QAGE,aAAc,CAFd,gBAAiB,CACjB,kBAEF,CAEA,SACE,YAAa,CAEb,QAAS,CADT,sBAEF,CAEA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CACf,eAAiB,CAJjB,qBAKF,CAEA,SACE,wBAAyB,CACzB,UACF,CAEA,WACE,wBAAyB,CACzB,UACF,CAEA,UACE,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAEA,SAGE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAJxC,QAAO,CACP,cAAe,CAIf,iBACF,CAEA,YAEE,aAAc,CADd,kBAEF,CAEA,QACE,wBAAyB,CACzB,UAAY,CAEZ,YAAa,CADb,iBAEF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}", "/* Basic Reset */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: Arial, sans-serif;\n  line-height: 1.6;\n}\n\n/* Layout */\n.app-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n}\n\n.header {\n  background-color: #2c3e50;\n  color: white;\n  padding: 1rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header h1 {\n  font-size: 1.5rem;\n}\n\n.header nav ul {\n  display: flex;\n  list-style: none;\n}\n\n.header nav ul li {\n  margin-left: 1.5rem;\n}\n\n.header nav ul li a {\n  color: white;\n  text-decoration: none;\n}\n\n.header nav ul li a:hover {\n  text-decoration: underline;\n}\n\n.main {\n  flex: 1;\n  padding: 2rem;\n}\n\n.hero {\n  text-align: center;\n  margin-bottom: 3rem;\n  padding: 3rem 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n\n.hero h2 {\n  font-size: 2rem;\n  margin-bottom: 1rem;\n  color: #2c3e50;\n}\n\n.hero p {\n  font-size: 1.2rem;\n  margin-bottom: 2rem;\n  color: #7f8c8d;\n}\n\n.buttons {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n}\n\n.primary {\n  background-color: #3498db;\n  color: white;\n}\n\n.secondary {\n  background-color: #95a5a6;\n  color: white;\n}\n\n.features {\n  display: flex;\n  justify-content: space-between;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n.feature {\n  flex: 1;\n  padding: 1.5rem;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.feature h3 {\n  margin-bottom: 1rem;\n  color: #2c3e50;\n}\n\n.footer {\n  background-color: #2c3e50;\n  color: white;\n  text-align: center;\n  padding: 1rem;\n}"], "names": [], "sourceRoot": ""}