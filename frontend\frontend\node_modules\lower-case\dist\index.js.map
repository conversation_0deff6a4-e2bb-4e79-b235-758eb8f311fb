{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAQA;;GAEG;AACH,IAAM,gBAAgB,GAA2B;IAC/C,EAAE,EAAE;QACF,MAAM,EAAE,6BAA6B;QACrC,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,SAAS;QACjB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;YACX,EAAE,EAAE,QAAQ;SACb;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,4CAA4C;QACpD,GAAG,EAAE;YACH,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,cAAc;YACjB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;YACvB,CAAC,EAAE,oBAAoB;SACxB;KACF;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,eAAe,CAAC,GAAW,EAAE,MAAc;IACzD,IAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,IAAI,IAAI;QAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAX,CAAW,CAAC,CAAC,CAAC;IACzE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAJD,0CAIC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC3B,CAAC;AAFD,8BAEC", "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\",\n    },\n  },\n};\n\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return lowerCase(str);\n}\n\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str: string) {\n  return str.toLowerCase();\n}\n"]}