[{"C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx": "3", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx": "4", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx": "5", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts": "6", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx": "7", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx": "8"}, {"size": 273, "mtime": 1749107557668, "results": "9", "hashOfConfig": "10"}, {"size": 1565, "mtime": 1749111644797, "results": "11", "hashOfConfig": "10"}, {"size": 3836, "mtime": 1749113404317, "results": "12", "hashOfConfig": "10"}, {"size": 1151, "mtime": 1749112921219, "results": "13", "hashOfConfig": "10"}, {"size": 2825, "mtime": 1749113010723, "results": "14", "hashOfConfig": "10"}, {"size": 3139, "mtime": 1749108859951, "results": "15", "hashOfConfig": "10"}, {"size": 4942, "mtime": 1749113160095, "results": "16", "hashOfConfig": "10"}, {"size": 5335, "mtime": 1749113122080, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rc2va9", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx", ["42"], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx", [], [], {"ruleId": "43", "severity": 1, "message": "44", "line": 9, "column": 8, "nodeType": "45", "messageId": "46", "endLine": 9, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'Admin' is defined but never used.", "Identifier", "unusedVar"]