[{"C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx": "3", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx": "4", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx": "5", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts": "6", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx": "7", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx": "8", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Admin.tsx": "9"}, {"size": 273, "mtime": 1749107557668, "results": "10", "hashOfConfig": "11"}, {"size": 1565, "mtime": 1749114035490, "results": "12", "hashOfConfig": "11"}, {"size": 4276, "mtime": 1749114150134, "results": "13", "hashOfConfig": "11"}, {"size": 1151, "mtime": 1749112921219, "results": "14", "hashOfConfig": "11"}, {"size": 2825, "mtime": 1749113010723, "results": "15", "hashOfConfig": "11"}, {"size": 3139, "mtime": 1749108859951, "results": "16", "hashOfConfig": "11"}, {"size": 4942, "mtime": 1749113160095, "results": "17", "hashOfConfig": "11"}, {"size": 5335, "mtime": 1749113122080, "results": "18", "hashOfConfig": "11"}, {"size": 5092, "mtime": 1749111588157, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rc2va9", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx", ["47"], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Admin.tsx", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 9, "column": 8, "nodeType": "50", "messageId": "51", "endLine": 9, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'Admin' is defined but never used.", "Identifier", "unusedVar"]