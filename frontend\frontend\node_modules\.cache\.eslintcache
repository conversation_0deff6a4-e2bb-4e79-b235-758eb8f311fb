[{"C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx": "3", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx": "4", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx": "5", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts": "6", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx": "7", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx": "8", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Admin.tsx": "9", "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\CollegeDashboard.tsx": "10"}, {"size": 273, "mtime": 1749107557668, "results": "11", "hashOfConfig": "12"}, {"size": 2207, "mtime": 1749146823889, "results": "13", "hashOfConfig": "12"}, {"size": 4276, "mtime": 1749115998020, "results": "14", "hashOfConfig": "12"}, {"size": 1151, "mtime": 1749112921219, "results": "15", "hashOfConfig": "12"}, {"size": 2825, "mtime": 1749113010723, "results": "16", "hashOfConfig": "12"}, {"size": 3139, "mtime": 1749108859951, "results": "17", "hashOfConfig": "12"}, {"size": 4942, "mtime": 1749113160095, "results": "18", "hashOfConfig": "12"}, {"size": 5335, "mtime": 1749113122080, "results": "19", "hashOfConfig": "12"}, {"size": 4367, "mtime": 1749114954263, "results": "20", "hashOfConfig": "12"}, {"size": 12231, "mtime": 1749147262567, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rc2va9", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\services\\auth.ts", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\CollegeSignup.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\components\\auth\\StudentSignup.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\Admin.tsx", [], [], "C:\\Users\\<USER>\\workuuu\\frontend\\frontend\\src\\pages\\CollegeDashboard.tsx", ["52", "53"], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 37, "column": 17, "nodeType": "56", "messageId": "57", "endLine": 37, "endColumn": 25}, {"ruleId": "58", "severity": 1, "message": "59", "line": 41, "column": 6, "nodeType": "60", "endLine": 41, "endColumn": 8, "suggestions": "61"}, "@typescript-eslint/no-unused-vars", "'setError' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["62"], {"desc": "63", "fix": "64"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "65", "text": "66"}, [941, 943], "[fetchDashboardData]"]