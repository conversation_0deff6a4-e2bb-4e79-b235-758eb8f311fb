{"version": 3, "file": "workbox-navigation-preload.dev.js", "sources": ["../_version.js", "../isSupported.js", "../disable.js", "../enable.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:navigation-preload:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * @return {boolean} Whether or not the current browser supports enabling\n * navigation preload.\n *\n * @memberof workbox-navigation-preload\n */\nfunction isSupported() {\n    return Boolean(self.registration && self.registration.navigationPreload);\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will disable it.\n *\n * @memberof workbox-navigation-preload\n */\nfunction disable() {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.disable().then(() => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is disabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { disable };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will enable it.\n *\n * @param {string} [headerValue] Optionally, allows developers to\n * [override](https://developers.google.com/web/updates/2017/02/navigation-preload#changing_the_header)\n * the value of the `Service-Worker-Navigation-Preload` header which will be\n * sent to the server when making the navigation request.\n *\n * @memberof workbox-navigation-preload\n */\nfunction enable(headerValue) {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.enable().then(() => {\n                // Defaults to Service-Worker-Navigation-Preload: true if not set.\n                if (headerValue) {\n                    void self.registration.navigationPreload.setHeaderValue(headerValue);\n                }\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is enabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { enable };\n"], "names": ["self", "_", "e", "isSupported", "Boolean", "registration", "navigationPreload", "disable", "addEventListener", "event", "waitUntil", "then", "logger", "log", "enable", "headerValue", "setHeaderValue"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,kCAAD,CAAJ,IAA4CC,CAAC,EAA7C;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,WAAT,GAAuB;IACnB,SAAOC,OAAO,CAACJ,IAAI,CAACK,YAAL,IAAqBL,IAAI,CAACK,YAAL,CAAkBC,iBAAxC,CAAd;IACH;;IChBD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;;IACA,SAASC,OAAT,GAAmB;IACf,MAAIJ,WAAW,EAAf,EAAmB;IACfH,IAAAA,IAAI,CAACQ,gBAAL,CAAsB,UAAtB,EAAmCC,KAAD,IAAW;IACzCA,MAAAA,KAAK,CAACC,SAAN,CAAgBV,IAAI,CAACK,YAAL,CAAkBC,iBAAlB,CAAoCC,OAApC,GAA8CI,IAA9C,CAAmD,MAAM;IACrE,QAA2C;IACvCC,UAAAA,gBAAM,CAACC,GAAP,CAAY,iCAAZ;IACH;IACJ,OAJe,CAAhB;IAKH,KAND;IAOH,GARD,MASK;IACD,IAA2C;IACvCD,MAAAA,gBAAM,CAACC,GAAP,CAAY,sDAAZ;IACH;IACJ;IACJ;;IC9BD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,MAAT,CAAgBC,WAAhB,EAA6B;IACzB,MAAIZ,WAAW,EAAf,EAAmB;IACfH,IAAAA,IAAI,CAACQ,gBAAL,CAAsB,UAAtB,EAAmCC,KAAD,IAAW;IACzCA,MAAAA,KAAK,CAACC,SAAN,CAAgBV,IAAI,CAACK,YAAL,CAAkBC,iBAAlB,CAAoCQ,MAApC,GAA6CH,IAA7C,CAAkD,MAAM;IACpE;IACA,YAAII,WAAJ,EAAiB;IACb,eAAKf,IAAI,CAACK,YAAL,CAAkBC,iBAAlB,CAAoCU,cAApC,CAAmDD,WAAnD,CAAL;IACH;;IACD,QAA2C;IACvCH,UAAAA,gBAAM,CAACC,GAAP,CAAY,gCAAZ;IACH;IACJ,OARe,CAAhB;IASH,KAVD;IAWH,GAZD,MAaK;IACD,IAA2C;IACvCD,MAAAA,gBAAM,CAACC,GAAP,CAAY,sDAAZ;IACH;IACJ;IACJ;;;;;;;;;;;;"}