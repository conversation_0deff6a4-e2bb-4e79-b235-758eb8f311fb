"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dashboard_controller_1 = require("../controllers/dashboard.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = express_1.default.Router();
// Dashboard routes (protected)
router.get('/', auth_middleware_1.authMiddleware, dashboard_controller_1.getCollegeDashboard);
router.get('/event/:eventId/metrics', auth_middleware_1.authMiddleware, dashboard_controller_1.getEventPerformanceMetrics);
router.get('/profile-summary', auth_middleware_1.authMiddleware, dashboard_controller_1.getCollegeProfileSummary);
exports.default = router;
