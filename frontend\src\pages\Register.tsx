import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Register: React.FC = () => {
  const [userType, setUserType] = useState<'student' | 'college'>('student');

  return (
    <div className="container">
      <h1>Register</h1>
      <div className="form-container">
        <div className="form-group">
          <label>I want to register as a</label>
          <div>
            <button 
              className={`btn ${userType === 'student' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setUserType('student')}
              style={{ marginRight: '10px' }}
            >
              Student
            </button>
            <button 
              className={`btn ${userType === 'college' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setUserType('college')}
            >
              College
            </button>
          </div>
        </div>
        
        {userType === 'student' ? (
          <Link to="/register/student">
            <button className="btn btn-primary">Continue as Student</button>
          </Link>
        ) : (
          <Link to="/register/college">
            <button className="btn btn-primary">Continue as College</button>
          </Link>
        )}
      </div>
    </div>
  );
};

export default Register;



