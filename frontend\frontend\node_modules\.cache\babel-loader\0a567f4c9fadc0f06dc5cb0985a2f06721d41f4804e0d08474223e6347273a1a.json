{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Register=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"register-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"register-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"register-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Choose Registration Type\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Select your account type to get started\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"register-options\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/register/student\",className:\"register-option student-option\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"option-icon\",children:\"\\uD83C\\uDF93\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Register as Student\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Join events at your college and discover new opportunities\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/register/college\",className:\"register-option college-option\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"option-icon\",children:\"\\uD83C\\uDFEB\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Register as College\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create and manage events for your institution\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"register-footer\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"login-link\",children:\"Login here\"})]})]})});};export default Register;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Register", "className", "children", "to"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Register: React.FC = () => {\n  return (\n    <div className=\"register-container\">\n      <div className=\"register-box\">\n        <div className=\"register-header\">\n          <h2>Choose Registration Type</h2>\n          <p>Select your account type to get started</p>\n        </div>\n\n        <div className=\"register-options\">\n          <Link to=\"/register/student\" className=\"register-option student-option\">\n            <div className=\"option-icon\">🎓</div>\n            <h3>Register as Student</h3>\n            <p>Join events at your college and discover new opportunities</p>\n          </Link>\n\n          <Link to=\"/register/college\" className=\"register-option college-option\">\n            <div className=\"option-icon\">🏫</div>\n            <h3>Register as College</h3>\n            <p>Create and manage events for your institution</p>\n          </Link>\n        </div>\n\n        <div className=\"register-footer\">\n          Already have an account? <Link to=\"/login\" className=\"login-link\">Login here</Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,mBACEH,IAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BH,KAAA,QAAKE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BL,IAAA,OAAAK,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCL,IAAA,MAAAK,QAAA,CAAG,yCAAuC,CAAG,CAAC,EAC3C,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,mBAAmB,CAACF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eACrEL,IAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACrCL,IAAA,OAAAK,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BL,IAAA,MAAAK,QAAA,CAAG,4DAA0D,CAAG,CAAC,EAC7D,CAAC,cAEPH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,mBAAmB,CAACF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eACrEL,IAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACrCL,IAAA,OAAAK,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BL,IAAA,MAAAK,QAAA,CAAG,+CAA6C,CAAG,CAAC,EAChD,CAAC,EACJ,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,2BACN,cAAAL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EAChF,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}