{"ast": null, "code": "const hasBrowserEnv = typeof window !== 'undefined' && typeof document !== 'undefined';\nconst _navigator = typeof navigator === 'object' && navigator || undefined;\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\nconst hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || ['ReactNative', 'NativeScript', 'NS'].indexOf(_navigator.product) < 0);\n\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */\nconst hasStandardBrowserWebWorkerEnv = (() => {\n  return typeof WorkerGlobalScope !== 'undefined' &&\n  // eslint-disable-next-line no-undef\n  self instanceof WorkerGlobalScope && typeof self.importScripts === 'function';\n})();\nconst origin = hasBrowserEnv && window.location.href || 'http://localhost';\nexport { hasBrowserEnv, hasStandardBrowserWebWorkerEnv, hasStandardBrowserEnv, _navigator as navigator, origin };", "map": {"version": 3, "names": ["hasBrowserEnv", "window", "document", "_navigator", "navigator", "undefined", "hasStandardBrowserEnv", "indexOf", "product", "hasStandardBrowserWebWorkerEnv", "WorkerGlobalScope", "self", "importScripts", "origin", "location", "href"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/node_modules/axios/lib/platform/common/utils.js"], "sourcesContent": ["const hasBrowserEnv = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst _navigator = typeof navigator === 'object' && navigator || undefined;\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\nconst hasStandardBrowserEnv = hasBrowserEnv &&\n  (!_navigator || ['ReactNative', 'NativeScript', 'NS'].indexOf(_navigator.product) < 0);\n\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */\nconst hasStandardBrowserWebWorkerEnv = (() => {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    // eslint-disable-next-line no-undef\n    self instanceof WorkerGlobalScope &&\n    typeof self.importScripts === 'function'\n  );\n})();\n\nconst origin = hasBrowserEnv && window.location.href || 'http://localhost';\n\nexport {\n  hasBrowserEnv,\n  hasStandardBrowserWebWorkerEnv,\n  hasStandardBrowserEnv,\n  _navigator as navigator,\n  origin\n}\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW;AAEtF,MAAMC,UAAU,GAAG,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAIC,SAAS;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGN,aAAa,KACxC,CAACG,UAAU,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC,CAACI,OAAO,CAACJ,UAAU,CAACK,OAAO,CAAC,GAAG,CAAC,CAAC;;AAExF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC,MAAM;EAC5C,OACE,OAAOC,iBAAiB,KAAK,WAAW;EACxC;EACAC,IAAI,YAAYD,iBAAiB,IACjC,OAAOC,IAAI,CAACC,aAAa,KAAK,UAAU;AAE5C,CAAC,EAAE,CAAC;AAEJ,MAAMC,MAAM,GAAGb,aAAa,IAAIC,MAAM,CAACa,QAAQ,CAACC,IAAI,IAAI,kBAAkB;AAE1E,SACEf,aAAa,EACbS,8BAA8B,EAC9BH,qBAAqB,EACrBH,UAAU,IAAIC,SAAS,EACvBS,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}