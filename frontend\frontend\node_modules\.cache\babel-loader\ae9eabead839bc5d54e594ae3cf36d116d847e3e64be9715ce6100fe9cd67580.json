{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Link}from'react-router-dom';import'./App.css';import Home from'./pages/Home';import Login from'./pages/Login';import Register from'./pages/Register';import StudentSignup from'./components/auth/StudentSignup';import CollegeSignup from'./components/auth/CollegeSignup';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"app-container\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"College Events Platform\"}),/*#__PURE__*/_jsx(\"nav\",{children:/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/\",children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/events\",children:\"Events\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/login\",children:\"Login\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/register\",children:\"Register\"})})]})})]}),/*#__PURE__*/_jsx(\"main\",{className:\"main\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register/student\",element:/*#__PURE__*/_jsx(StudentSignup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register/college\",element:/*#__PURE__*/_jsx(CollegeSignup,{})}),/*#__PURE__*/_jsx(Route,{path:\"/events\",element:/*#__PURE__*/_jsx(\"div\",{children:\"Events page coming soon...\"})})]})}),/*#__PURE__*/_jsx(\"footer\",{className:\"footer\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2023 College Events Platform\"})})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "Home", "<PERSON><PERSON>", "Register", "StudentSignup", "CollegeSignup", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "to", "path", "element"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport './App.css';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport StudentSignup from './components/auth/StudentSignup';\nimport CollegeSignup from './components/auth/CollegeSignup';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"app-container\">\n        <header className=\"header\">\n          <h1>College Events Platform</h1>\n          <nav>\n            <ul>\n              <li><Link to=\"/\">Home</Link></li>\n              <li><Link to=\"/events\">Events</Link></li>\n              <li><Link to=\"/login\">Login</Link></li>\n              <li><Link to=\"/register\">Register</Link></li>\n            </ul>\n          </nav>\n        </header>\n\n        <main className=\"main\">\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/register/student\" element={<StudentSignup />} />\n            <Route path=\"/register/college\" element={<CollegeSignup />} />\n            <Route path=\"/events\" element={<div>Events page coming soon...</div>} />\n          </Routes>\n        </main>\n\n        <footer className=\"footer\">\n          <p>&copy; 2023 College Events Platform</p>\n        </footer>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,KAAQ,kBAAkB,CAC/E,MAAO,WAAW,CAClB,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACV,MAAM,EAAAc,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BF,KAAA,WAAQG,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACxBJ,IAAA,OAAAI,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCJ,IAAA,QAAAI,QAAA,cACEF,KAAA,OAAAE,QAAA,eACEJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACP,IAAI,EAACa,EAAE,CAAC,GAAG,CAAAF,QAAA,CAAC,MAAI,CAAM,CAAC,CAAI,CAAC,cACjCJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACP,IAAI,EAACa,EAAE,CAAC,SAAS,CAAAF,QAAA,CAAC,QAAM,CAAM,CAAC,CAAI,CAAC,cACzCJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACP,IAAI,EAACa,EAAE,CAAC,QAAQ,CAAAF,QAAA,CAAC,OAAK,CAAM,CAAC,CAAI,CAAC,cACvCJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACP,IAAI,EAACa,EAAE,CAAC,WAAW,CAAAF,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,EAC3C,CAAC,CACF,CAAC,EACA,CAAC,cAETJ,IAAA,SAAMK,SAAS,CAAC,MAAM,CAAAD,QAAA,cACpBF,KAAA,CAACX,MAAM,EAAAa,QAAA,eACLJ,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAER,IAAA,CAACN,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCM,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAER,IAAA,CAACL,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CK,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,WAAW,CAACC,OAAO,cAAER,IAAA,CAACJ,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDI,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAER,IAAA,CAACH,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9DG,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAER,IAAA,CAACF,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9DE,IAAA,CAACR,KAAK,EAACe,IAAI,CAAC,SAAS,CAACC,OAAO,cAAER,IAAA,QAAAI,QAAA,CAAK,4BAA0B,CAAK,CAAE,CAAE,CAAC,EAClE,CAAC,CACL,CAAC,cAEPJ,IAAA,WAAQK,SAAS,CAAC,QAAQ,CAAAD,QAAA,cACxBJ,IAAA,MAAAI,QAAA,CAAG,mCAAmC,CAAG,CAAC,CACpC,CAAC,EACN,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}