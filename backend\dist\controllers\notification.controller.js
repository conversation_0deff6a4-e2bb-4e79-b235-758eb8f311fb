"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.trackNotificationClick = exports.markNotificationAsRead = exports.getNotificationAnalytics = exports.notifyEventParticipants = exports.getCollegeNotifications = exports.createNotification = void 0;
const Notification_1 = __importDefault(require("../models/Notification"));
const Event_1 = __importDefault(require("../models/Event"));
// Create and send notification
const createNotification = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { title, message, type, recipientType, recipientIds, relatedEvent, scheduledFor } = req.body;
        // @ts-ignore
        const senderId = req.college.id;
        const notification = new Notification_1.default({
            title,
            message,
            type,
            recipients: {
                students: recipientType === 'students' ? recipientIds : [],
                colleges: recipientType === 'colleges' ? recipientIds : []
            },
            sender: senderId,
            relatedEvent,
            status: scheduledFor ? 'scheduled' : 'sent',
            scheduledFor,
            sentAt: scheduledFor ? undefined : new Date()
        });
        yield notification.save();
        // TODO: Implement actual push notification service (Firebase, etc.)
        // For now, we'll just save to database
        res.status(201).json({
            success: true,
            message: 'Notification created successfully',
            notification
        });
    }
    catch (error) {
        console.error('Create notification error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.createNotification = createNotification;
// Get notifications sent by college
const getCollegeNotifications = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const notifications = yield Notification_1.default.find({ sender: collegeId })
            .populate('relatedEvent', 'title startDate')
            .sort({ createdAt: -1 });
        res.json({
            success: true,
            notifications
        });
    }
    catch (error) {
        console.error('Get notifications error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getCollegeNotifications = getCollegeNotifications;
// Send notification to event participants
const notifyEventParticipants = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { eventId } = req.params;
        const { title, message, type } = req.body;
        // @ts-ignore
        const senderId = req.college.id;
        // Get event and verify ownership
        const event = yield Event_1.default.findById(eventId);
        if (!event) {
            return res.status(404).json({ message: 'Event not found' });
        }
        if (event.organizer.toString() !== senderId) {
            return res.status(403).json({ message: 'Not authorized' });
        }
        // Get all students registered for this event
        // TODO: Implement EventRegistration model query
        const registeredStudents = []; // Placeholder
        const notification = new Notification_1.default({
            title,
            message,
            type: type || 'event_update',
            recipients: {
                students: registeredStudents,
                colleges: []
            },
            sender: senderId,
            relatedEvent: eventId,
            status: 'sent',
            sentAt: new Date()
        });
        yield notification.save();
        res.json({
            success: true,
            message: 'Notification sent to event participants',
            notification
        });
    }
    catch (error) {
        console.error('Notify event participants error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.notifyEventParticipants = notifyEventParticipants;
// Get notification analytics
const getNotificationAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // @ts-ignore
        const collegeId = req.college.id;
        const analytics = yield Notification_1.default.aggregate([
            { $match: { sender: collegeId } },
            {
                $group: {
                    _id: '$type',
                    count: { $sum: 1 },
                    totalClicks: { $sum: '$clickCount' },
                    avgClicks: { $avg: '$clickCount' }
                }
            }
        ]);
        const totalNotifications = yield Notification_1.default.countDocuments({ sender: collegeId });
        const totalClicks = yield Notification_1.default.aggregate([
            { $match: { sender: collegeId } },
            { $group: { _id: null, total: { $sum: '$clickCount' } } }
        ]);
        res.json({
            success: true,
            analytics: {
                totalNotifications,
                totalClicks: ((_a = totalClicks[0]) === null || _a === void 0 ? void 0 : _a.total) || 0,
                byType: analytics
            }
        });
    }
    catch (error) {
        console.error('Get notification analytics error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.getNotificationAnalytics = getNotificationAnalytics;
// Mark notification as read
const markNotificationAsRead = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { notificationId } = req.params;
        // @ts-ignore
        const userId = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.id) || ((_b = req.college) === null || _b === void 0 ? void 0 : _b.id);
        yield Notification_1.default.findByIdAndUpdate(notificationId, {
            $addToSet: {
                readBy: {
                    user: userId,
                    readAt: new Date()
                }
            }
        });
        res.json({
            success: true,
            message: 'Notification marked as read'
        });
    }
    catch (error) {
        console.error('Mark notification as read error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.markNotificationAsRead = markNotificationAsRead;
// Track notification click
const trackNotificationClick = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { notificationId } = req.params;
        yield Notification_1.default.findByIdAndUpdate(notificationId, { $inc: { clickCount: 1 } });
        res.json({
            success: true,
            message: 'Click tracked'
        });
    }
    catch (error) {
        console.error('Track notification click error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.trackNotificationClick = trackNotificationClick;
