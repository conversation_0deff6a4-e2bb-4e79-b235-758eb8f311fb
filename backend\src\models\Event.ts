import mongoose, { Document, Schema } from 'mongoose';

export interface IEvent extends Document {
  title: string;
  description: string;
  eventType: string;
  startDate: Date;
  endDate: Date;
  registrationDeadline: Date;
  location: {
    address: string;
    coordinates: [number, number]; // [longitude, latitude]
    venue: string;
    city: string;
    state: string;
    pincode: string;
  };
  organizer: mongoose.Types.ObjectId;
  collaborators: mongoose.Types.ObjectId[];
  price: number;
  isFree: boolean;
  maxParticipants?: number;
  currentParticipants: number;
  registrationLink?: string;
  images: string[];
  tags: string[];
  requirements?: string[];
  agenda?: {
    time: string;
    activity: string;
    speaker?: string;
  }[];
  speakers?: {
    name: string;
    designation: string;
    company: string;
    bio: string;
    image?: string;
  }[];
  sponsors?: {
    name: string;
    logo: string;
    website?: string;
    tier: 'platinum' | 'gold' | 'silver' | 'bronze';
  }[];
  status: 'draft' | 'published' | 'ongoing' | 'completed' | 'cancelled';
  isOnline: boolean;
  meetingLink?: string;
  recordingLink?: string;
  certificateTemplate?: string;
  feedback: {
    averageRating: number;
    totalReviews: number;
  };
  analytics: {
    views: number;
    registrations: number;
    attendance: number;
    completionRate: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const EventSchema: Schema = new Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  eventType: { type: String, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  registrationDeadline: { type: Date, required: true },
  location: {
    address: { type: String, required: true },
    coordinates: { type: [Number], index: '2dsphere' },
    venue: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    pincode: { type: String, required: true }
  },
  organizer: { type: Schema.Types.ObjectId, ref: 'College', required: true },
  collaborators: [{ type: Schema.Types.ObjectId, ref: 'College' }],
  price: { type: Number, default: 0 },
  isFree: { type: Boolean, default: true },
  maxParticipants: { type: Number },
  currentParticipants: { type: Number, default: 0 },
  registrationLink: { type: String },
  images: [{ type: String }],
  tags: [{ type: String }],
  requirements: [{ type: String }],
  agenda: [{
    time: { type: String },
    activity: { type: String },
    speaker: { type: String }
  }],
  speakers: [{
    name: { type: String },
    designation: { type: String },
    company: { type: String },
    bio: { type: String },
    image: { type: String }
  }],
  sponsors: [{
    name: { type: String },
    logo: { type: String },
    website: { type: String },
    tier: { type: String, enum: ['platinum', 'gold', 'silver', 'bronze'] }
  }],
  status: {
    type: String,
    enum: ['draft', 'published', 'ongoing', 'completed', 'cancelled'],
    default: 'draft'
  },
  isOnline: { type: Boolean, default: false },
  meetingLink: { type: String },
  recordingLink: { type: String },
  certificateTemplate: { type: String },
  feedback: {
    averageRating: { type: Number, default: 0 },
    totalReviews: { type: Number, default: 0 }
  },
  analytics: {
    views: { type: Number, default: 0 },
    registrations: { type: Number, default: 0 },
    attendance: { type: Number, default: 0 },
    completionRate: { type: Number, default: 0 }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field before saving
EventSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Index for location-based queries
EventSchema.index({ 'location.coordinates': '2dsphere' });
EventSchema.index({ eventType: 1, startDate: 1 });
EventSchema.index({ organizer: 1, status: 1 });

export default mongoose.model<IEvent>('Event', EventSchema);
