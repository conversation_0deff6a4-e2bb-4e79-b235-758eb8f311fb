"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyCollegeByEmail = exports.verifyCollege = exports.getUnverifiedColleges = exports.login = exports.registerStudent = exports.registerCollege = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const College_1 = __importDefault(require("../models/College"));
const Student_1 = __importDefault(require("../models/Student"));
// Register a new college
const registerCollege = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, email, password, address } = req.body;
        // Check if college already exists
        const existingCollege = yield College_1.default.findOne({ email });
        if (existingCollege) {
            return res.status(400).json({
                success: false,
                message: 'College with this email already exists'
            });
        }
        // Hash password
        const salt = yield bcryptjs_1.default.genSalt(10);
        const hashedPassword = yield bcryptjs_1.default.hash(password, salt);
        // Get file paths from multer middleware
        const verificationDocuments = req.files ?
            req.files.map(file => file.path) :
            [];
        // Create new college
        const college = new College_1.default({
            name,
            email,
            password: hashedPassword,
            address,
            verificationDocuments,
            isVerified: false
        });
        yield college.save();
        res.status(201).json({
            success: true,
            message: 'College registered successfully. Awaiting verification.'
        });
    }
    catch (error) {
        next(error);
    }
});
exports.registerCollege = registerCollege;
// Register a new student
const registerStudent = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, email, password, age, collegeName, class: className, adharId, studentCardId } = req.body;
        // Check if student already exists
        const existingStudent = yield Student_1.default.findOne({ email });
        if (existingStudent) {
            return res.status(400).json({
                success: false,
                message: 'Student with this email already exists'
            });
        }
        // Hash password
        const salt = yield bcryptjs_1.default.genSalt(10);
        const hashedPassword = yield bcryptjs_1.default.hash(password, salt);
        // Create new student
        const student = new Student_1.default({
            name,
            email,
            password: hashedPassword,
            age,
            collegeName,
            class: className,
            adharId,
            studentCardId,
            preferences: {
                eventTypes: [],
                maxDistance: 50,
                preferredColleges: []
            }
        });
        yield student.save();
        res.status(201).json({
            success: true,
            message: 'Student registered successfully'
        });
    }
    catch (error) {
        next(error);
    }
});
exports.registerStudent = registerStudent;
// Login user (college or student)
const login = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password, userType } = req.body;
        let user;
        // Find user based on type
        if (userType === 'college') {
            user = yield College_1.default.findOne({ email });
            // Check if college is verified
            if (user && !user.isVerified) {
                return res.status(403).json({
                    success: false,
                    message: 'Your college account is pending verification'
                });
            }
        }
        else {
            user = yield Student_1.default.findOne({ email });
        }
        // Check if user exists
        if (!user) {
            return res.status(400).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        // Check password
        const isMatch = yield bcryptjs_1.default.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({
                success: false,
                message: 'Invalid credentials'
            });
        }
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ id: user._id, userType }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '1d' });
        // Remove password from response
        const userResponse = {
            id: user._id,
            name: user.name,
            email: user.email,
            userType
        };
        res.json({
            success: true,
            token,
            user: userResponse
        });
    }
    catch (error) {
        next(error);
    }
});
exports.login = login;
// Get all unverified colleges (for admin)
const getUnverifiedColleges = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const colleges = yield College_1.default.find({ isVerified: false }).select('-password');
        res.json({
            success: true,
            colleges
        });
    }
    catch (error) {
        next(error);
    }
});
exports.getUnverifiedColleges = getUnverifiedColleges;
// Verify college account
const verifyCollege = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const college = yield College_1.default.findByIdAndUpdate(id, { isVerified: true }, { new: true });
        if (!college) {
            return res.status(404).json({
                success: false,
                message: 'College not found'
            });
        }
        res.json({
            success: true,
            message: 'College verified successfully',
            college: {
                id: college._id,
                name: college.name,
                email: college.email,
                isVerified: college.isVerified
            }
        });
    }
    catch (error) {
        next(error);
    }
});
exports.verifyCollege = verifyCollege;
// Verify college by email (simpler for testing)
const verifyCollegeByEmail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        const college = yield College_1.default.findOneAndUpdate({ email }, { isVerified: true }, { new: true });
        if (!college) {
            return res.status(404).json({
                success: false,
                message: 'College not found'
            });
        }
        res.json({
            success: true,
            message: 'College verified successfully',
            college: {
                id: college._id,
                name: college.name,
                email: college.email,
                isVerified: college.isVerified
            }
        });
    }
    catch (error) {
        next(error);
    }
});
exports.verifyCollegeByEmail = verifyCollegeByEmail;
