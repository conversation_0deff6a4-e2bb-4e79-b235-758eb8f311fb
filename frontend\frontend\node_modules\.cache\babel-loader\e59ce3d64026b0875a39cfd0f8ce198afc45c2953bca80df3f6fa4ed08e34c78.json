{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\Register.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold mb-6\",\n      children: \"Choose Registration Type\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register/student\",\n        className: \"bg-blue-500 text-white py-3 px-4 rounded hover:bg-blue-600\",\n        children: \"Register as Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register/college\",\n        className: \"bg-green-500 text-white py-3 px-4 rounded hover:bg-green-600\",\n        children: \"Register as College\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-gray-600\",\n        children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"text-blue-500 hover:underline\",\n          children: \"Login here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 36\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Register", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Register: React.FC = () => {\n  return (\n    <div className=\"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center\">\n      <h2 className=\"text-2xl font-bold mb-6\">Choose Registration Type</h2>\n      \n      <div className=\"flex flex-col space-y-4\">\n        <Link \n          to=\"/register/student\" \n          className=\"bg-blue-500 text-white py-3 px-4 rounded hover:bg-blue-600\"\n        >\n          Register as Student\n        </Link>\n        \n        <Link \n          to=\"/register/college\" \n          className=\"bg-green-500 text-white py-3 px-4 rounded hover:bg-green-600\"\n        >\n          Register as College\n        </Link>\n        \n        <div className=\"mt-4 text-gray-600\">\n          Already have an account? <Link to=\"/login\" className=\"text-blue-500 hover:underline\">Login here</Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBACnFH,OAAA;MAAIE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErEP,OAAA;MAAKE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCH,OAAA,CAACF,IAAI;QACHU,EAAE,EAAC,mBAAmB;QACtBN,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPP,OAAA,CAACF,IAAI;QACHU,EAAE,EAAC,mBAAmB;QACtBN,SAAS,EAAC,8DAA8D;QAAAC,QAAA,EACzE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPP,OAAA;QAAKE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAAC,2BACT,eAAAH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA1BIR,QAAkB;AA4BxB,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}