{"name": "is-root", "version": "2.1.0", "description": "Check if the process is running as root user, for example, one started with `sudo`", "license": "MIT", "repository": "sindresorhus/is-root", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["sudo", "root", "user", "permissions", "uid", "process", "posix"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}