import { Request, Response } from 'express';
import Collaboration from '../models/Collaboration';
import College from '../models/College';
import Event from '../models/Event';
import Notification from '../models/Notification';

// Send collaboration request
export const sendCollaborationRequest = async (req: Request, res: Response) => {
  try {
    const {
      requesteeId,
      eventId,
      proposedEvent,
      message,
      terms
    } = req.body;

    // @ts-ignore
    const requesterId = req.college.id;

    // Check if requestee college exists
    const requesteeCollege = await College.findById(requesteeId);
    if (!requesteeCollege) {
      return res.status(404).json({ message: 'Requestee college not found' });
    }

    // Check if collaboration already exists
    const existingCollaboration = await Collaboration.findOne({
      requester: requesterId,
      requestee: requesteeId,
      status: 'pending'
    });

    if (existingCollaboration) {
      return res.status(400).json({ 
        message: 'Collaboration request already pending with this college' 
      });
    }

    const collaboration = new Collaboration({
      requester: requesterId,
      requestee: requesteeId,
      eventId,
      proposedEvent,
      message,
      terms,
      status: 'pending'
    });

    await collaboration.save();

    // Update college collaboration requests
    await College.findByIdAndUpdate(requesterId, {
      $addToSet: { 'collaborationRequests.sent': collaboration._id }
    });

    await College.findByIdAndUpdate(requesteeId, {
      $addToSet: { 'collaborationRequests.received': collaboration._id }
    });

    // Send notification to requestee
    const notification = new Notification({
      title: 'New Collaboration Request',
      message: `You have received a collaboration request from ${req.college.name}`,
      type: 'collaboration_request',
      recipients: {
        students: [],
        colleges: [requesteeId]
      },
      sender: requesterId,
      status: 'sent',
      sentAt: new Date()
    });

    await notification.save();

    res.status(201).json({
      success: true,
      message: 'Collaboration request sent successfully',
      collaboration
    });
  } catch (error) {
    console.error('Send collaboration request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get collaboration requests (sent and received)
export const getCollaborationRequests = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;
    const { type } = req.query; // 'sent' or 'received'

    let query: any = {};
    
    if (type === 'sent') {
      query.requester = collegeId;
    } else if (type === 'received') {
      query.requestee = collegeId;
    } else {
      query = {
        $or: [
          { requester: collegeId },
          { requestee: collegeId }
        ]
      };
    }

    const collaborations = await Collaboration.find(query)
      .populate('requester', 'name email')
      .populate('requestee', 'name email')
      .populate('eventId', 'title startDate')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      collaborations
    });
  } catch (error) {
    console.error('Get collaboration requests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Respond to collaboration request
export const respondToCollaborationRequest = async (req: Request, res: Response) => {
  try {
    const { collaborationId } = req.params;
    const { status, response } = req.body; // 'accepted' or 'rejected'

    // @ts-ignore
    const collegeId = req.college.id;

    const collaboration = await Collaboration.findById(collaborationId);
    if (!collaboration) {
      return res.status(404).json({ message: 'Collaboration request not found' });
    }

    // Check if the college is the requestee
    if (collaboration.requestee.toString() !== collegeId) {
      return res.status(403).json({ message: 'Not authorized to respond to this request' });
    }

    // Check if already responded
    if (collaboration.status !== 'pending') {
      return res.status(400).json({ message: 'Request already responded to' });
    }

    collaboration.status = status;
    collaboration.response = response;
    collaboration.respondedAt = new Date();

    await collaboration.save();

    // If accepted, add to event collaborators
    if (status === 'accepted' && collaboration.eventId) {
      await Event.findByIdAndUpdate(collaboration.eventId, {
        $addToSet: { collaborators: collegeId }
      });
    }

    // Send notification to requester
    const notification = new Notification({
      title: `Collaboration Request ${status.charAt(0).toUpperCase() + status.slice(1)}`,
      message: `Your collaboration request has been ${status}`,
      type: 'collaboration_request',
      recipients: {
        students: [],
        colleges: [collaboration.requester]
      },
      sender: collegeId,
      status: 'sent',
      sentAt: new Date()
    });

    await notification.save();

    res.json({
      success: true,
      message: `Collaboration request ${status}`,
      collaboration
    });
  } catch (error) {
    console.error('Respond to collaboration request error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get collaboration analytics
export const getCollaborationAnalytics = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    const sentRequests = await Collaboration.countDocuments({ requester: collegeId });
    const receivedRequests = await Collaboration.countDocuments({ requestee: collegeId });
    const acceptedSent = await Collaboration.countDocuments({ 
      requester: collegeId, 
      status: 'accepted' 
    });
    const acceptedReceived = await Collaboration.countDocuments({ 
      requestee: collegeId, 
      status: 'accepted' 
    });

    const activeCollaborations = await Collaboration.countDocuments({
      $or: [
        { requester: collegeId },
        { requestee: collegeId }
      ],
      status: 'accepted'
    });

    res.json({
      success: true,
      analytics: {
        sentRequests,
        receivedRequests,
        acceptedSent,
        acceptedReceived,
        activeCollaborations,
        successRate: sentRequests > 0 ? (acceptedSent / sentRequests) * 100 : 0
      }
    });
  } catch (error) {
    console.error('Get collaboration analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get potential collaboration partners
export const getPotentialPartners = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const collegeId = req.college.id;

    // Get colleges that are verified and not the current college
    const potentialPartners = await College.find({
      _id: { $ne: collegeId },
      isVerified: true
    }).select('name email address departments totalStudents establishedYear');

    res.json({
      success: true,
      partners: potentialPartners
    });
  } catch (error) {
    console.error('Get potential partners error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
