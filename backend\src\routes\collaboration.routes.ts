import express from 'express';
import {
  sendCollaborationRequest,
  getCollaborationRequests,
  respondToCollaborationRequest,
  getCollaborationAnalytics,
  getPotentialPartners
} from '../controllers/collaboration.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Collaboration routes (protected)
router.post('/request', authMiddleware, sendCollaborationRequest);
router.get('/requests', authMiddleware, getCollaborationRequests);
router.put('/requests/:collaborationId/respond', authMiddleware, respondToCollaborationRequest);
router.get('/analytics', authMiddleware, getCollaborationAnalytics);
router.get('/partners', authMiddleware, getPotentialPartners);

export default router;
