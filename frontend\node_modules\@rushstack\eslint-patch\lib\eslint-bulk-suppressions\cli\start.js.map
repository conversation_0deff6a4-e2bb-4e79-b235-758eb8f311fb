{"version": 3, "file": "start.js", "sourceRoot": "", "sources": ["../../../src/eslint-bulk-suppressions/cli/start.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,mCAAqC;AACrC,yCAA2C;AAC3C,2DAAsD;AACtD,mDAA+C;AAE/C,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;IAClG,IAAA,sBAAS,GAAE,CAAC;IACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAA,sBAAS,GAAE,CAAC;IACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,IAAI,CAAC,IAAA,6BAAY,EAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IACjC,OAAO,CAAC,KAAK,CACX,iHAAiH,CAClH,CAAC;IACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,GAAW,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,cAA6B,CAAC;AAClC,QAAQ,UAAU,EAAE,CAAC;IACnB,KAAK,UAAU,CAAC,CAAC,CAAC;QAChB,cAAc,GAAG,IAAA,wBAAa,GAAE,CAAC;QACjC,MAAM;IACR,CAAC;IAED,KAAK,OAAO,CAAC,CAAC,CAAC;QACb,cAAc,GAAG,IAAA,kBAAU,GAAE,CAAC;QAC9B,MAAM;IACR,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;QACR,OAAO,CAAC,KAAK,CAAC,8CAA8C,GAAG,UAAU,CAAC,CAAC;QAC3E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;IACzB,IAAI,CAAC,YAAY,KAAK,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,CAAC;AACV,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { pruneAsync } from './prune';\nimport { suppressAsync } from './suppress';\nimport { isCorrectCwd } from './utils/is-correct-cwd';\nimport { printHelp } from './utils/print-help';\n\nif (process.argv.includes('-h') || process.argv.includes('-H') || process.argv.includes('--help')) {\n  printHelp();\n  process.exit(0);\n}\n\nif (process.argv.length < 3) {\n  printHelp();\n  process.exit(1);\n}\n\nif (!isCorrectCwd(process.cwd())) {\n  console.error(\n    '@rushstack/eslint-bulk: Please call this command from the directory that contains .eslintrc.js or .eslintrc.cjs'\n  );\n  process.exit(1);\n}\n\nconst subcommand: string = process.argv[2];\nlet processPromise: Promise<void>;\nswitch (subcommand) {\n  case 'suppress': {\n    processPromise = suppressAsync();\n    break;\n  }\n\n  case 'prune': {\n    processPromise = pruneAsync();\n    break;\n  }\n\n  default: {\n    console.error('@rushstack/eslint-bulk: Unknown subcommand: ' + subcommand);\n    process.exit(1);\n  }\n}\n\nprocessPromise.catch((e) => {\n  if (e instanceof Error) {\n    console.error(e.message);\n    process.exit(1);\n  }\n\n  throw e;\n});\n"]}