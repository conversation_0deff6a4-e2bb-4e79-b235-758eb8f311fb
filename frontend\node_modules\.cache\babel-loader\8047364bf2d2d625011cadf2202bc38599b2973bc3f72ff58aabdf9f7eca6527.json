{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { BrowserRouter } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 12,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/workuuu/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { BrowserRouter } from 'react-router-dom';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <App />\n    </BrowserRouter>\n  </React.StrictMode>\n);\n\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,aAAa,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,IAAI,GAAGL,QAAQ,CAACM,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACL,KAAK,CAACW,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACF,aAAa;IAAAS,QAAA,eACZP,OAAA,CAACH,GAAG;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}