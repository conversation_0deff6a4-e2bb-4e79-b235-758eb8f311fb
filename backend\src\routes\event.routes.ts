import express from 'express';
import {
  createEvent,
  getAllEvents,
  getEventById,
  updateEvent,
  deleteEvent,
  getCollegeEvents,
  getEventAnalytics,
  upload
} from '../controllers/event.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Public event routes
router.get('/', getAllEvents);
router.get('/:id', getEventById);

// Protected event routes
router.post('/', authMiddleware, upload.array('images', 5), createEvent);
router.put('/:id', authMiddleware, upload.array('images', 5), updateEvent);
router.delete('/:id', authMiddleware, deleteEvent);

// College-specific routes
router.get('/college/my-events', authMiddleware, getCollegeEvents);
router.get('/college/analytics', authMiddleware, getEventAnalytics);

export default router;