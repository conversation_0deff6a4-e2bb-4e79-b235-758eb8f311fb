import express from 'express';
import { 
  createEvent, 
  getAllEvents, 
  getEventById, 
  updateEvent, 
  deleteEvent 
} from '../controllers/event.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = express.Router();

// Event routes
router.post('/', authMiddleware, createEvent);
router.get('/', getAllEvents);
router.get('/:id', getEventById);
router.put('/:id', authMiddleware, updateEvent);
router.delete('/:id', authMiddleware, deleteEvent);

export default router;