{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\workuuu\\\\frontend\\\\frontend\\\\src\\\\pages\\\\Register.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Choose Registration Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select your account type to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-options\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register/student\",\n          className: \"register-option student-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"option-icon\",\n            children: \"\\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Register as Student\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Join events at your college and discover new opportunities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register/college\",\n          className: \"register-option college-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"option-icon\",\n            children: \"\\uD83C\\uDFEB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Register as College\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Create and manage events for your institution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-footer\",\n        children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"login-link\",\n          children: \"Login here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 36\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Register", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Register: React.FC = () => {\n  return (\n    <div className=\"register-container\">\n      <div className=\"register-box\">\n        <div className=\"register-header\">\n          <h2>Choose Registration Type</h2>\n          <p>Select your account type to get started</p>\n        </div>\n\n        <div className=\"register-options\">\n          <Link to=\"/register/student\" className=\"register-option student-option\">\n            <div className=\"option-icon\">🎓</div>\n            <h3>Register as Student</h3>\n            <p>Join events at your college and discover new opportunities</p>\n          </Link>\n\n          <Link to=\"/register/college\" className=\"register-option college-option\">\n            <div className=\"option-icon\">🏫</div>\n            <h3>Register as College</h3>\n            <p>Create and manage events for your institution</p>\n          </Link>\n        </div>\n\n        <div className=\"register-footer\">\n          Already have an account? <Link to=\"/login\" className=\"login-link\">Login here</Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCH,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BH,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BH,OAAA;UAAAG,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCP,OAAA;UAAAG,QAAA,EAAG;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,mBAAmB;UAACN,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBACrEH,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCP,OAAA;YAAAG,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BP,OAAA;YAAAG,QAAA,EAAG;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAEPP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,mBAAmB;UAACN,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBACrEH,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCP,OAAA;YAAAG,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BP,OAAA;YAAAG,QAAA,EAAG;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,2BACN,eAAAH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA7BIR,QAAkB;AA+BxB,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}