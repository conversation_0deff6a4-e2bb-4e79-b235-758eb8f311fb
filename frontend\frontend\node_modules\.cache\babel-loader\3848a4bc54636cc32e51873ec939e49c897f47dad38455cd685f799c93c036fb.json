{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/workuuu/frontend/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{login}from'../services/auth';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{const navigate=useNavigate();const[formData,setFormData]=useState({email:'',password:'',userType:'student'});const[error,setError]=useState('');const handleChange=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const handleSubmit=async e=>{e.preventDefault();setError('');try{await login(formData);navigate('/dashboard');}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Login failed. Please try again.');}};return/*#__PURE__*/_jsx(\"div\",{className:\"auth-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-box\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Welcome Back\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Sign in to your account\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-container\",children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"auth-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleChange,placeholder:\"Enter your email\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",name:\"password\",value:formData.password,onChange:handleChange,placeholder:\"Enter your password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"userType\",children:\"Account Type\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"userType\",name:\"userType\",value:formData.userType,onChange:handleChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"student\",children:\"Student\"}),/*#__PURE__*/_jsx(\"option\",{value:\"college\",children:\"College\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"auth-btn\",children:\"Sign In\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"auth-footer\",children:[\"Don't have an account? \",/*#__PURE__*/_jsx(\"a\",{href:\"/register\",className:\"auth-link\",children:\"Register here\"})]})]})]})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "login", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "navigate", "formData", "setFormData", "email", "password", "userType", "error", "setError", "handleChange", "e", "_objectSpread", "target", "name", "value", "handleSubmit", "preventDefault", "err", "_err$response", "_err$response$data", "response", "data", "message", "className", "children", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "href"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { login } from '../services/auth';\n\nconst Login: React.FC = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    userType: 'student' as 'student' | 'college'\n  });\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await login(formData);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-box\">\n        <div className=\"auth-header\">\n          <h1>Welcome Back</h1>\n          <p>Sign in to your account</p>\n        </div>\n\n        <div className=\"form-container\">\n          {error && <div className=\"error-message\">{error}</div>}\n\n          <form onSubmit={handleSubmit} className=\"auth-form\">\n            <div className=\"form-group\">\n              <label htmlFor=\"email\">Email Address</label>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"Enter your email\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"password\">Password</label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                placeholder=\"Enter your password\"\n                required\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"userType\">Account Type</label>\n              <select\n                id=\"userType\"\n                name=\"userType\"\n                value={formData.userType}\n                onChange={handleChange}\n                required\n              >\n                <option value=\"student\">Student</option>\n                <option value=\"college\">College</option>\n              </select>\n            </div>\n\n            <button type=\"submit\" className=\"auth-btn\">\n              Sign In\n            </button>\n          </form>\n\n          <div className=\"auth-footer\">\n            Don't have an account? <a href=\"/register\" className=\"auth-link\">Register here</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;"], "mappings": "6HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,KAAK,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,CACvCW,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAAgB,YAAY,CAAIC,CAA0D,EAAK,CACnFP,WAAW,CAAAQ,aAAA,CAAAA,aAAA,IACNT,QAAQ,MACX,CAACQ,CAAC,CAACE,MAAM,CAACC,IAAI,EAAGH,CAAC,CAACE,MAAM,CAACE,KAAK,EAChC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAL,CAAkB,EAAK,CACjDA,CAAC,CAACM,cAAc,CAAC,CAAC,CAClBR,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAb,KAAK,CAACO,QAAQ,CAAC,CACrBD,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOgB,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBX,QAAQ,CAAC,EAAAU,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcG,IAAI,UAAAF,kBAAA,iBAAlBA,kBAAA,CAAoBG,OAAO,GAAI,iCAAiC,CAAC,CAC5E,CACF,CAAC,CAED,mBACEzB,IAAA,QAAK0B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BzB,KAAA,QAAKwB,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBzB,KAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3B,IAAA,OAAA2B,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB3B,IAAA,MAAA2B,QAAA,CAAG,yBAAuB,CAAG,CAAC,EAC3B,CAAC,cAENzB,KAAA,QAAKwB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5BjB,KAAK,eAAIV,IAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEjB,KAAK,CAAM,CAAC,cAEtDR,KAAA,SAAM0B,QAAQ,CAAEV,YAAa,CAACQ,SAAS,CAAC,WAAW,CAAAC,QAAA,eACjDzB,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAO6B,OAAO,CAAC,OAAO,CAAAF,QAAA,CAAC,eAAa,CAAO,CAAC,cAC5C3B,IAAA,UACE8B,IAAI,CAAC,OAAO,CACZC,EAAE,CAAC,OAAO,CACVf,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEZ,QAAQ,CAACE,KAAM,CACtByB,QAAQ,CAAEpB,YAAa,CACvBqB,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAENhC,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAO6B,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1C3B,IAAA,UACE8B,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACbf,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEZ,QAAQ,CAACG,QAAS,CACzBwB,QAAQ,CAAEpB,YAAa,CACvBqB,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENhC,KAAA,QAAKwB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3B,IAAA,UAAO6B,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,cAAY,CAAO,CAAC,cAC9CzB,KAAA,WACE6B,EAAE,CAAC,UAAU,CACbf,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEZ,QAAQ,CAACI,QAAS,CACzBuB,QAAQ,CAAEpB,YAAa,CACvBsB,QAAQ,MAAAP,QAAA,eAER3B,IAAA,WAAQiB,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC3B,IAAA,WAAQiB,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,EACN,CAAC,cAEN3B,IAAA,WAAQ8B,IAAI,CAAC,QAAQ,CAACJ,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,SAE3C,CAAQ,CAAC,EACL,CAAC,cAEPzB,KAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,yBACJ,cAAA3B,IAAA,MAAGmC,IAAI,CAAC,WAAW,CAACT,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,EAC/E,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}