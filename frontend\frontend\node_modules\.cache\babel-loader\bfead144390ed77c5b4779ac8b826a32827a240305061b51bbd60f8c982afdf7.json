{"ast": null, "code": "import React from'react';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsxs(\"div\",{className:\"app-container\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"College Events Platform\"}),/*#__PURE__*/_jsx(\"nav\",{children:/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"/\",children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"/events\",children:\"Events\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"/login\",children:\"Login\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"/register\",children:\"Register\"})})]})})]}),/*#__PURE__*/_jsxs(\"main\",{className:\"main\",children:[/*#__PURE__*/_jsxs(\"section\",{className:\"hero\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Find and Join College Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Connect with events happening at colleges near you\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn primary\",children:\"Browse Events\"}),/*#__PURE__*/_jsx(\"button\",{className:\"btn secondary\",children:\"Create Account\"})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"features\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Discover Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Find events based on your interests and location\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Connect with Peers\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Meet students with similar interests\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Manage Events\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Create and manage your own events\"})]})]})]}),/*#__PURE__*/_jsx(\"footer\",{className:\"footer\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2023 College Events Platform\"})})]});}export default App;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "App", "className", "children", "href"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"app-container\">\n      <header className=\"header\">\n        <h1>College Events Platform</h1>\n        <nav>\n          <ul>\n            <li><a href=\"/\">Home</a></li>\n            <li><a href=\"/events\">Events</a></li>\n            <li><a href=\"/login\">Login</a></li>\n            <li><a href=\"/register\">Register</a></li>\n          </ul>\n        </nav>\n      </header>\n      \n      <main className=\"main\">\n        <section className=\"hero\">\n          <h2>Find and Join College Events</h2>\n          <p>Connect with events happening at colleges near you</p>\n          <div className=\"buttons\">\n            <button className=\"btn primary\">Browse Events</button>\n            <button className=\"btn secondary\">Create Account</button>\n          </div>\n        </section>\n        \n        <section className=\"features\">\n          <div className=\"feature\">\n            <h3>Discover Events</h3>\n            <p>Find events based on your interests and location</p>\n          </div>\n          <div className=\"feature\">\n            <h3>Connect with <PERSON>eers</h3>\n            <p>Meet students with similar interests</p>\n          </div>\n          <div className=\"feature\">\n            <h3>Manage Events</h3>\n            <p>Create and manage your own events</p>\n          </div>\n        </section>\n      </main>\n      \n      <footer className=\"footer\">\n        <p>&copy; 2023 College Events Platform</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,KAAA,QAAKE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BH,KAAA,WAAQE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACxBL,IAAA,OAAAK,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCL,IAAA,QAAAK,QAAA,cACEH,KAAA,OAAAG,QAAA,eACEL,IAAA,OAAAK,QAAA,cAAIL,IAAA,MAAGM,IAAI,CAAC,GAAG,CAAAD,QAAA,CAAC,MAAI,CAAG,CAAC,CAAI,CAAC,cAC7BL,IAAA,OAAAK,QAAA,cAAIL,IAAA,MAAGM,IAAI,CAAC,SAAS,CAAAD,QAAA,CAAC,QAAM,CAAG,CAAC,CAAI,CAAC,cACrCL,IAAA,OAAAK,QAAA,cAAIL,IAAA,MAAGM,IAAI,CAAC,QAAQ,CAAAD,QAAA,CAAC,OAAK,CAAG,CAAC,CAAI,CAAC,cACnCL,IAAA,OAAAK,QAAA,cAAIL,IAAA,MAAGM,IAAI,CAAC,WAAW,CAAAD,QAAA,CAAC,UAAQ,CAAG,CAAC,CAAI,CAAC,EACvC,CAAC,CACF,CAAC,EACA,CAAC,cAETH,KAAA,SAAME,SAAS,CAAC,MAAM,CAAAC,QAAA,eACpBH,KAAA,YAASE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACvBL,IAAA,OAAAK,QAAA,CAAI,8BAA4B,CAAI,CAAC,cACrCL,IAAA,MAAAK,QAAA,CAAG,oDAAkD,CAAG,CAAC,cACzDH,KAAA,QAAKE,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBL,IAAA,WAAQI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,eAAa,CAAQ,CAAC,cACtDL,IAAA,WAAQI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAQ,CAAC,EACtD,CAAC,EACC,CAAC,cAEVH,KAAA,YAASE,SAAS,CAAC,UAAU,CAAAC,QAAA,eAC3BH,KAAA,QAAKE,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBL,IAAA,OAAAK,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBL,IAAA,MAAAK,QAAA,CAAG,kDAAgD,CAAG,CAAC,EACpD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBL,IAAA,OAAAK,QAAA,CAAI,oBAAkB,CAAI,CAAC,cAC3BL,IAAA,MAAAK,QAAA,CAAG,sCAAoC,CAAG,CAAC,EACxC,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBL,IAAA,OAAAK,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBL,IAAA,MAAAK,QAAA,CAAG,mCAAiC,CAAG,CAAC,EACrC,CAAC,EACC,CAAC,EACN,CAAC,cAEPL,IAAA,WAAQI,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACxBL,IAAA,MAAAK,QAAA,CAAG,mCAAmC,CAAG,CAAC,CACpC,CAAC,EACN,CAAC,CAEV,CAEA,cAAe,CAAAF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}