import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check if user is logged in
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      navigate('/login');
      return;
    }
    
    setUser(JSON.parse(storedUser));
  }, [navigate]);

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container">
      <h1>Dashboard</h1>
      <p>Welcome, {user.name}!</p>
      <div>
        {user.userType === 'college' ? (
          <div>
            <h2>College Dashboard</h2>
            <p>Manage your events and profile here.</p>
          </div>
        ) : (
          <div>
            <h2>Student Dashboard</h2>
            <p>Discover events and manage your preferences here.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;

