{"version": 3, "file": "static/css/main.0a56aff8.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,KACE,iBACF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,cACF,CAEA,gBAIE,qBAAsB,CACtB,iBAAkB,CAClB,8BAAwC,CAJxC,aAAc,CADd,eAAgB,CAEhB,YAIF,CAEA,YACE,kBACF,CAEA,kBACE,aAAc,CAEd,eAAiB,CADjB,iBAEF,CAEA,0DAKE,qBAAsB,CACtB,iBAAkB,CAFlB,WAAY,CADZ,UAIF,CAEA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CACf,eAAiB,CAJjB,iBAKF,CAEA,aACE,wBAAyB,CACzB,UACF,CAEA,eACE,wBAAyB,CACzB,UACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n\n", ".App {\n  text-align: center;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.form-container {\n  max-width: 500px;\n  margin: 0 auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\n.btn {\n  padding: 10px 15px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: bold;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n\n"], "names": [], "sourceRoot": ""}