"use strict";function r(r){return r&&"object"==typeof r&&"default"in r?r:{default:r}}var e=r(require("postcss")),t=(r,t)=>{const o="rule"===Object(r.parent).type?r.parent.cloneBefore({raws:{}}).removeAll():e.default.rule({selector:"&"});return o.selectors=o.selectors.map((r=>`${r}:dir(${t})`)),o};const o=/^border-(block|block-start|block-end|inline|inline-start|inline-end)(-(width|style|color))?$/i;var l=(r,e,t,l)=>{r.cloneBefore({prop:`border-top${r.prop.replace(o,"$2")}`,value:e[0]}),r.cloneBefore({prop:`border-bottom${r.prop.replace(o,"$2")}`,value:e[1]||e[0]}),b(r,l)},n=(r,e,t,l)=>{r.cloneBefore({prop:`border-top${r.prop.replace(o,"$2")}`}),b(r,l)},i=(r,e,t,l)=>{r.cloneBefore({prop:`border-bottom${r.prop.replace(o,"$2")}`}),b(r,l)},d=(r,e,l,n)=>{const i=()=>[r.cloneBefore({prop:`border-left${r.prop.replace(o,"$2")}`,value:e[0]}),r.cloneBefore({prop:`border-right${r.prop.replace(o,"$2")}`,value:e[1]||e[0]})],d=()=>[r.clone({prop:`border-right${r.prop.replace(o,"$2")}`,value:e[0]}),r.clone({prop:`border-left${r.prop.replace(o,"$2")}`,value:e[1]||e[0]})];return 1===e.length||2===e.length&&e[0]===e[1]||"ltr"===l?(i(),void b(r,n)):"rtl"===l?(d(),void b(r,n)):(t(r,"ltr").append(i()),t(r,"rtl").append(d()),void b(r,n))},p=(r,e,l,n)=>{const i=()=>r.cloneBefore({prop:`border-left${r.prop.replace(o,"$2")}`}),d=()=>r.cloneBefore({prop:`border-right${r.prop.replace(o,"$2")}`});return"ltr"===l?(i(),void b(r,n)):"rtl"===l?(d(),void b(r,n)):(t(r,"ltr").append(i()),t(r,"rtl").append(d()),void b(r,n))},a=(r,e,l,n)=>{const i=()=>r.cloneBefore({prop:`border-right${r.prop.replace(o,"$2")}`}),d=()=>r.cloneBefore({prop:`border-left${r.prop.replace(o,"$2")}`});return"ltr"===l?(i(),void b(r,n)):"rtl"===l?(d(),void b(r,n)):(t(r,"ltr").append(i()),t(r,"rtl").append(d()),void b(r,n))};function b(r,e){e||r.remove()}const c=/^(border-)(end-end|end-start|start-end|start-start)(-radius)$/i,s={"end-end":"bottom-right","end-start":"bottom-left","start-end":"top-right","start-start":"top-left"},g={"end-end":"bottom-left","end-start":"bottom-right","start-end":"top-left","start-start":"top-right"};var f=(r,e,o,l)=>"ltr"===o?(u(r),void v(r,l)):"rtl"===o?(h(r),void v(r,l)):(t(r,"ltr").append(u(r)),t(r,"rtl").append(h(r)),void v(r,l));function u(r){return r.cloneBefore({prop:r.prop.replace(c,((r,e,t,o)=>`${e}${s[t]}${o}`))})}function h(r){return r.cloneBefore({prop:r.prop.replace(c,((r,e,t,o)=>`${e}${g[t]}${o}`))})}function v(r,e){e||r.remove()}var m=r=>{const e=r.slice();return 4===e.length&&e[3]===e[1]&&e.pop(),3===e.length&&e[2]===e[0]&&e.pop(),2===e.length&&e[1]===e[0]&&e.pop(),e},k=(r,e,o,l)=>{if("logical"!==e[0])return null;const[,n,i,d,p]=e,a=m([n,p||i||n,d||n,i||n]),b=()=>r.cloneBefore({value:a.join(" ")});if(a.length<4||"ltr"===o)return b(),void $(r,l);const c=m([n,i||n,d||n,p||i||n]),s=()=>r.cloneBefore({value:c.join(" ")});if("rtl"===o)return s(),void $(r,l);t(r,"ltr").append(b()),t(r,"rtl").append(s()),$(r,l)};function $(r,e){e||r.remove()}var B=(r,e,o,l)=>/^inline-start$/i.test(r.value)?"ltr"===o?(y(r),void j(r,l)):"rtl"===o?(w(r),void j(r,l)):(t(r,"ltr").append(y(r)),t(r,"rtl").append(w(r)),void j(r,l)):/^inline-end$/i.test(r.value)?"ltr"===o?(w(r),void j(r,l)):"rtl"===o?(y(r),void j(r,l)):(t(r,"ltr").append(w(r)),t(r,"rtl").append(y(r)),void j(r,l)):void 0;function y(r){return r.cloneBefore({value:"left"})}function w(r){return r.cloneBefore({value:"right"})}function j(r,e){e||r.remove()}var z=(r,e,o,l)=>{if("logical"!==e[0])return r.cloneBefore({prop:"top",value:e[0]}),r.cloneBefore({prop:"right",value:e[1]||e[0]}),r.cloneBefore({prop:"bottom",value:e[2]||e[0]}),r.cloneBefore({prop:"left",value:e[3]||e[1]||e[0]}),void O(r,l);return!e[4]||e[4]===e[2]||"ltr"===o?(x(r,e),void O(r,l)):"rtl"===o?(E(r,e),void O(r,l)):(t(r,"ltr").append(x(r,e)),t(r,"rtl").append(E(r,e)),void O(r,l))};function x(r,e){return[r.cloneBefore({prop:"top",value:e[1]}),r.cloneBefore({prop:"left",value:e[2]||e[1]}),r.cloneBefore({prop:"bottom",value:e[3]||e[1]}),r.cloneBefore({prop:"right",value:e[4]||e[2]||e[1]})]}function E(r,e){return[r.cloneBefore({prop:"top",value:e[1]}),r.cloneBefore({prop:"right",value:e[2]||e[1]}),r.cloneBefore({prop:"bottom",value:e[3]||e[1]}),r.cloneBefore({prop:"left",value:e[4]||e[2]||e[1]})]}function O(r,e){e||r.remove()}var q=(r,e,t,o)=>/^block$/i.test(r.value)?(r.cloneBefore({value:"vertical"}),void A(r,o)):/^inline$/i.test(r.value)?(r.cloneBefore({value:"horizontal"}),void A(r,o)):void 0;function A(r,e){e||r.remove()}var D=/^(inset|margin|padding)(?:-(block|block-start|block-end|inline|inline-start|inline-end|start|end))$/i,P=/^inset-/i,C=(r,e,t)=>r.cloneBefore({prop:`${r.prop.replace(D,"$1")}${e}`.replace(P,""),value:t}),F=(r,e,t,o)=>{C(r,"-top",e[0]),C(r,"-bottom",e[1]||e[0]),L(r,o)},G=(r,e,t,o)=>{r.cloneBefore({prop:r.prop.replace(D,"$1-top").replace(P,"")}),L(r,o)},H=(r,e,t,o)=>{r.cloneBefore({prop:r.prop.replace(D,"$1-bottom").replace(P,"")}),L(r,o)},I=(r,e,o,l)=>{const n=()=>[C(r,"-left",e[0]),C(r,"-right",e[1]||e[0])],i=()=>[C(r,"-right",e[0]),C(r,"-left",e[1]||e[0])];return 1===e.length||2===e.length&&e[0]===e[1]||"ltr"===o?(n(),void L(r,l)):"rtl"===o?(i(),void L(r,l)):(t(r,"ltr").append(n()),t(r,"rtl").append(i()),void L(r,l))},J=(r,e,o,l)=>{const n=()=>C(r,"-left",r.value),i=()=>C(r,"-right",r.value);return"ltr"===o?(n(),void L(r,l)):"rtl"===o?(i(),void L(r,l)):(t(r,"ltr").append(n()),t(r,"rtl").append(i()),void L(r,l))},K=(r,e,o,l)=>{const n=()=>C(r,"-right",r.value),i=()=>C(r,"-left",r.value);return"ltr"===o?(n(),void L(r,l)):"rtl"===o?(i(),void L(r,l)):(t(r,"ltr").append(n()),t(r,"rtl").append(i()),void L(r,l))};function L(r,e){e||r.remove()}var M=/^(min-|max-)?(block|inline)-(size)$/i,N=(r,e,t,o)=>{r.cloneBefore({prop:r.prop.replace(M,((r,e,t)=>`${e||""}${"block"===t?"height":"width"}`))}),o||r.remove()},Q=(r,e,o,l)=>/^start$/i.test(r.value)?"ltr"===o?(R(r),void T(r,l)):"rtl"===o?(S(r),void T(r,l)):(t(r,"ltr").append(R(r)),t(r,"rtl").append(S(r)),void T(r,l)):/^end$/i.test(r.value)?"ltr"===o?(S(r),void T(r,l)):"rtl"===o?(R(r),void T(r,l)):(t(r,"ltr").append(S(r)),t(r,"rtl").append(R(r)),void T(r,l)):void 0;function R(r){return r.cloneBefore({value:"left"})}function S(r){return r.cloneBefore({value:"right"})}function T(r,e){e||r.remove()}function U(r,e){return V(r,/^\s$/,e)}function V(r,e,t){const o=[];let l="",n=!1,i=0,d=-1;for(;++d<r.length;){const p=r[d];"("===p?i+=1:")"===p?i>0&&(i-=1):0===i&&e.test(p)&&(n=!0),n?(t&&!l.trim()||o.push(t?l.trim():l),t||o.push(p),l="",n=!1):l+=p}return""!==l&&o.push(t?l.trim():l),o}var W=(r,e,o,l)=>{const n=[],i=[];var d,p;return(d=r.value,V(d,/^,$/,p)).forEach((r=>{let e=!1;U(r).forEach(((r,t,o)=>{r in X&&(e=!0,X[r].ltr.forEach((r=>{const e=o.slice();e.splice(t,1,r),n.length&&!/^,$/.test(n[n.length-1])&&n.push(","),n.push(e.join(""))})),X[r].rtl.forEach((r=>{const e=o.slice();e.splice(t,1,r),i.length&&!/^,$/.test(i[i.length-1])&&i.push(","),i.push(e.join(""))})))})),e||(n.push(r),i.push(r))})),n.length&&"ltr"===o?(l&&r.cloneBefore({}),void(r.value=n.join(""))):i.length&&"rtl"===o?(l&&r.cloneBefore({}),void(r.value=i.join(""))):n.join("")!==i.join("")?(t(r,"ltr").append(r.cloneBefore({value:n.join("")})),t(r,"rtl").append(r.cloneBefore({value:i.join("")})),void function(r,e){e||r.remove()}(r,l)):void 0};const X={"block-size":{ltr:["height"],rtl:["height"]},"inline-size":{ltr:["width"],rtl:["width"]},"margin-block-end":{ltr:["margin-bottom"],rtl:["margin-bottom"]},"margin-block-start":{ltr:["margin-top"],rtl:["margin-top"]},"margin-block":{ltr:["margin-top","margin-bottom"],rtl:["margin-top","margin-bottom"]},"margin-inline-end":{ltr:["margin-right"],rtl:["margin-left"]},"margin-inline-start":{ltr:["margin-left"],rtl:["margin-right"]},"margin-inline":{ltr:["margin-left","margin-right"],rtl:["margin-left","margin-right"]},"inset-block-end":{ltr:["bottom"],rtl:["bottom"]},"inset-block-start":{ltr:["top"],rtl:["top"]},"inset-block":{ltr:["top","bottom"],rtl:["top","bottom"]},"inset-inline-end":{ltr:["right"],rtl:["left"]},"inset-inline-start":{ltr:["left"],rtl:["right"]},"inset-inline":{ltr:["left","right"],rtl:["left","right"]},inset:{ltr:["top","right","bottom","left"],rtl:["top","right","bottom","left"]},"padding-block-end":{ltr:["padding-bottom"],rtl:["padding-bottom"]},"padding-block-start":{ltr:["padding-top"],rtl:["padding-top"]},"padding-block":{ltr:["padding-top","padding-bottom"],rtl:["padding-top","padding-bottom"]},"padding-inline-end":{ltr:["padding-right"],rtl:["padding-left"]},"padding-inline-start":{ltr:["padding-left"],rtl:["padding-right"]},"padding-inline":{ltr:["padding-left","padding-right"],rtl:["padding-left","padding-right"]},"border-block-color":{ltr:["border-top-color","border-bottom-color"],rtl:["border-top-color","border-bottom-color"]},"border-block-end-color":{ltr:["border-bottom-color"],rtl:["border-bottom-color"]},"border-block-end-style":{ltr:["border-bottom-style"],rtl:["border-bottom-style"]},"border-block-end-width":{ltr:["border-bottom-width"],rtl:["border-bottom-width"]},"border-block-end":{ltr:["border-bottom"],rtl:["border-bottom"]},"border-block-start-color":{ltr:["border-top-color"],rtl:["border-top-color"]},"border-block-start-style":{ltr:["border-top-style"],rtl:["border-top-style"]},"border-block-start-width":{ltr:["border-top-width"],rtl:["border-top-width"]},"border-block-start":{ltr:["border-top"],rtl:["border-top"]},"border-block-style":{ltr:["border-top-style","border-bottom-style"],rtl:["border-top-style","border-bottom-style"]},"border-block-width":{ltr:["border-top-width","border-bottom-width"],rtl:["border-top-width","border-bottom-width"]},"border-block":{ltr:["border-top","border-bottom"],rtl:["border-top","border-bottom"]},"border-inline-color":{ltr:["border-left-color","border-right-color"],rtl:["border-left-color","border-right-color"]},"border-inline-end-color":{ltr:["border-right-color"],rtl:["border-left-color"]},"border-inline-end-style":{ltr:["border-right-style"],rtl:["border-left-style"]},"border-inline-end-width":{ltr:["border-right-width"],rtl:["border-left-width"]},"border-inline-end":{ltr:["border-right"],rtl:["border-left"]},"border-inline-start-color":{ltr:["border-left-color"],rtl:["border-right-color"]},"border-inline-start-style":{ltr:["border-left-style"],rtl:["border-right-style"]},"border-inline-start-width":{ltr:["border-left-width"],rtl:["border-right-width"]},"border-inline-start":{ltr:["border-left"],rtl:["border-right"]},"border-inline-style":{ltr:["border-left-style","border-right-style"],rtl:["border-left-style","border-right-style"]},"border-inline-width":{ltr:["border-left-width","border-right-width"],rtl:["border-left-width","border-right-width"]},"border-inline":{ltr:["border-left","border-right"],rtl:["border-left","border-right"]},"border-end-end-radius":{ltr:["border-bottom-right-radius"],rtl:["border-bottom-left-radius"]},"border-end-start-radius":{ltr:["border-bottom-left-radius"],rtl:["border-bottom-right-radius"]},"border-start-end-radius":{ltr:["border-top-right-radius"],rtl:["border-top-left-radius"]},"border-start-start-radius":{ltr:["border-top-left-radius"],rtl:["border-top-right-radius"]}};function Y(r){let e=r.parent;for(;e;)if("atrule"===e.type){if("keyframes"===e.name)return!0;e=e.parent}else e=e.parent;return!1}function Z(r){r=Object(r);const e=Boolean(r.preserve),t=!e&&"string"==typeof r.dir&&(/^rtl$/i.test(r.dir)?"rtl":"ltr"),o=r=>o=>{if(Y(o))return;const l=o.parent,n=U(o.value,!0);r(o,n,t,e),l.nodes.length||l.remove()},b=r=>o=>{if(Y(o))return;const l=o.parent,n=[o.value];r(o,n,t,e),l.nodes.length||l.remove()};return{postcssPlugin:"postcss-logical-properties",Declaration:{clear:o(B),float:o(B),resize:o(q),"text-align":o(Q),"block-size":o(N),"max-block-size":o(N),"min-block-size":o(N),"inline-size":o(N),"max-inline-size":o(N),"min-inline-size":o(N),margin:o(k),"margin-inline":o(I),"margin-inline-end":o(K),"margin-inline-start":o(J),"margin-block":o(F),"margin-block-end":o(H),"margin-block-start":o(G),inset:o(z),"inset-inline":o(I),"inset-inline-end":o(K),"inset-inline-start":o(J),"inset-block":o(F),"inset-block-end":o(H),"inset-block-start":o(G),padding:o(k),"padding-inline":o(I),"padding-inline-end":o(K),"padding-inline-start":o(J),"padding-block":o(F),"padding-block-end":o(H),"padding-block-start":o(G),"border-block":b(l),"border-block-color":o(l),"border-block-style":o(l),"border-block-width":o(l),"border-block-end":b(i),"border-block-end-color":o(i),"border-block-end-style":o(i),"border-block-end-width":o(i),"border-block-start":b(n),"border-block-start-color":o(n),"border-block-start-style":o(n),"border-block-start-width":o(n),"border-inline":b(d),"border-inline-color":o(d),"border-inline-style":o(d),"border-inline-width":o(d),"border-inline-end":b(a),"border-inline-end-color":o(a),"border-inline-end-style":o(a),"border-inline-end-width":o(a),"border-inline-start":b(p),"border-inline-start-color":o(p),"border-inline-start-style":o(p),"border-inline-start-width":o(p),"border-end-end-radius":o(f),"border-end-start-radius":o(f),"border-start-end-radius":o(f),"border-start-start-radius":o(f),"border-color":o(k),"border-style":o(k),"border-width":o(k),transition:o(W),"transition-property":o(W)}}}Z.postcss=!0,module.exports=Z;
