"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = isContentEditable;
var _jsxAstUtils = require("jsx-ast-utils");
function isContentEditable(tagName, attributes) {
  var _prop$value;
  var prop = (0, _jsxAstUtils.getProp)(attributes, 'contentEditable');
  return (prop === null || prop === void 0 ? void 0 : (_prop$value = prop.value) === null || _prop$value === void 0 ? void 0 : _prop$value.raw) === '"true"';
}
module.exports = exports.default;