{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Register=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-6\",children:\"Choose Registration Type\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col space-y-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/register/student\",className:\"bg-blue-500 text-white py-3 px-4 rounded hover:bg-blue-600\",children:\"Register as Student\"}),/*#__PURE__*/_jsx(<PERSON>,{to:\"/register/college\",className:\"bg-green-500 text-white py-3 px-4 rounded hover:bg-green-600\",children:\"Register as College\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 text-gray-600\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"text-blue-500 hover:underline\",children:\"Login here\"})]})]})]});};export default Register;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Register", "className", "children", "to"], "sources": ["C:/Users/<USER>/workuuu/frontend/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Register: React.FC = () => {\n  return (\n    <div className=\"max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center\">\n      <h2 className=\"text-2xl font-bold mb-6\">Choose Registration Type</h2>\n      \n      <div className=\"flex flex-col space-y-4\">\n        <Link \n          to=\"/register/student\" \n          className=\"bg-blue-500 text-white py-3 px-4 rounded hover:bg-blue-600\"\n        >\n          Register as Student\n        </Link>\n        \n        <Link \n          to=\"/register/college\" \n          className=\"bg-green-500 text-white py-3 px-4 rounded hover:bg-green-600\"\n        >\n          Register as College\n        </Link>\n        \n        <div className=\"mt-4 text-gray-600\">\n          Already have an account? <Link to=\"/login\" className=\"text-blue-500 hover:underline\">Login here</Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,mBACED,KAAA,QAAKE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,eACnFL,IAAA,OAAII,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAErEH,KAAA,QAAKE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCL,IAAA,CAACF,IAAI,EACHQ,EAAE,CAAC,mBAAmB,CACtBF,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CACvE,qBAED,CAAM,CAAC,cAEPL,IAAA,CAACF,IAAI,EACHQ,EAAE,CAAC,mBAAmB,CACtBF,SAAS,CAAC,8DAA8D,CAAAC,QAAA,CACzE,qBAED,CAAM,CAAC,cAEPH,KAAA,QAAKE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,2BACT,cAAAL,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACnG,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}