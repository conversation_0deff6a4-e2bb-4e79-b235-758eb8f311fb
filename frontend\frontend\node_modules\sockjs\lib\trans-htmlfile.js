// Generated by CoffeeScript 1.12.7
(function() {
  var HtmlFileReceiver, iframe_template, transport, utils,
    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    hasProp = {}.hasOwnProperty;

  utils = require('./utils');

  transport = require('./transport');

  iframe_template = "<!doctype html>\n<html><head>\n  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n</head><body><h2>Don't panic!</h2>\n  <script>\n    document.domain = document.domain;\n    var c = parent.{{ callback }};\n    c.start();\n    function p(d) {c.message(d);};\n    window.onload = function() {c.stop();};\n  </script>";

  iframe_template += Array(1024 - iframe_template.length + 14).join(' ');

  iframe_template += '\r\n\r\n';

  HtmlFileReceiver = (function(superClass) {
    extend(HtmlFileReceiver, superClass);

    function HtmlFileReceiver() {
      return HtmlFileReceiver.__super__.constructor.apply(this, arguments);
    }

    HtmlFileReceiver.prototype.protocol = "htmlfile";

    HtmlFileReceiver.prototype.doSendFrame = function(payload) {
      return HtmlFileReceiver.__super__.doSendFrame.call(this, '<script>\np(' + JSON.stringify(payload) + ');\n</script>\r\n');
    };

    return HtmlFileReceiver;

  })(transport.ResponseReceiver);

  exports.app = {
    htmlfile: function(req, res) {
      var callback;
      if (!('c' in req.query || 'callback' in req.query)) {
        throw {
          status: 500,
          message: '"callback" parameter required'
        };
      }
      callback = 'c' in req.query ? req.query['c'] : req.query['callback'];
      if (/[^a-zA-Z0-9-_.]/.test(callback)) {
        throw {
          status: 500,
          message: 'invalid "callback" parameter'
        };
      }
      res.setHeader('Content-Type', 'text/html; charset=UTF-8');
      res.writeHead(200);
      res.write(iframe_template.replace(/{{ callback }}/g, callback));
      transport.register(req, this, new HtmlFileReceiver(req, res, this.options));
      return true;
    }
  };

}).call(this);
