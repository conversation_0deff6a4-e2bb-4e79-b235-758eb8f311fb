import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import College from '../models/College';
import Student from '../models/Student';

// Register a new college
export const registerCollege = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, email, password, address } = req.body;
    
    // Check if college already exists
    const existingCollege = await College.findOne({ email });
    if (existingCollege) {
      return res.status(400).json({
        success: false,
        message: 'College with this email already exists'
      });
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Get file paths from multer middleware
    const verificationDocuments = req.files ? 
      (req.files as Express.Multer.File[]).map(file => file.path) : 
      [];
    
    // Create new college
    const college = new College({
      name,
      email,
      password: hashedPassword,
      address,
      verificationDocuments,
      isVerified: false
    });
    
    await college.save();
    
    res.status(201).json({
      success: true,
      message: 'College registered successfully. Awaiting verification.'
    });
  } catch (error) {
    next(error);
  }
};

// Register a new student
export const registerStudent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, email, password, age, collegeName, class: className, adharId, studentCardId } = req.body;
    
    // Check if student already exists
    const existingStudent = await Student.findOne({ email });
    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: 'Student with this email already exists'
      });
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create new student
    const student = new Student({
      name,
      email,
      password: hashedPassword,
      age,
      collegeName,
      class: className,
      adharId,
      studentCardId,
      preferences: {
        eventTypes: [],
        maxDistance: 50,
        preferredColleges: []
      }
    });
    
    await student.save();
    
    res.status(201).json({
      success: true,
      message: 'Student registered successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Login user (college or student)
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password, userType } = req.body;
    
    let user;
    
    // Find user based on type
    if (userType === 'college') {
      user = await College.findOne({ email });
      
      // Check if college is verified
      if (user && !user.isVerified) {
        return res.status(403).json({
          success: false,
          message: 'Your college account is pending verification'
        });
      }
    } else {
      user = await Student.findOne({ email });
    }
    
    // Check if user exists
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { id: user._id, userType },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '1d' }
    );
    
    // Remove password from response
    const userResponse = {
      id: user._id,
      name: user.name,
      email: user.email,
      userType
    };
    
    res.json({
      success: true,
      token,
      user: userResponse
    });
  } catch (error) {
    next(error);
  }
};

// Get all unverified colleges (for admin)
export const getUnverifiedColleges = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const colleges = await College.find({ isVerified: false }).select('-password');

    res.json({
      success: true,
      colleges
    });
  } catch (error) {
    next(error);
  }
};

// Verify college account
export const verifyCollege = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const college = await College.findByIdAndUpdate(
      id,
      { isVerified: true },
      { new: true }
    );

    if (!college) {
      return res.status(404).json({
        success: false,
        message: 'College not found'
      });
    }

    res.json({
      success: true,
      message: 'College verified successfully',
      college: {
        id: college._id,
        name: college.name,
        email: college.email,
        isVerified: college.isVerified
      }
    });
  } catch (error) {
    next(error);
  }
};

// Verify college by email (simpler for testing)
export const verifyCollegeByEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;

    const college = await College.findOneAndUpdate(
      { email },
      { isVerified: true },
      { new: true }
    );

    if (!college) {
      return res.status(404).json({
        success: false,
        message: 'College not found'
      });
    }

    res.json({
      success: true,
      message: 'College verified successfully',
      college: {
        id: college._id,
        name: college.name,
        email: college.email,
        isVerified: college.isVerified
      }
    });
  } catch (error) {
    next(error);
  }
};


