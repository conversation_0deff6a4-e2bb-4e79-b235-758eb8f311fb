import express from 'express';
import { 
  registerCollege, 
  registerStudent, 
  login, 
  verifyCollege 
} from '../controllers/auth.controller';
import multer from 'multer';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });

const router = express.Router();

// Auth routes
router.post('/register/college', upload.array('documents'), registerCollege);
router.post('/register/student', registerStudent);
router.post('/login', login);
router.put('/verify/:id', verifyCollege);

export default router;



