declare const isModuleResolutionError: (ex: unknown) => boolean;
declare let eslintFolder: string | undefined;
export declare const eslintPackageVersion: string;
declare const ESLINT_MAJOR_VERSION: number;
declare let configArrayFactory: any;
declare let ModuleResolver: {
    resolve: any;
};
declare let Naming: {
    normalizePackageName: any;
};
export { eslintFolder, configArrayFactory, ModuleResolver, Naming, ESLINT_MAJOR_VERSION, isModuleResolutionError };
//# sourceMappingURL=_patch-base.d.ts.map